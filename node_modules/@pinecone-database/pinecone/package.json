{"name": "@pinecone-database/pinecone", "version": "2.2.2", "main": "dist/index.js", "repository": {"type": "git", "url": "https://github.com/pinecone-io/pinecone-ts-client"}, "contributors": ["<PERSON> (https://github.com/jhamon)", "<PERSON><PERSON><PERSON> (https://github.com/rschwabco)", "<PERSON> (https://github.com/austin-denoble)"], "license": "Apache-2.0", "scripts": {"build": "rm -rf dist/ && tsc", "version": "jq --null-input --arg version $npm_package_version '{\"name\": \"@pinecone-database/pinecone\", \"version\": $version}' > src/version.json", "docs:build": "typedoc --plugin ./assets/docs-theme.mjs", "format": "prettier --write .", "lint": "eslint src/ --ext .ts", "repl": "npm run build && node utils/replInit.ts", "test:integration:node": "TEST_ENV=node jest src/integration/ -c jest.config.integration-node.js --runInBand --bail", "test:integration:edge": "TEST_ENV=edge jest src/integration/ -c jest.config.integration-edge.js --runInBand --bail", "test:integration:cleanup": "npm run build && node utils/cleanupResources.ts", "test:unit": "jest src/"}, "engines": {"node": ">=14.0.0"}, "devDependencies": {"@edge-runtime/jest-environment": "^2.3.3", "@edge-runtime/types": "^2.2.3", "@types/jest": "^29.5.0", "@types/node": "^18.11.17", "@typescript-eslint/eslint-plugin": "^5.59.11", "@typescript-eslint/parser": "^5.59.11", "dotenv": "^16.0.3", "eslint": "^8.42.0", "eslint-plugin-import": "^2.27.5", "jest": "^29.5.0", "jest-progress-bar-reporter": "^1.0.25", "jest-skipped-reporter": "^0.0.5", "prettier": "^2.8.8", "ts-jest": "^29.0.5", "typedoc": "^0.24.8", "typescript": "^4.9.4"}, "types": "dist/index.d.ts", "files": ["/dist"], "dependencies": {"@sinclair/typebox": "^0.29.0", "ajv": "^8.12.0", "cross-fetch": "^3.1.5", "encoding": "^0.1.13"}}