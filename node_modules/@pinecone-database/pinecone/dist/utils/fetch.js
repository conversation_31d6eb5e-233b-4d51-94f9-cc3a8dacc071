"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getFetch = void 0;
var cross_fetch_1 = __importDefault(require("cross-fetch"));
var getFetch = function (config) {
    if (config.fetchApi) {
        // User-provided fetch implementation, if any, takes precedence.
        return config.fetchApi;
    }
    else if (global.fetch) {
        // If a fetch implementation is already present in the global
        // scope, use that. This should prevent confusing failures in
        // nextjs projects where @vercel/fetch is mandated and
        // other implementations are stubbed out.
        return global.fetch;
    }
    else {
        // Use ponyfill as last resort
        return cross_fetch_1.default;
    }
};
exports.getFetch = getFetch;
//# sourceMappingURL=fetch.js.map