{"version": 3, "file": "user-agent.test.js", "sourceRoot": "", "sources": ["../../../src/utils/__tests__/user-agent.test.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAAA,4CAA+C;AAC/C,gEAAoD;AAEpD,QAAQ,CAAC,YAAY,EAAE;IACrB,QAAQ,CAAC,gBAAgB,EAAE;QACzB,IAAI,CAAC,0DAA0D,EAAE;YAC/D,IAAI,CAAC,KAAK,CAAC,iBAAiB,EAAE,QAAQ,CAAC,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YAC9D,IAAM,MAAM,GAAG,EAAE,MAAM,EAAE,cAAc,EAAE,CAAC;YAC1C,IAAM,SAAS,GAAG,IAAA,2BAAc,EAAC,MAAM,CAAC,CAAC;YAEzC,MAAM,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,4DAA4D,EAAE;YACjE,IAAM,MAAM,GAAG;gBACb,MAAM,EAAE,cAAc;gBACtB,SAAS,EAAE,iBAAiB;aAC7B,CAAC;YAEF,IAAM,SAAS,GAAG,IAAA,2BAAc,EAAC,MAAM,CAAC,CAAC;YACzC,MAAM,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC,4BAA4B,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE;QAC7B,IAAI,CAAC,oCAAoC,EAAE;YACzC,IAAM,MAAM,GAAG;gBACb,MAAM,EAAE,cAAc;gBACtB,SAAS,EAAE,kBAAkB;aAC9B,CAAC;YACF,IAAI,SAAS,GAAG,IAAA,2BAAc,EAAC,MAAM,CAAC,CAAC;YACvC,MAAM,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAC;YAExD,MAAM,CAAC,SAAS,GAAG,eAAe,CAAC;YACnC,SAAS,GAAG,IAAA,2BAAc,EAAC,MAAM,CAAC,CAAC;YACnC,MAAM,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAC;YAExD,MAAM,CAAC,SAAS,GAAG,qCAAqC,CAAC;YACzD,SAAS,GAAG,IAAA,2BAAc,EAAC,MAAM,CAAC,CAAC;YACnC,MAAM,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC,8BAA8B,CAAC,CAAC;YAE5D,MAAM,CAAC,SAAS,GAAG,2CAA2C,CAAC;YAC/D,SAAS,GAAG,IAAA,2BAAc,EAAC,MAAM,CAAC,CAAC;YACnC,MAAM,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC,+BAA+B,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}