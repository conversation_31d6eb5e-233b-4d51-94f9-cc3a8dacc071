"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CollectionNameSchema = exports.MetadataConfigSchema = exports.CapacityModeSchema = exports.CloudSchema = exports.EnvironmentSchema = exports.RegionSchema = exports.DimensionSchema = exports.MetricSchema = exports.ShardsSchema = exports.PodsSchema = exports.ReplicasSchema = exports.PodTypeSchema = exports.IndexNameSchema = void 0;
var typebox_1 = require("@sinclair/typebox");
var nonemptyString = typebox_1.Type.String({ minLength: 1 });
var positiveInteger = typebox_1.Type.Integer({ minimum: 1 });
// If user passes the empty string for index name, the generated
// OpenAPI client will call /databases/ which is the list
// indexes endpoint. This returns 200 instead of 404, but obviously
// no descriptive information is returned for an index named empty
// string. To avoid this confusing case, we require lenth > 1.
exports.IndexNameSchema = nonemptyString;
exports.PodTypeSchema = nonemptyString;
exports.ReplicasSchema = positiveInteger;
exports.PodsSchema = positiveInteger;
exports.ShardsSchema = positiveInteger;
exports.MetricSchema = typebox_1.Type.Union([
    typebox_1.Type.Literal('cosine'),
    typebox_1.Type.Literal('euclidean'),
    typebox_1.Type.Literal('dotproduct'),
]);
exports.DimensionSchema = positiveInteger;
exports.RegionSchema = nonemptyString;
exports.EnvironmentSchema = nonemptyString;
exports.CloudSchema = typebox_1.Type.Union([
    typebox_1.Type.Literal('gcp'),
    typebox_1.Type.Literal('aws'),
    typebox_1.Type.Literal('azure'),
]);
exports.CapacityModeSchema = nonemptyString;
exports.MetadataConfigSchema = typebox_1.Type.Object({
    indexed: typebox_1.Type.Array(nonemptyString),
}, { additionalProperties: false });
// If user passes the empty string for collection name, the generated
// OpenAPI client will call /collections/ which is the list
// collection endpoint. This returns 200 instead of 404, but obviously
// no descriptive information is returned for an collection named empty
// string. To avoid this confusing case, we require lenth > 1.
exports.CollectionNameSchema = nonemptyString;
//# sourceMappingURL=types.js.map