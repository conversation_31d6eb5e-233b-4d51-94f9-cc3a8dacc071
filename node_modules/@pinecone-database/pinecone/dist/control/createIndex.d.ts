import { CreateIndexRequest, IndexModel, ManageIndexesApi, CreateIndexRequestMetricEnum } from '../pinecone-generated-ts-fetch';
/**
 * @see [Understanding indexes](https://docs.pinecone.io/docs/indexes)
 */
export interface CreateIndexOptions extends Omit<CreateIndexRequest, 'metric'> {
    /** The distance metric to be used for similarity search. You can use 'euclidean', 'cosine', or 'dotproduct'. Defaults to 'cosine'. */
    metric?: CreateIndexRequestMetricEnum;
    /** This option tells the client not to resolve the returned promise until the index is ready to receive data. */
    waitUntilReady?: boolean;
    /** This option tells the client not to throw if you attempt to create an index that already exists. */
    suppressConflicts?: boolean;
}
export declare const createIndex: (api: ManageIndexesApi) => (options: CreateIndexOptions) => Promise<IndexModel | void>;
