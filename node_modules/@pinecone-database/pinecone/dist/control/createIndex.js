"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createIndex = void 0;
var validator_1 = require("../validator");
var utils_1 = require("../utils");
var errors_1 = require("../errors");
var typebox_1 = require("@sinclair/typebox");
var types_1 = require("./types");
var CreateIndexOptionsSchema = typebox_1.Type.Object({
    name: types_1.IndexNameSchema,
    dimension: types_1.DimensionSchema,
    metric: types_1.MetricSchema,
    spec: typebox_1.Type.Object({
        serverless: typebox_1.Type.Optional(typebox_1.Type.Object({
            cloud: types_1.CloudSchema,
            region: types_1.RegionSchema,
        })),
        pod: typebox_1.Type.Optional(typebox_1.Type.Object({
            environment: types_1.EnvironmentSchema,
            replicas: typebox_1.Type.Optional(types_1.ReplicasSchema),
            shards: typebox_1.Type.Optional(types_1.ShardsSchema),
            podType: typebox_1.Type.Optional(types_1.PodTypeSchema),
            pods: typebox_1.Type.Optional(types_1.PodsSchema),
            metadataConfig: typebox_1.Type.Optional(types_1.MetadataConfigSchema),
            sourceCollection: typebox_1.Type.Optional(types_1.CollectionNameSchema),
        })),
    }),
    waitUntilReady: typebox_1.Type.Optional(typebox_1.Type.Boolean()),
    suppressConflicts: typebox_1.Type.Optional(typebox_1.Type.Boolean()),
}, { additionalProperties: false });
var createIndex = function (api) {
    var validator = (0, validator_1.buildConfigValidator)(CreateIndexOptionsSchema, 'createIndex');
    return function (options) { return __awaiter(void 0, void 0, void 0, function () {
        var createResponse, e_1;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    // If metric is not specified, default to cosine
                    if (options && !options.metric) {
                        options.metric = 'cosine';
                    }
                    validator(options);
                    _a.label = 1;
                case 1:
                    _a.trys.push([1, 5, , 6]);
                    return [4 /*yield*/, api.createIndex({
                            createIndexRequest: options,
                        })];
                case 2:
                    createResponse = _a.sent();
                    if (!options.waitUntilReady) return [3 /*break*/, 4];
                    return [4 /*yield*/, waitUntilIndexIsReady(api, options.name)];
                case 3: return [2 /*return*/, _a.sent()];
                case 4: return [2 /*return*/, createResponse];
                case 5:
                    e_1 = _a.sent();
                    if (!(options.suppressConflicts &&
                        e_1 instanceof Error &&
                        e_1.name === 'PineconeConflictError')) {
                        throw e_1;
                    }
                    return [3 /*break*/, 6];
                case 6: return [2 /*return*/];
            }
        });
    }); };
};
exports.createIndex = createIndex;
var waitUntilIndexIsReady = function (api, indexName, seconds) {
    if (seconds === void 0) { seconds = 0; }
    return __awaiter(void 0, void 0, void 0, function () {
        var indexDescription, e_2, err;
        var _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    _b.trys.push([0, 6, , 8]);
                    return [4 /*yield*/, api.describeIndex({ indexName: indexName })];
                case 1:
                    indexDescription = _b.sent();
                    if (!!((_a = indexDescription.status) === null || _a === void 0 ? void 0 : _a.ready)) return [3 /*break*/, 4];
                    return [4 /*yield*/, new Promise(function (r) { return setTimeout(r, 1000); })];
                case 2:
                    _b.sent();
                    return [4 /*yield*/, waitUntilIndexIsReady(api, indexName, seconds + 1)];
                case 3: return [2 /*return*/, _b.sent()];
                case 4:
                    (0, utils_1.debugLog)("Index ".concat(indexName, " is ready after ").concat(seconds));
                    return [2 /*return*/, indexDescription];
                case 5: return [3 /*break*/, 8];
                case 6:
                    e_2 = _b.sent();
                    return [4 /*yield*/, (0, errors_1.handleApiError)(e_2, function (_, rawMessageText) { return __awaiter(void 0, void 0, void 0, function () { return __generator(this, function (_a) {
                            return [2 /*return*/, "Error creating index ".concat(indexName, ": ").concat(rawMessageText)];
                        }); }); })];
                case 7:
                    err = _b.sent();
                    throw err;
                case 8: return [2 /*return*/];
            }
        });
    });
};
//# sourceMappingURL=createIndex.js.map