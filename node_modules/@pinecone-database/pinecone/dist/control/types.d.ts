export declare const IndexNameSchema: import("@sinclair/typebox").TString;
export declare const PodTypeSchema: import("@sinclair/typebox").TString;
export declare const ReplicasSchema: import("@sinclair/typebox").TInteger;
export declare const PodsSchema: import("@sinclair/typebox").TInteger;
export declare const ShardsSchema: import("@sinclair/typebox").TInteger;
export declare const MetricSchema: import("@sinclair/typebox").TUnion<[import("@sinclair/typebox").TLiteral<"cosine">, import("@sinclair/typebox").TLiteral<"euclidean">, import("@sinclair/typebox").TLiteral<"dotproduct">]>;
export declare const DimensionSchema: import("@sinclair/typebox").TInteger;
export declare const RegionSchema: import("@sinclair/typebox").TString;
export declare const EnvironmentSchema: import("@sinclair/typebox").TString;
export declare const CloudSchema: import("@sinclair/typebox").TUnion<[import("@sinclair/typebox").TLiteral<"gcp">, import("@sinclair/typebox").TLiteral<"aws">, import("@sinclair/typebox").TLiteral<"azure">]>;
export declare const CapacityModeSchema: import("@sinclair/typebox").TString;
export declare const MetadataConfigSchema: import("@sinclair/typebox").TObject<{
    indexed: import("@sinclair/typebox").TArray<import("@sinclair/typebox").TString>;
}>;
export declare const CollectionNameSchema: import("@sinclair/typebox").TString;
/**
 * Index names are strings composed of:
 * - alphanumeric characters
 * - hyphens
 *
 * Index names must be unique within a project and may not start or end with a hyphen.
 *
 * @see [Understanding indexes](https://docs.pinecone.io/docs/indexes)
 */
export type IndexName = string;
/**
 * Collection names are strings composed of:
 * - alphanumeric characters
 * - hyphens
 *
 * Collection names must be unique within a project and may not start or end with a hyphen.
 *
 * @see [Understanding collections](https://docs.pinecone.io/docs/collections)
 */
export type CollectionName = string;
/**
 * @see [Understanding indexes](https://docs.pinecone.io/docs/indexes)
 */
export type PodType = 's1.x1' | 's1.x2' | 's1.x4' | 's1.x8' | 'p1.x1' | 'p1.x2' | 'p1.x4' | 'p1.x8' | 'p2.x1' | 'p2.x2' | 'p2.x4' | 'p2.x8' | string;
