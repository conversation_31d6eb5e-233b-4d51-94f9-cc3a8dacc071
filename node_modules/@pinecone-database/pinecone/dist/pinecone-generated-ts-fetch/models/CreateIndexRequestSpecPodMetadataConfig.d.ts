/**
 * Pinecone Control Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: v1
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
/**
 * Configuration for the behavior of Pinecone's internal metadata index. By default, all metadata is indexed; when `metadata_config` is present, only specified metadata fields are indexed. These configurations are only valid for use with pod-based indexes.
 * @export
 * @interface CreateIndexRequestSpecPodMetadataConfig
 */
export interface CreateIndexRequestSpecPodMetadataConfig {
    /**
     * By default, all metadata is indexed; to change this behavior, use this property to specify an array of metadata fields which should be indexed.
     * @type {Array<string>}
     * @memberof CreateIndexRequestSpecPodMetadataConfig
     */
    indexed?: Array<string>;
}
/**
 * Check if a given object implements the CreateIndexRequestSpecPodMetadataConfig interface.
 */
export declare function instanceOfCreateIndexRequestSpecPodMetadataConfig(value: object): boolean;
export declare function CreateIndexRequestSpecPodMetadataConfigFromJSON(json: any): CreateIndexRequestSpecPodMetadataConfig;
export declare function CreateIndexRequestSpecPodMetadataConfigFromJSONTyped(json: any, ignoreDiscriminator: boolean): CreateIndexRequestSpecPodMetadataConfig;
export declare function CreateIndexRequestSpecPodMetadataConfigToJSON(value?: CreateIndexRequestSpecPodMetadataConfig | null): any;
