/**
 * Pinecone Control Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: v1
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import type { SparseValues } from './SparseValues';
/**
 * A single query vector within a `QueryRequest`.
 * @export
 * @interface QueryVector
 */
export interface QueryVector {
    /**
     * The query vector values. This should be the same length as the dimension of the index being queried.
     * @type {Array<number>}
     * @memberof QueryVector
     */
    values: Array<number>;
    /**
     *
     * @type {SparseValues}
     * @memberof QueryVector
     */
    sparseValues?: SparseValues;
    /**
     * An override for the number of results to return for this query vector.
     * @type {number}
     * @memberof QueryVector
     */
    topK?: number;
    /**
     * An override the namespace to search.
     * @type {string}
     * @memberof QueryVector
     */
    namespace?: string;
    /**
     * An override for the metadata filter to apply. This replaces the request-level filter.
     * @type {object}
     * @memberof QueryVector
     */
    filter?: object;
}
/**
 * Check if a given object implements the QueryVector interface.
 */
export declare function instanceOfQueryVector(value: object): boolean;
export declare function QueryVectorFromJSON(json: any): QueryVector;
export declare function QueryVectorFromJSONTyped(json: any, ignoreDiscriminator: boolean): QueryVector;
export declare function QueryVectorToJSON(value?: QueryVector | null): any;
