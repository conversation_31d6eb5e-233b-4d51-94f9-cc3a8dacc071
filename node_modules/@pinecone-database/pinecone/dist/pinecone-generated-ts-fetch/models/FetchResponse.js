"use strict";
/* tslint:disable */
/* eslint-disable */
/**
 * Pinecone Control Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: v1
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.FetchResponseToJSON = exports.FetchResponseFromJSONTyped = exports.FetchResponseFromJSON = exports.instanceOfFetchResponse = void 0;
var runtime_1 = require("../runtime");
var Usage_1 = require("./Usage");
var Vector_1 = require("./Vector");
/**
 * Check if a given object implements the FetchResponse interface.
 */
function instanceOfFetchResponse(value) {
    var isInstance = true;
    return isInstance;
}
exports.instanceOfFetchResponse = instanceOfFetchResponse;
function FetchResponseFromJSON(json) {
    return FetchResponseFromJSONTyped(json, false);
}
exports.FetchResponseFromJSON = FetchResponseFromJSON;
function FetchResponseFromJSONTyped(json, ignoreDiscriminator) {
    if ((json === undefined) || (json === null)) {
        return json;
    }
    return {
        'vectors': !(0, runtime_1.exists)(json, 'vectors') ? undefined : ((0, runtime_1.mapValues)(json['vectors'], Vector_1.VectorFromJSON)),
        'namespace': !(0, runtime_1.exists)(json, 'namespace') ? undefined : json['namespace'],
        'usage': !(0, runtime_1.exists)(json, 'usage') ? undefined : (0, Usage_1.UsageFromJSON)(json['usage']),
    };
}
exports.FetchResponseFromJSONTyped = FetchResponseFromJSONTyped;
function FetchResponseToJSON(value) {
    if (value === undefined) {
        return undefined;
    }
    if (value === null) {
        return null;
    }
    return {
        'vectors': value.vectors === undefined ? undefined : ((0, runtime_1.mapValues)(value.vectors, Vector_1.VectorToJSON)),
        'namespace': value.namespace,
        'usage': (0, Usage_1.UsageToJSON)(value.usage),
    };
}
exports.FetchResponseToJSON = FetchResponseToJSON;
//# sourceMappingURL=FetchResponse.js.map