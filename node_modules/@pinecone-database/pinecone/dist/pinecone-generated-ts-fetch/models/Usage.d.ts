/**
 * Pinecone Control Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: v1
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
/**
 *
 * @export
 * @interface Usage
 */
export interface Usage {
    /**
     * The number of read units consumed by this operation.
     * @type {number}
     * @memberof Usage
     */
    readUnits?: number;
}
/**
 * Check if a given object implements the Usage interface.
 */
export declare function instanceOfUsage(value: object): boolean;
export declare function UsageFromJSON(json: any): Usage;
export declare function UsageFromJSONTyped(json: any, ignoreDiscriminator: boolean): Usage;
export declare function UsageToJSON(value?: Usage | null): any;
