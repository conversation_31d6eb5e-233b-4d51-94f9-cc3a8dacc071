"use strict";
/* tslint:disable */
/* eslint-disable */
/**
 * Pinecone Control Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: v1
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ServerlessSpecToJSON = exports.ServerlessSpecFromJSONTyped = exports.ServerlessSpecFromJSON = exports.instanceOfServerlessSpec = exports.ServerlessSpecCloudEnum = void 0;
/**
 * @export
 */
exports.ServerlessSpecCloudEnum = {
    Gcp: 'gcp',
    Aws: 'aws',
    Azure: 'azure'
};
/**
 * Check if a given object implements the ServerlessSpec interface.
 */
function instanceOfServerlessSpec(value) {
    var isInstance = true;
    isInstance = isInstance && "cloud" in value;
    isInstance = isInstance && "region" in value;
    return isInstance;
}
exports.instanceOfServerlessSpec = instanceOfServerlessSpec;
function ServerlessSpecFromJSON(json) {
    return ServerlessSpecFromJSONTyped(json, false);
}
exports.ServerlessSpecFromJSON = ServerlessSpecFromJSON;
function ServerlessSpecFromJSONTyped(json, ignoreDiscriminator) {
    if ((json === undefined) || (json === null)) {
        return json;
    }
    return {
        'cloud': json['cloud'],
        'region': json['region'],
    };
}
exports.ServerlessSpecFromJSONTyped = ServerlessSpecFromJSONTyped;
function ServerlessSpecToJSON(value) {
    if (value === undefined) {
        return undefined;
    }
    if (value === null) {
        return null;
    }
    return {
        'cloud': value.cloud,
        'region': value.region,
    };
}
exports.ServerlessSpecToJSON = ServerlessSpecToJSON;
//# sourceMappingURL=ServerlessSpec.js.map