/**
 * Pinecone Control Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: v1
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
/**
 *
 * @export
 * @interface ProtobufAny
 */
export interface ProtobufAny {
    /**
     *
     * @type {string}
     * @memberof ProtobufAny
     */
    typeUrl?: string;
    /**
     *
     * @type {string}
     * @memberof ProtobufAny
     */
    value?: string;
}
/**
 * Check if a given object implements the ProtobufAny interface.
 */
export declare function instanceOfProtobufAny(value: object): boolean;
export declare function ProtobufAnyFromJSON(json: any): ProtobufAny;
export declare function ProtobufAnyFromJSONTyped(json: any, ignoreDiscriminator: boolean): ProtobufAny;
export declare function ProtobufAnyToJSON(value?: ProtobufAny | null): any;
