/**
 * Pinecone Control Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: v1
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import type { CreateIndexRequestSpecPod } from './CreateIndexRequestSpecPod';
import type { ServerlessSpec } from './ServerlessSpec';
/**
 * The spec object defines how the index should be deployed. For serverless indexes, you define only the cloud and region
 * where the index should be hosted. For pod-based indexes, you define the environment where the index should be hosted,
 * the pod type and size to use, and other index characteristics. For more information on creating indexes,
 * see [Understanding indexes](https://docs.pinecone.io/guides/indexes/understanding-indexes).
 *
 * @export
 * @interface CreateIndexRequestSpec
 */
export interface CreateIndexRequestSpec {
    /**
     *
     * @type {ServerlessSpec}
     * @memberof CreateIndexRequestSpec
     */
    serverless?: ServerlessSpec;
    /**
     *
     * @type {CreateIndexRequestSpecPod}
     * @memberof CreateIndexRequestSpec
     */
    pod?: CreateIndexRequestSpecPod;
}
/**
 * Check if a given object implements the CreateIndexRequestSpec interface.
 */
export declare function instanceOfCreateIndexRequestSpec(value: object): boolean;
export declare function CreateIndexRequestSpecFromJSON(json: any): CreateIndexRequestSpec;
export declare function CreateIndexRequestSpecFromJSONTyped(json: any, ignoreDiscriminator: boolean): CreateIndexRequestSpec;
export declare function CreateIndexRequestSpecToJSON(value?: CreateIndexRequestSpec | null): any;
