"use strict";
/* tslint:disable */
/* eslint-disable */
/**
 * Pinecone Control Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: v1
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaginationToJSON = exports.PaginationFromJSONTyped = exports.PaginationFromJSON = exports.instanceOfPagination = void 0;
var runtime_1 = require("../runtime");
/**
 * Check if a given object implements the Pagination interface.
 */
function instanceOfPagination(value) {
    var isInstance = true;
    return isInstance;
}
exports.instanceOfPagination = instanceOfPagination;
function PaginationFromJSON(json) {
    return PaginationFromJSONTyped(json, false);
}
exports.PaginationFromJSON = PaginationFromJSON;
function PaginationFromJSONTyped(json, ignoreDiscriminator) {
    if ((json === undefined) || (json === null)) {
        return json;
    }
    return {
        'next': !(0, runtime_1.exists)(json, 'next') ? undefined : json['next'],
    };
}
exports.PaginationFromJSONTyped = PaginationFromJSONTyped;
function PaginationToJSON(value) {
    if (value === undefined) {
        return undefined;
    }
    if (value === null) {
        return null;
    }
    return {
        'next': value.next,
    };
}
exports.PaginationToJSON = PaginationToJSON;
//# sourceMappingURL=Pagination.js.map