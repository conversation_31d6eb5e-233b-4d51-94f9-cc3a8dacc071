"use strict";
/* tslint:disable */
/* eslint-disable */
/**
 * Pinecone Control Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: v1
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateIndexRequestSpecPodMetadataConfigToJSON = exports.CreateIndexRequestSpecPodMetadataConfigFromJSONTyped = exports.CreateIndexRequestSpecPodMetadataConfigFromJSON = exports.instanceOfCreateIndexRequestSpecPodMetadataConfig = void 0;
var runtime_1 = require("../runtime");
/**
 * Check if a given object implements the CreateIndexRequestSpecPodMetadataConfig interface.
 */
function instanceOfCreateIndexRequestSpecPodMetadataConfig(value) {
    var isInstance = true;
    return isInstance;
}
exports.instanceOfCreateIndexRequestSpecPodMetadataConfig = instanceOfCreateIndexRequestSpecPodMetadataConfig;
function CreateIndexRequestSpecPodMetadataConfigFromJSON(json) {
    return CreateIndexRequestSpecPodMetadataConfigFromJSONTyped(json, false);
}
exports.CreateIndexRequestSpecPodMetadataConfigFromJSON = CreateIndexRequestSpecPodMetadataConfigFromJSON;
function CreateIndexRequestSpecPodMetadataConfigFromJSONTyped(json, ignoreDiscriminator) {
    if ((json === undefined) || (json === null)) {
        return json;
    }
    return {
        'indexed': !(0, runtime_1.exists)(json, 'indexed') ? undefined : json['indexed'],
    };
}
exports.CreateIndexRequestSpecPodMetadataConfigFromJSONTyped = CreateIndexRequestSpecPodMetadataConfigFromJSONTyped;
function CreateIndexRequestSpecPodMetadataConfigToJSON(value) {
    if (value === undefined) {
        return undefined;
    }
    if (value === null) {
        return null;
    }
    return {
        'indexed': value.indexed,
    };
}
exports.CreateIndexRequestSpecPodMetadataConfigToJSON = CreateIndexRequestSpecPodMetadataConfigToJSON;
//# sourceMappingURL=CreateIndexRequestSpecPodMetadataConfig.js.map