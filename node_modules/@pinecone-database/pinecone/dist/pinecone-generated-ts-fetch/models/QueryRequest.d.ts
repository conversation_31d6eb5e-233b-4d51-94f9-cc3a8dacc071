/**
 * Pinecone Control Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: v1
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import type { QueryVector } from './QueryVector';
import type { SparseValues } from './SparseValues';
/**
 * The request for the `query` operation.
 * @export
 * @interface QueryRequest
 */
export interface QueryRequest {
    /**
     * The namespace to query.
     * @type {string}
     * @memberof QueryRequest
     */
    namespace?: string;
    /**
     * The number of results to return for each query.
     * @type {number}
     * @memberof QueryRequest
     */
    topK: number;
    /**
     * The filter to apply. You can use vector metadata to limit your search. See https://www.pinecone.io/docs/metadata-filtering/.
     * @type {object}
     * @memberof QueryRequest
     */
    filter?: object;
    /**
     * Indicates whether vector values are included in the response.
     * @type {boolean}
     * @memberof QueryRequest
     */
    includeValues?: boolean;
    /**
     * Indicates whether metadata is included in the response as well as the ids.
     * @type {boolean}
     * @memberof QueryRequest
     */
    includeMetadata?: boolean;
    /**
     * DEPRECATED. The query vectors. Each `query()` request can contain only one of the parameters `queries`, `vector`, or  `id`.
     * @type {Array<QueryVector>}
     * @memberof QueryRequest
     * @deprecated
     */
    queries?: Array<QueryVector>;
    /**
     * The query vector. This should be the same length as the dimension of the index being queried. Each `query()` request can contain only one of the parameters `id` or `vector`.
     * @type {Array<number>}
     * @memberof QueryRequest
     */
    vector?: Array<number>;
    /**
     *
     * @type {SparseValues}
     * @memberof QueryRequest
     */
    sparseVector?: SparseValues;
    /**
     * The unique ID of the vector to be used as a query vector. Each `query()` request can contain only one of the parameters `queries`, `vector`, or  `id`.
     * @type {string}
     * @memberof QueryRequest
     */
    id?: string;
}
/**
 * Check if a given object implements the QueryRequest interface.
 */
export declare function instanceOfQueryRequest(value: object): boolean;
export declare function QueryRequestFromJSON(json: any): QueryRequest;
export declare function QueryRequestFromJSONTyped(json: any, ignoreDiscriminator: boolean): QueryRequest;
export declare function QueryRequestToJSON(value?: QueryRequest | null): any;
