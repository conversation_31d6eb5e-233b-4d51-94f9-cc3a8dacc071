/**
 * Pinecone Control Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: v1
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
/**
 *
 * @export
 * @interface IndexModelStatus
 */
export interface IndexModelStatus {
    /**
     *
     * @type {boolean}
     * @memberof IndexModelStatus
     */
    ready: boolean;
    /**
     *
     * @type {string}
     * @memberof IndexModelStatus
     */
    state: IndexModelStatusStateEnum;
}
/**
 * @export
 */
export declare const IndexModelStatusStateEnum: {
    readonly Initializing: "Initializing";
    readonly InitializationFailed: "InitializationFailed";
    readonly ScalingUp: "ScalingUp";
    readonly ScalingDown: "ScalingDown";
    readonly ScalingUpPodSize: "ScalingUpPodSize";
    readonly ScalingDownPodSize: "ScalingDownPodSize";
    readonly Terminating: "Terminating";
    readonly Ready: "Ready";
};
export type IndexModelStatusStateEnum = typeof IndexModelStatusStateEnum[keyof typeof IndexModelStatusStateEnum];
/**
 * Check if a given object implements the IndexModelStatus interface.
 */
export declare function instanceOfIndexModelStatus(value: object): boolean;
export declare function IndexModelStatusFromJSON(json: any): IndexModelStatus;
export declare function IndexModelStatusFromJSONTyped(json: any, ignoreDiscriminator: boolean): IndexModelStatus;
export declare function IndexModelStatusToJSON(value?: IndexModelStatus | null): any;
