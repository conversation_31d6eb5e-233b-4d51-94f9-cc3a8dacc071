/**
 * Pinecone Control Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: v1
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import type { CreateIndexRequestSpecPodMetadataConfig } from './CreateIndexRequestSpecPodMetadataConfig';
/**
 * Configuration needed to deploy a pod-based index.
 * @export
 * @interface CreateIndexRequestSpecPod
 */
export interface CreateIndexRequestSpecPod {
    /**
     * The environment where the index is hosted.
     * @type {string}
     * @memberof CreateIndexRequestSpecPod
     */
    environment: string;
    /**
     * The number of replicas. Replicas duplicate your index. They provide higher availability and throughput. Replicas can be scaled up or down as your needs change.
     * @type {number}
     * @memberof CreateIndexRequestSpecPod
     */
    replicas?: number;
    /**
     * The type of pod to use. One of `s1`, `p1`, or `p2` appended with `.` and one of `x1`, `x2`, `x4`, or `x8`.
     * @type {string}
     * @memberof CreateIndexRequestSpecPod
     */
    podType: string;
    /**
     * The number of pods to be used in the index. This should be equal to `shards` x `replicas`.
     * @type {number}
     * @memberof CreateIndexRequestSpecPod
     */
    pods?: number;
    /**
     * The number of shards. Shards split your data across multiple pods so you can fit more data into an index.
     * @type {number}
     * @memberof CreateIndexRequestSpecPod
     */
    shards?: number;
    /**
     *
     * @type {CreateIndexRequestSpecPodMetadataConfig}
     * @memberof CreateIndexRequestSpecPod
     */
    metadataConfig?: CreateIndexRequestSpecPodMetadataConfig;
    /**
     * The name of the collection to be used as the source for the index.
     * @type {string}
     * @memberof CreateIndexRequestSpecPod
     */
    sourceCollection?: string;
}
/**
 * Check if a given object implements the CreateIndexRequestSpecPod interface.
 */
export declare function instanceOfCreateIndexRequestSpecPod(value: object): boolean;
export declare function CreateIndexRequestSpecPodFromJSON(json: any): CreateIndexRequestSpecPod;
export declare function CreateIndexRequestSpecPodFromJSONTyped(json: any, ignoreDiscriminator: boolean): CreateIndexRequestSpecPod;
export declare function CreateIndexRequestSpecPodToJSON(value?: CreateIndexRequestSpecPod | null): any;
