/**
 * Pinecone Control Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: v1
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import type { IndexModel } from './IndexModel';
/**
 * The list of indexes that exist in the project.
 * @export
 * @interface IndexList
 */
export interface IndexList {
    /**
     *
     * @type {Array<IndexModel>}
     * @memberof IndexList
     */
    indexes?: Array<IndexModel>;
}
/**
 * Check if a given object implements the IndexList interface.
 */
export declare function instanceOfIndexList(value: object): boolean;
export declare function IndexListFromJSON(json: any): IndexList;
export declare function IndexListFromJSONTyped(json: any, ignoreDiscriminator: boolean): IndexList;
export declare function IndexListToJSON(value?: IndexList | null): any;
