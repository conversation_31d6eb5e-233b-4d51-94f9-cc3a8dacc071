/**
 * Pinecone Control Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: v1
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
/**
 *
 * @export
 * @interface SparseValues
 */
export interface SparseValues {
    /**
     *
     * @type {Array<number>}
     * @memberof SparseValues
     */
    indices: Array<number>;
    /**
     *
     * @type {Array<number>}
     * @memberof SparseValues
     */
    values: Array<number>;
}
/**
 * Check if a given object implements the SparseValues interface.
 */
export declare function instanceOfSparseValues(value: object): boolean;
export declare function SparseValuesFromJSON(json: any): SparseValues;
export declare function SparseValuesFromJSONTyped(json: any, ignoreDiscriminator: boolean): SparseValues;
export declare function SparseValuesToJSON(value?: SparseValues | null): any;
