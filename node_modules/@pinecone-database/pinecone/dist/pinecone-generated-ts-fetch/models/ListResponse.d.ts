/**
 * Pinecone Control Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: v1
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import type { ListItem } from './ListItem';
import type { Pagination } from './Pagination';
import type { Usage } from './Usage';
/**
 * The response for the `List` operation.
 * @export
 * @interface ListResponse
 */
export interface ListResponse {
    /**
     *
     * @type {Array<ListItem>}
     * @memberof ListResponse
     */
    vectors?: Array<ListItem>;
    /**
     *
     * @type {Pagination}
     * @memberof ListResponse
     */
    pagination?: Pagination;
    /**
     * The namespace of the vectors.
     * @type {string}
     * @memberof ListResponse
     */
    namespace?: string;
    /**
     *
     * @type {Usage}
     * @memberof ListResponse
     */
    usage?: Usage;
}
/**
 * Check if a given object implements the ListResponse interface.
 */
export declare function instanceOfListResponse(value: object): boolean;
export declare function ListResponseFromJSON(json: any): ListResponse;
export declare function ListResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): ListResponse;
export declare function ListResponseToJSON(value?: ListResponse | null): any;
