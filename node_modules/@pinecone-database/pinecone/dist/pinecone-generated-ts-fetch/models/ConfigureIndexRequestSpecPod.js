"use strict";
/* tslint:disable */
/* eslint-disable */
/**
 * Pinecone Control Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: v1
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConfigureIndexRequestSpecPodToJSON = exports.ConfigureIndexRequestSpecPodFromJSONTyped = exports.ConfigureIndexRequestSpecPodFromJSON = exports.instanceOfConfigureIndexRequestSpecPod = void 0;
var runtime_1 = require("../runtime");
/**
 * Check if a given object implements the ConfigureIndexRequestSpecPod interface.
 */
function instanceOfConfigureIndexRequestSpecPod(value) {
    var isInstance = true;
    return isInstance;
}
exports.instanceOfConfigureIndexRequestSpecPod = instanceOfConfigureIndexRequestSpecPod;
function ConfigureIndexRequestSpecPodFromJSON(json) {
    return ConfigureIndexRequestSpecPodFromJSONTyped(json, false);
}
exports.ConfigureIndexRequestSpecPodFromJSON = ConfigureIndexRequestSpecPodFromJSON;
function ConfigureIndexRequestSpecPodFromJSONTyped(json, ignoreDiscriminator) {
    if ((json === undefined) || (json === null)) {
        return json;
    }
    return {
        'replicas': !(0, runtime_1.exists)(json, 'replicas') ? undefined : json['replicas'],
        'podType': !(0, runtime_1.exists)(json, 'pod_type') ? undefined : json['pod_type'],
    };
}
exports.ConfigureIndexRequestSpecPodFromJSONTyped = ConfigureIndexRequestSpecPodFromJSONTyped;
function ConfigureIndexRequestSpecPodToJSON(value) {
    if (value === undefined) {
        return undefined;
    }
    if (value === null) {
        return null;
    }
    return {
        'replicas': value.replicas,
        'pod_type': value.podType,
    };
}
exports.ConfigureIndexRequestSpecPodToJSON = ConfigureIndexRequestSpecPodToJSON;
//# sourceMappingURL=ConfigureIndexRequestSpecPod.js.map