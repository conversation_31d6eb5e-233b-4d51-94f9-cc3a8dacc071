/**
 * Pinecone Control Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: v1
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import type { ConfigureIndexRequestSpec } from './ConfigureIndexRequestSpec';
/**
 * Configuration used to scale an index.
 * @export
 * @interface ConfigureIndexRequest
 */
export interface ConfigureIndexRequest {
    /**
     *
     * @type {ConfigureIndexRequestSpec}
     * @memberof ConfigureIndexRequest
     */
    spec: ConfigureIndexRequestSpec;
}
/**
 * Check if a given object implements the ConfigureIndexRequest interface.
 */
export declare function instanceOfConfigureIndexRequest(value: object): boolean;
export declare function ConfigureIndexRequestFromJSON(json: any): ConfigureIndexRequest;
export declare function ConfigureIndexRequestFromJSONTyped(json: any, ignoreDiscriminator: boolean): ConfigureIndexRequest;
export declare function ConfigureIndexRequestToJSON(value?: ConfigureIndexRequest | null): any;
