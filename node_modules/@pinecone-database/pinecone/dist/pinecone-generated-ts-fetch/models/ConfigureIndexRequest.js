"use strict";
/* tslint:disable */
/* eslint-disable */
/**
 * Pinecone Control Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: v1
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConfigureIndexRequestToJSON = exports.ConfigureIndexRequestFromJSONTyped = exports.ConfigureIndexRequestFromJSON = exports.instanceOfConfigureIndexRequest = void 0;
var ConfigureIndexRequestSpec_1 = require("./ConfigureIndexRequestSpec");
/**
 * Check if a given object implements the ConfigureIndexRequest interface.
 */
function instanceOfConfigureIndexRequest(value) {
    var isInstance = true;
    isInstance = isInstance && "spec" in value;
    return isInstance;
}
exports.instanceOfConfigureIndexRequest = instanceOfConfigureIndexRequest;
function ConfigureIndexRequestFromJSON(json) {
    return ConfigureIndexRequestFromJSONTyped(json, false);
}
exports.ConfigureIndexRequestFromJSON = ConfigureIndexRequestFromJSON;
function ConfigureIndexRequestFromJSONTyped(json, ignoreDiscriminator) {
    if ((json === undefined) || (json === null)) {
        return json;
    }
    return {
        'spec': (0, ConfigureIndexRequestSpec_1.ConfigureIndexRequestSpecFromJSON)(json['spec']),
    };
}
exports.ConfigureIndexRequestFromJSONTyped = ConfigureIndexRequestFromJSONTyped;
function ConfigureIndexRequestToJSON(value) {
    if (value === undefined) {
        return undefined;
    }
    if (value === null) {
        return null;
    }
    return {
        'spec': (0, ConfigureIndexRequestSpec_1.ConfigureIndexRequestSpecToJSON)(value.spec),
    };
}
exports.ConfigureIndexRequestToJSON = ConfigureIndexRequestToJSON;
//# sourceMappingURL=ConfigureIndexRequest.js.map