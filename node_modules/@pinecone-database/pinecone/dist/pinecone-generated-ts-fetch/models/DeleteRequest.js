"use strict";
/* tslint:disable */
/* eslint-disable */
/**
 * Pinecone Control Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: v1
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.DeleteRequestToJSON = exports.DeleteRequestFromJSONTyped = exports.DeleteRequestFromJSON = exports.instanceOfDeleteRequest = void 0;
var runtime_1 = require("../runtime");
/**
 * Check if a given object implements the DeleteRequest interface.
 */
function instanceOfDeleteRequest(value) {
    var isInstance = true;
    return isInstance;
}
exports.instanceOfDeleteRequest = instanceOfDeleteRequest;
function DeleteRequestFromJSON(json) {
    return DeleteRequestFromJSONTyped(json, false);
}
exports.DeleteRequestFromJSON = DeleteRequestFromJSON;
function DeleteRequestFromJSONTyped(json, ignoreDiscriminator) {
    if ((json === undefined) || (json === null)) {
        return json;
    }
    return {
        'ids': !(0, runtime_1.exists)(json, 'ids') ? undefined : json['ids'],
        'deleteAll': !(0, runtime_1.exists)(json, 'deleteAll') ? undefined : json['deleteAll'],
        'namespace': !(0, runtime_1.exists)(json, 'namespace') ? undefined : json['namespace'],
        'filter': !(0, runtime_1.exists)(json, 'filter') ? undefined : json['filter'],
    };
}
exports.DeleteRequestFromJSONTyped = DeleteRequestFromJSONTyped;
function DeleteRequestToJSON(value) {
    if (value === undefined) {
        return undefined;
    }
    if (value === null) {
        return null;
    }
    return {
        'ids': value.ids,
        'deleteAll': value.deleteAll,
        'namespace': value.namespace,
        'filter': value.filter,
    };
}
exports.DeleteRequestToJSON = DeleteRequestToJSON;
//# sourceMappingURL=DeleteRequest.js.map