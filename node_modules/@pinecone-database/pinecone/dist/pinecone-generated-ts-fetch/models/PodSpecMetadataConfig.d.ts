/**
 * Pinecone Control Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: v1
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
/**
 * Configuration for the behavior of Pinecone's internal metadata index. By default, all metadata is indexed; when `metadata_config` is present, only specified metadata fields are indexed. These configurations are only valid for use with pod-based indexes.
 * @export
 * @interface PodSpecMetadataConfig
 */
export interface PodSpecMetadataConfig {
    /**
     * By default, all metadata is indexed; to change this behavior, use this property to specify an array of metadata fields that should be indexed.
     * @type {Array<string>}
     * @memberof PodSpecMetadataConfig
     */
    indexed?: Array<string>;
}
/**
 * Check if a given object implements the PodSpecMetadataConfig interface.
 */
export declare function instanceOfPodSpecMetadataConfig(value: object): boolean;
export declare function PodSpecMetadataConfigFromJSON(json: any): PodSpecMetadataConfig;
export declare function PodSpecMetadataConfigFromJSONTyped(json: any, ignoreDiscriminator: boolean): PodSpecMetadataConfig;
export declare function PodSpecMetadataConfigToJSON(value?: PodSpecMetadataConfig | null): any;
