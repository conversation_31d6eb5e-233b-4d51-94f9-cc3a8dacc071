/**
 * Pinecone Control Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: v1
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import type { ScoredVector } from './ScoredVector';
/**
 *
 * @export
 * @interface SingleQueryResults
 */
export interface SingleQueryResults {
    /**
     * The matches for the vectors.
     * @type {Array<ScoredVector>}
     * @memberof SingleQueryResults
     */
    matches?: Array<ScoredVector>;
    /**
     * The namespace for the vectors.
     * @type {string}
     * @memberof SingleQueryResults
     */
    namespace?: string;
}
/**
 * Check if a given object implements the SingleQueryResults interface.
 */
export declare function instanceOfSingleQueryResults(value: object): boolean;
export declare function SingleQueryResultsFromJSON(json: any): SingleQueryResults;
export declare function SingleQueryResultsFromJSONTyped(json: any, ignoreDiscriminator: boolean): SingleQueryResults;
export declare function SingleQueryResultsToJSON(value?: SingleQueryResults | null): any;
