"use strict";
/* tslint:disable */
/* eslint-disable */
/**
 * Pinecone Control Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: v1
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ListItemToJSON = exports.ListItemFromJSONTyped = exports.ListItemFromJSON = exports.instanceOfListItem = void 0;
var runtime_1 = require("../runtime");
/**
 * Check if a given object implements the ListItem interface.
 */
function instanceOfListItem(value) {
    var isInstance = true;
    return isInstance;
}
exports.instanceOfListItem = instanceOfListItem;
function ListItemFromJSON(json) {
    return ListItemFromJSONTyped(json, false);
}
exports.ListItemFromJSON = ListItemFromJSON;
function ListItemFromJSONTyped(json, ignoreDiscriminator) {
    if ((json === undefined) || (json === null)) {
        return json;
    }
    return {
        'id': !(0, runtime_1.exists)(json, 'id') ? undefined : json['id'],
    };
}
exports.ListItemFromJSONTyped = ListItemFromJSONTyped;
function ListItemToJSON(value) {
    if (value === undefined) {
        return undefined;
    }
    if (value === null) {
        return null;
    }
    return {
        'id': value.id,
    };
}
exports.ListItemToJSON = ListItemToJSON;
//# sourceMappingURL=ListItem.js.map