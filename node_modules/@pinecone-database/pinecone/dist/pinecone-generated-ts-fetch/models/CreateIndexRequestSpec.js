"use strict";
/* tslint:disable */
/* eslint-disable */
/**
 * Pinecone Control Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: v1
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateIndexRequestSpecToJSON = exports.CreateIndexRequestSpecFromJSONTyped = exports.CreateIndexRequestSpecFromJSON = exports.instanceOfCreateIndexRequestSpec = void 0;
var runtime_1 = require("../runtime");
var CreateIndexRequestSpecPod_1 = require("./CreateIndexRequestSpecPod");
var ServerlessSpec_1 = require("./ServerlessSpec");
/**
 * Check if a given object implements the CreateIndexRequestSpec interface.
 */
function instanceOfCreateIndexRequestSpec(value) {
    var isInstance = true;
    return isInstance;
}
exports.instanceOfCreateIndexRequestSpec = instanceOfCreateIndexRequestSpec;
function CreateIndexRequestSpecFromJSON(json) {
    return CreateIndexRequestSpecFromJSONTyped(json, false);
}
exports.CreateIndexRequestSpecFromJSON = CreateIndexRequestSpecFromJSON;
function CreateIndexRequestSpecFromJSONTyped(json, ignoreDiscriminator) {
    if ((json === undefined) || (json === null)) {
        return json;
    }
    return {
        'serverless': !(0, runtime_1.exists)(json, 'serverless') ? undefined : (0, ServerlessSpec_1.ServerlessSpecFromJSON)(json['serverless']),
        'pod': !(0, runtime_1.exists)(json, 'pod') ? undefined : (0, CreateIndexRequestSpecPod_1.CreateIndexRequestSpecPodFromJSON)(json['pod']),
    };
}
exports.CreateIndexRequestSpecFromJSONTyped = CreateIndexRequestSpecFromJSONTyped;
function CreateIndexRequestSpecToJSON(value) {
    if (value === undefined) {
        return undefined;
    }
    if (value === null) {
        return null;
    }
    return {
        'serverless': (0, ServerlessSpec_1.ServerlessSpecToJSON)(value.serverless),
        'pod': (0, CreateIndexRequestSpecPod_1.CreateIndexRequestSpecPodToJSON)(value.pod),
    };
}
exports.CreateIndexRequestSpecToJSON = CreateIndexRequestSpecToJSON;
//# sourceMappingURL=CreateIndexRequestSpec.js.map