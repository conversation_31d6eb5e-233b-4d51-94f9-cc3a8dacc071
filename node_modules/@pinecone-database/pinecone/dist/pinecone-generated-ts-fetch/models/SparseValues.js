"use strict";
/* tslint:disable */
/* eslint-disable */
/**
 * Pinecone Control Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: v1
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.SparseValuesToJSON = exports.SparseValuesFromJSONTyped = exports.SparseValuesFromJSON = exports.instanceOfSparseValues = void 0;
/**
 * Check if a given object implements the SparseValues interface.
 */
function instanceOfSparseValues(value) {
    var isInstance = true;
    isInstance = isInstance && "indices" in value;
    isInstance = isInstance && "values" in value;
    return isInstance;
}
exports.instanceOfSparseValues = instanceOfSparseValues;
function SparseValuesFromJSON(json) {
    return SparseValuesFromJSONTyped(json, false);
}
exports.SparseValuesFromJSON = SparseValuesFromJSON;
function SparseValuesFromJSONTyped(json, ignoreDiscriminator) {
    if ((json === undefined) || (json === null)) {
        return json;
    }
    return {
        'indices': json['indices'],
        'values': json['values'],
    };
}
exports.SparseValuesFromJSONTyped = SparseValuesFromJSONTyped;
function SparseValuesToJSON(value) {
    if (value === undefined) {
        return undefined;
    }
    if (value === null) {
        return null;
    }
    return {
        'indices': value.indices,
        'values': value.values,
    };
}
exports.SparseValuesToJSON = SparseValuesToJSON;
//# sourceMappingURL=SparseValues.js.map