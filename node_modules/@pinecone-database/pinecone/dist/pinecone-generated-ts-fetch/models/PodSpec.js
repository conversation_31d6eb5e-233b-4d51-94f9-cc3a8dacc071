"use strict";
/* tslint:disable */
/* eslint-disable */
/**
 * Pinecone Control Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: v1
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.PodSpecToJSON = exports.PodSpecFromJSONTyped = exports.PodSpecFromJSON = exports.instanceOfPodSpec = void 0;
var runtime_1 = require("../runtime");
var PodSpecMetadataConfig_1 = require("./PodSpecMetadataConfig");
/**
 * Check if a given object implements the PodSpec interface.
 */
function instanceOfPodSpec(value) {
    var isInstance = true;
    isInstance = isInstance && "environment" in value;
    isInstance = isInstance && "replicas" in value;
    isInstance = isInstance && "shards" in value;
    isInstance = isInstance && "podType" in value;
    isInstance = isInstance && "pods" in value;
    return isInstance;
}
exports.instanceOfPodSpec = instanceOfPodSpec;
function PodSpecFromJSON(json) {
    return PodSpecFromJSONTyped(json, false);
}
exports.PodSpecFromJSON = PodSpecFromJSON;
function PodSpecFromJSONTyped(json, ignoreDiscriminator) {
    if ((json === undefined) || (json === null)) {
        return json;
    }
    return {
        'environment': json['environment'],
        'replicas': json['replicas'],
        'shards': json['shards'],
        'podType': json['pod_type'],
        'pods': json['pods'],
        'metadataConfig': !(0, runtime_1.exists)(json, 'metadata_config') ? undefined : (0, PodSpecMetadataConfig_1.PodSpecMetadataConfigFromJSON)(json['metadata_config']),
        'sourceCollection': !(0, runtime_1.exists)(json, 'source_collection') ? undefined : json['source_collection'],
    };
}
exports.PodSpecFromJSONTyped = PodSpecFromJSONTyped;
function PodSpecToJSON(value) {
    if (value === undefined) {
        return undefined;
    }
    if (value === null) {
        return null;
    }
    return {
        'environment': value.environment,
        'replicas': value.replicas,
        'shards': value.shards,
        'pod_type': value.podType,
        'pods': value.pods,
        'metadata_config': (0, PodSpecMetadataConfig_1.PodSpecMetadataConfigToJSON)(value.metadataConfig),
        'source_collection': value.sourceCollection,
    };
}
exports.PodSpecToJSON = PodSpecToJSON;
//# sourceMappingURL=PodSpec.js.map