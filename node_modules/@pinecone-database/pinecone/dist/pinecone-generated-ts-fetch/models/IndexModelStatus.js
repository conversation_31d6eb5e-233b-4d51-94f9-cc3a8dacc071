"use strict";
/* tslint:disable */
/* eslint-disable */
/**
 * Pinecone Control Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: v1
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.IndexModelStatusToJSON = exports.IndexModelStatusFromJSONTyped = exports.IndexModelStatusFromJSON = exports.instanceOfIndexModelStatus = exports.IndexModelStatusStateEnum = void 0;
/**
 * @export
 */
exports.IndexModelStatusStateEnum = {
    Initializing: 'Initializing',
    InitializationFailed: 'InitializationFailed',
    ScalingUp: 'ScalingUp',
    ScalingDown: 'ScalingDown',
    ScalingUpPodSize: 'ScalingUpPodSize',
    ScalingDownPodSize: 'ScalingDownPodSize',
    Terminating: 'Terminating',
    Ready: 'Ready'
};
/**
 * Check if a given object implements the IndexModelStatus interface.
 */
function instanceOfIndexModelStatus(value) {
    var isInstance = true;
    isInstance = isInstance && "ready" in value;
    isInstance = isInstance && "state" in value;
    return isInstance;
}
exports.instanceOfIndexModelStatus = instanceOfIndexModelStatus;
function IndexModelStatusFromJSON(json) {
    return IndexModelStatusFromJSONTyped(json, false);
}
exports.IndexModelStatusFromJSON = IndexModelStatusFromJSON;
function IndexModelStatusFromJSONTyped(json, ignoreDiscriminator) {
    if ((json === undefined) || (json === null)) {
        return json;
    }
    return {
        'ready': json['ready'],
        'state': json['state'],
    };
}
exports.IndexModelStatusFromJSONTyped = IndexModelStatusFromJSONTyped;
function IndexModelStatusToJSON(value) {
    if (value === undefined) {
        return undefined;
    }
    if (value === null) {
        return null;
    }
    return {
        'ready': value.ready,
        'state': value.state,
    };
}
exports.IndexModelStatusToJSON = IndexModelStatusToJSON;
//# sourceMappingURL=IndexModelStatus.js.map