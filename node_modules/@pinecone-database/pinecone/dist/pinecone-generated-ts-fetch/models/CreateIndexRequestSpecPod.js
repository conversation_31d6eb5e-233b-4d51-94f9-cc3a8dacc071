"use strict";
/* tslint:disable */
/* eslint-disable */
/**
 * Pinecone Control Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: v1
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateIndexRequestSpecPodToJSON = exports.CreateIndexRequestSpecPodFromJSONTyped = exports.CreateIndexRequestSpecPodFromJSON = exports.instanceOfCreateIndexRequestSpecPod = void 0;
var runtime_1 = require("../runtime");
var CreateIndexRequestSpecPodMetadataConfig_1 = require("./CreateIndexRequestSpecPodMetadataConfig");
/**
 * Check if a given object implements the CreateIndexRequestSpecPod interface.
 */
function instanceOfCreateIndexRequestSpecPod(value) {
    var isInstance = true;
    isInstance = isInstance && "environment" in value;
    isInstance = isInstance && "podType" in value;
    return isInstance;
}
exports.instanceOfCreateIndexRequestSpecPod = instanceOfCreateIndexRequestSpecPod;
function CreateIndexRequestSpecPodFromJSON(json) {
    return CreateIndexRequestSpecPodFromJSONTyped(json, false);
}
exports.CreateIndexRequestSpecPodFromJSON = CreateIndexRequestSpecPodFromJSON;
function CreateIndexRequestSpecPodFromJSONTyped(json, ignoreDiscriminator) {
    if ((json === undefined) || (json === null)) {
        return json;
    }
    return {
        'environment': json['environment'],
        'replicas': !(0, runtime_1.exists)(json, 'replicas') ? undefined : json['replicas'],
        'podType': json['pod_type'],
        'pods': !(0, runtime_1.exists)(json, 'pods') ? undefined : json['pods'],
        'shards': !(0, runtime_1.exists)(json, 'shards') ? undefined : json['shards'],
        'metadataConfig': !(0, runtime_1.exists)(json, 'metadata_config') ? undefined : (0, CreateIndexRequestSpecPodMetadataConfig_1.CreateIndexRequestSpecPodMetadataConfigFromJSON)(json['metadata_config']),
        'sourceCollection': !(0, runtime_1.exists)(json, 'source_collection') ? undefined : json['source_collection'],
    };
}
exports.CreateIndexRequestSpecPodFromJSONTyped = CreateIndexRequestSpecPodFromJSONTyped;
function CreateIndexRequestSpecPodToJSON(value) {
    if (value === undefined) {
        return undefined;
    }
    if (value === null) {
        return null;
    }
    return {
        'environment': value.environment,
        'replicas': value.replicas,
        'pod_type': value.podType,
        'pods': value.pods,
        'shards': value.shards,
        'metadata_config': (0, CreateIndexRequestSpecPodMetadataConfig_1.CreateIndexRequestSpecPodMetadataConfigToJSON)(value.metadataConfig),
        'source_collection': value.sourceCollection,
    };
}
exports.CreateIndexRequestSpecPodToJSON = CreateIndexRequestSpecPodToJSON;
//# sourceMappingURL=CreateIndexRequestSpecPod.js.map