/**
 * Pinecone Control Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: v1
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
/**
 * Configuration needed to deploy a serverless index.
 * @export
 * @interface ServerlessSpec
 */
export interface ServerlessSpec {
    /**
     * The public cloud where you would like your index hosted. Serverless indexes can be hosted only in AWS at this time.
     * @type {string}
     * @memberof ServerlessSpec
     */
    cloud: ServerlessSpecCloudEnum;
    /**
     * The region where you would like your index to be created.  Serverless indexes can be created only in the us-west-2 region of AWS at this time.
     * @type {string}
     * @memberof ServerlessSpec
     */
    region: string;
}
/**
 * @export
 */
export declare const ServerlessSpecCloudEnum: {
    readonly Gcp: "gcp";
    readonly Aws: "aws";
    readonly Azure: "azure";
};
export type ServerlessSpecCloudEnum = typeof ServerlessSpecCloudEnum[keyof typeof ServerlessSpecCloudEnum];
/**
 * Check if a given object implements the ServerlessSpec interface.
 */
export declare function instanceOfServerlessSpec(value: object): boolean;
export declare function ServerlessSpecFromJSON(json: any): ServerlessSpec;
export declare function ServerlessSpecFromJSONTyped(json: any, ignoreDiscriminator: boolean): ServerlessSpec;
export declare function ServerlessSpecToJSON(value?: ServerlessSpec | null): any;
