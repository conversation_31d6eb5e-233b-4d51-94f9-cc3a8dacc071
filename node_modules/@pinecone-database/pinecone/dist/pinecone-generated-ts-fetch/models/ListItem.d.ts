/**
 * Pinecone Control Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: v1
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
/**
 *
 * @export
 * @interface ListItem
 */
export interface ListItem {
    /**
     *
     * @type {string}
     * @memberof ListItem
     */
    id?: string;
}
/**
 * Check if a given object implements the ListItem interface.
 */
export declare function instanceOfListItem(value: object): boolean;
export declare function ListItemFromJSON(json: any): ListItem;
export declare function ListItemFromJSONTyped(json: any, ignoreDiscriminator: boolean): ListItem;
export declare function ListItemToJSON(value?: ListItem | null): any;
