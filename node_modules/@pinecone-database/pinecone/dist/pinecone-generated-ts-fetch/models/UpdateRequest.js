"use strict";
/* tslint:disable */
/* eslint-disable */
/**
 * Pinecone Control Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: v1
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateRequestToJSON = exports.UpdateRequestFromJSONTyped = exports.UpdateRequestFromJSON = exports.instanceOfUpdateRequest = void 0;
var runtime_1 = require("../runtime");
var SparseValues_1 = require("./SparseValues");
/**
 * Check if a given object implements the UpdateRequest interface.
 */
function instanceOfUpdateRequest(value) {
    var isInstance = true;
    isInstance = isInstance && "id" in value;
    return isInstance;
}
exports.instanceOfUpdateRequest = instanceOfUpdateRequest;
function UpdateRequestFromJSON(json) {
    return UpdateRequestFromJSONTyped(json, false);
}
exports.UpdateRequestFromJSON = UpdateRequestFromJSON;
function UpdateRequestFromJSONTyped(json, ignoreDiscriminator) {
    if ((json === undefined) || (json === null)) {
        return json;
    }
    return {
        'id': json['id'],
        'values': !(0, runtime_1.exists)(json, 'values') ? undefined : json['values'],
        'sparseValues': !(0, runtime_1.exists)(json, 'sparseValues') ? undefined : (0, SparseValues_1.SparseValuesFromJSON)(json['sparseValues']),
        'setMetadata': !(0, runtime_1.exists)(json, 'setMetadata') ? undefined : json['setMetadata'],
        'namespace': !(0, runtime_1.exists)(json, 'namespace') ? undefined : json['namespace'],
    };
}
exports.UpdateRequestFromJSONTyped = UpdateRequestFromJSONTyped;
function UpdateRequestToJSON(value) {
    if (value === undefined) {
        return undefined;
    }
    if (value === null) {
        return null;
    }
    return {
        'id': value.id,
        'values': value.values,
        'sparseValues': (0, SparseValues_1.SparseValuesToJSON)(value.sparseValues),
        'setMetadata': value.setMetadata,
        'namespace': value.namespace,
    };
}
exports.UpdateRequestToJSON = UpdateRequestToJSON;
//# sourceMappingURL=UpdateRequest.js.map