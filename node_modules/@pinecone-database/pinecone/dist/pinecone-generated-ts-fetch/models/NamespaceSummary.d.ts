/**
 * Pinecone Control Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: v1
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
/**
 * A summary of the contents of a namespace.
 * @export
 * @interface NamespaceSummary
 */
export interface NamespaceSummary {
    /**
     * The number of vectors stored in this namespace. Note that updates to this field may lag behind updates to the
     * underlying index and corresponding query results, etc.
     * @type {number}
     * @memberof NamespaceSummary
     */
    vectorCount?: number;
}
/**
 * Check if a given object implements the NamespaceSummary interface.
 */
export declare function instanceOfNamespaceSummary(value: object): boolean;
export declare function NamespaceSummaryFromJSON(json: any): NamespaceSummary;
export declare function NamespaceSummaryFromJSONTyped(json: any, ignoreDiscriminator: boolean): NamespaceSummary;
export declare function NamespaceSummaryToJSON(value?: NamespaceSummary | null): any;
