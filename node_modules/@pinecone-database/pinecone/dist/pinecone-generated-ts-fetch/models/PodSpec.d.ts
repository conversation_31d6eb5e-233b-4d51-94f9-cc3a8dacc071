/**
 * Pinecone Control Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: v1
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import type { PodSpecMetadataConfig } from './PodSpecMetadataConfig';
/**
 * Configuration needed to deploy a pod-based index.
 * @export
 * @interface PodSpec
 */
export interface PodSpec {
    /**
     * The environment where the index is hosted.
     * @type {string}
     * @memberof PodSpec
     */
    environment: string;
    /**
     * The number of replicas. Replicas duplicate your index. They provide higher availability and throughput. Replicas can be scaled up or down as your needs change.
     * @type {number}
     * @memberof PodSpec
     */
    replicas: number;
    /**
     * The number of shards. Shards split your data across multiple pods so you can fit more data into an index.
     * @type {number}
     * @memberof PodSpec
     */
    shards: number;
    /**
     * The type of pod to use. One of `s1`, `p1`, or `p2` appended with `.` and one of `x1`, `x2`, `x4`, or `x8`.
     * @type {string}
     * @memberof PodSpec
     */
    podType: string;
    /**
     * The number of pods to be used in the index. This should be equal to `shards` x `replicas`.'
     * @type {number}
     * @memberof PodSpec
     */
    pods: number;
    /**
     *
     * @type {PodSpecMetadataConfig}
     * @memberof PodSpec
     */
    metadataConfig?: PodSpecMetadataConfig;
    /**
     * The name of the collection to be used as the source for the index.
     * @type {string}
     * @memberof PodSpec
     */
    sourceCollection?: string;
}
/**
 * Check if a given object implements the PodSpec interface.
 */
export declare function instanceOfPodSpec(value: object): boolean;
export declare function PodSpecFromJSON(json: any): PodSpec;
export declare function PodSpecFromJSONTyped(json: any, ignoreDiscriminator: boolean): PodSpec;
export declare function PodSpecToJSON(value?: PodSpec | null): any;
