/**
 * Pinecone Control Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: v1
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import * as runtime from '../runtime';
import type { CollectionList, CollectionModel, ConfigureIndexRequest, CreateCollectionRequest, CreateIndexRequest, IndexList, IndexModel } from '../models/index';
export interface ConfigureIndexOperationRequest {
    indexName: string;
    configureIndexRequest: ConfigureIndexRequest;
}
export interface CreateCollectionOperationRequest {
    createCollectionRequest: CreateCollectionRequest;
}
export interface CreateIndexOperationRequest {
    createIndexRequest: CreateIndexRequest;
}
export interface DeleteCollectionRequest {
    collectionName: string;
}
export interface DeleteIndexRequest {
    indexName: string;
}
export interface DescribeCollectionRequest {
    collectionName: string;
}
export interface DescribeIndexRequest {
    indexName: string;
}
/**
 *
 */
export declare class ManageIndexesApi extends runtime.BaseAPI {
    /**
     * This operation specifies the pod type and number of replicas for an index. It applies to pod-based indexes only. Serverless indexes scale automatically based on usage.
     * Configure an index
     */
    configureIndexRaw(requestParameters: ConfigureIndexOperationRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<IndexModel>>;
    /**
     * This operation specifies the pod type and number of replicas for an index. It applies to pod-based indexes only. Serverless indexes scale automatically based on usage.
     * Configure an index
     */
    configureIndex(requestParameters: ConfigureIndexOperationRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<IndexModel>;
    /**
     * This operation creates a Pinecone collection. Serverless and starter indexes do not support collections.
     * Create a collection
     */
    createCollectionRaw(requestParameters: CreateCollectionOperationRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<CollectionModel>>;
    /**
     * This operation creates a Pinecone collection. Serverless and starter indexes do not support collections.
     * Create a collection
     */
    createCollection(requestParameters: CreateCollectionOperationRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<CollectionModel>;
    /**
     * This operation deploys a Pinecone index. This is where you specify the measure of similarity, the dimension of vectors to be stored in the index, which cloud provider you would like to deploy with, and more.  For guidance and examples, see [Create an index](https://docs.pinecone.io/docs/manage-indexes#create-a-serverless-index).
     * Create an index
     */
    createIndexRaw(requestParameters: CreateIndexOperationRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<IndexModel>>;
    /**
     * This operation deploys a Pinecone index. This is where you specify the measure of similarity, the dimension of vectors to be stored in the index, which cloud provider you would like to deploy with, and more.  For guidance and examples, see [Create an index](https://docs.pinecone.io/docs/manage-indexes#create-a-serverless-index).
     * Create an index
     */
    createIndex(requestParameters: CreateIndexOperationRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<IndexModel>;
    /**
     * This operation deletes an existing collection.  Serverless and starter indexes do not support collections.
     * Delete a collection
     */
    deleteCollectionRaw(requestParameters: DeleteCollectionRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<string>>;
    /**
     * This operation deletes an existing collection.  Serverless and starter indexes do not support collections.
     * Delete a collection
     */
    deleteCollection(requestParameters: DeleteCollectionRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<string>;
    /**
     * This operation deletes an existing index.
     * Delete an index
     */
    deleteIndexRaw(requestParameters: DeleteIndexRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<void>>;
    /**
     * This operation deletes an existing index.
     * Delete an index
     */
    deleteIndex(requestParameters: DeleteIndexRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<void>;
    /**
     * This operation gets a description of a collection.  Serverless and starter indexes do not support collections.
     * Describe a collection
     */
    describeCollectionRaw(requestParameters: DescribeCollectionRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<CollectionModel>>;
    /**
     * This operation gets a description of a collection.  Serverless and starter indexes do not support collections.
     * Describe a collection
     */
    describeCollection(requestParameters: DescribeCollectionRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<CollectionModel>;
    /**
     * Get a description of an index.
     * Describe an index
     */
    describeIndexRaw(requestParameters: DescribeIndexRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<IndexModel>>;
    /**
     * Get a description of an index.
     * Describe an index
     */
    describeIndex(requestParameters: DescribeIndexRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<IndexModel>;
    /**
     * This operation returns a list of all collections in a project. Serverless and starter indexes do not support collections.
     * List collections
     */
    listCollectionsRaw(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<CollectionList>>;
    /**
     * This operation returns a list of all collections in a project. Serverless and starter indexes do not support collections.
     * List collections
     */
    listCollections(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<CollectionList>;
    /**
     * This operation returns a list of all indexes in a project.
     * List indexes
     */
    listIndexesRaw(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<IndexList>>;
    /**
     * This operation returns a list of all indexes in a project.
     * List indexes
     */
    listIndexes(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<IndexList>;
}
