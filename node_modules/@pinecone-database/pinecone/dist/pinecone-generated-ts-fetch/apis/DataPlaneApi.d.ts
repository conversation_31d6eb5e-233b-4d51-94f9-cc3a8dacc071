/**
 * Pinecone Control Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: v1
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import * as runtime from '../runtime';
import type { DeleteRequest, DescribeIndexStatsRequest, DescribeIndexStatsResponse, FetchResponse, ListResponse, QueryRequest, QueryResponse, UpdateRequest, UpsertRequest, UpsertResponse } from '../models/index';
export interface DeleteOperationRequest {
    deleteRequest: DeleteRequest;
}
export interface AltDeleteRequest {
    ids?: Array<string>;
    deleteAll?: boolean;
    namespace?: string;
}
export interface DescribeIndexStatsOperationRequest {
    describeIndexStatsRequest: DescribeIndexStatsRequest;
}
export interface FetchRequest {
    ids: Array<string>;
    namespace?: string;
}
export interface ListRequest {
    prefix?: string;
    limit?: number;
    paginationToken?: string;
    namespace?: string;
}
export interface QueryOperationRequest {
    queryRequest: QueryRequest;
}
export interface UpdateOperationRequest {
    updateRequest: UpdateRequest;
}
export interface UpsertOperationRequest {
    upsertRequest: UpsertRequest;
}
/**
 *
 */
export declare class DataPlaneApi extends runtime.BaseAPI {
    /**
     * The `delete` operation deletes vectors, by id, from a single namespace.  For guidance and examples, see [Delete data](https://docs.pinecone.io/docs/delete-data).
     * Delete vectors
     */
    _deleteRaw(requestParameters: DeleteOperationRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<object>>;
    /**
     * The `delete` operation deletes vectors, by id, from a single namespace.  For guidance and examples, see [Delete data](https://docs.pinecone.io/docs/delete-data).
     * Delete vectors
     */
    _delete(requestParameters: DeleteOperationRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<object>;
    /**
     * DEPRECATED. Use [`POST /delete`](https://docs.pinecone.io/reference/delete) instead.
     * Delete vectors
     */
    altDeleteRaw(requestParameters: AltDeleteRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<object>>;
    /**
     * DEPRECATED. Use [`POST /delete`](https://docs.pinecone.io/reference/delete) instead.
     * Delete vectors
     */
    altDelete(requestParameters?: AltDeleteRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<object>;
    /**
     * DEPRECATED. Use [`POST /describe_index_stats`](https://docs.pinecone.io/reference/describe_index_stats) instead.
     * Get index stats
     */
    altDescribeIndexStatsRaw(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<DescribeIndexStatsResponse>>;
    /**
     * DEPRECATED. Use [`POST /describe_index_stats`](https://docs.pinecone.io/reference/describe_index_stats) instead.
     * Get index stats
     */
    altDescribeIndexStats(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<DescribeIndexStatsResponse>;
    /**
     * The `describe_index_stats` operation returns statistics about the contents of an index, including the vector count per namespace and the number of dimensions, and the index fullness.  Serverless indexes scale automatically as needed, so index fullness is relevant only for pod-based indexes.  For pod-based indexes, the index fullness result may be inaccurate during pod resizing; to get the status of a pod resizing process, use [`describe_index`](https://www.pinecone.io/docs/api/operation/describe_index/).
     * Get index stats
     */
    describeIndexStatsRaw(requestParameters: DescribeIndexStatsOperationRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<DescribeIndexStatsResponse>>;
    /**
     * The `describe_index_stats` operation returns statistics about the contents of an index, including the vector count per namespace and the number of dimensions, and the index fullness.  Serverless indexes scale automatically as needed, so index fullness is relevant only for pod-based indexes.  For pod-based indexes, the index fullness result may be inaccurate during pod resizing; to get the status of a pod resizing process, use [`describe_index`](https://www.pinecone.io/docs/api/operation/describe_index/).
     * Get index stats
     */
    describeIndexStats(requestParameters: DescribeIndexStatsOperationRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<DescribeIndexStatsResponse>;
    /**
     * The `fetch` operation looks up and returns vectors, by ID, from a single namespace. The returned vectors include the vector data and/or metadata.  For guidance and examples, see [Fetch data](https://docs.pinecone.io/docs/fetch-data).
     * Fetch vectors
     */
    fetchRaw(requestParameters: FetchRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<FetchResponse>>;
    /**
     * The `fetch` operation looks up and returns vectors, by ID, from a single namespace. The returned vectors include the vector data and/or metadata.  For guidance and examples, see [Fetch data](https://docs.pinecone.io/docs/fetch-data).
     * Fetch vectors
     */
    fetch(requestParameters: FetchRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<FetchResponse>;
    /**
     * The `list` operation lists the IDs of vectors in a single namespace of a serverless index. An optional prefix can be passed to limit the results to IDs with a common prefix.  `list` returns up to 100 IDs at a time by default in sorted order (bitwise/\"C\" collation). If the `limit` parameter is set, `list` returns up to that number of IDs instead. Whenever there are additional IDs to return, the response also includes a `pagination_token` that you can use to get the next batch of IDs. When the response does not include a `pagination_token`, there are no more IDs to return.  For guidance and examples, see [Get record IDs](https://docs.pinecone.io/docs/get-record-ids).  **Note:** `list` is supported only for serverless indexes.
     * List vector IDs
     */
    listRaw(requestParameters: ListRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<ListResponse>>;
    /**
     * The `list` operation lists the IDs of vectors in a single namespace of a serverless index. An optional prefix can be passed to limit the results to IDs with a common prefix.  `list` returns up to 100 IDs at a time by default in sorted order (bitwise/\"C\" collation). If the `limit` parameter is set, `list` returns up to that number of IDs instead. Whenever there are additional IDs to return, the response also includes a `pagination_token` that you can use to get the next batch of IDs. When the response does not include a `pagination_token`, there are no more IDs to return.  For guidance and examples, see [Get record IDs](https://docs.pinecone.io/docs/get-record-ids).  **Note:** `list` is supported only for serverless indexes.
     * List vector IDs
     */
    list(requestParameters?: ListRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<ListResponse>;
    /**
     * The `query` operation searches a namespace, using a query vector. It retrieves the ids of the most similar items in a namespace, along with their similarity scores.  For guidance and examples, see [Query data](https://docs.pinecone.io/docs/query-data).
     * Query vectors
     */
    queryRaw(requestParameters: QueryOperationRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<QueryResponse>>;
    /**
     * The `query` operation searches a namespace, using a query vector. It retrieves the ids of the most similar items in a namespace, along with their similarity scores.  For guidance and examples, see [Query data](https://docs.pinecone.io/docs/query-data).
     * Query vectors
     */
    query(requestParameters: QueryOperationRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<QueryResponse>;
    /**
     * The `update` operation updates a vector in a namespace. If a value is included, it will overwrite the previous value. If a `set_metadata` is included, the values of the fields specified in it will be added or overwrite the previous value.  For guidance and examples, see [Update data](https://docs.pinecone.io/docs/update-data).
     * Update a vector
     */
    updateRaw(requestParameters: UpdateOperationRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<object>>;
    /**
     * The `update` operation updates a vector in a namespace. If a value is included, it will overwrite the previous value. If a `set_metadata` is included, the values of the fields specified in it will be added or overwrite the previous value.  For guidance and examples, see [Update data](https://docs.pinecone.io/docs/update-data).
     * Update a vector
     */
    update(requestParameters: UpdateOperationRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<object>;
    /**
     * The `upsert` operation writes vectors into a namespace. If a new value is upserted for an existing vector ID, it will overwrite the previous value.  For guidance and examples, see [Upsert data](https://docs.pinecone.io/docs/upsert-data).
     * Upsert vectors
     */
    upsertRaw(requestParameters: UpsertOperationRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<UpsertResponse>>;
    /**
     * The `upsert` operation writes vectors into a namespace. If a new value is upserted for an existing vector ID, it will overwrite the previous value.  For guidance and examples, see [Upsert data](https://docs.pinecone.io/docs/upsert-data).
     * Upsert vectors
     */
    upsert(requestParameters: UpsertOperationRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<UpsertResponse>;
}
