{"version": 3, "file": "http.js", "sourceRoot": "", "sources": ["../../src/errors/http.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAAA,+BAA2C;AAU3C,IAAM,WAAW,GAAG,qHAAqH,CAAC;AAE1I;;;;;;GAMG;AACH;IAA6C,2CAAiB;IAC5D,iCAAY,aAAgC;QAA5C,iBAIC;QAHS,IAAA,OAAO,GAAK,aAAa,QAAlB,CAAmB;gBAClC,kBAAM,OAAO,CAAC;QACd,KAAI,CAAC,IAAI,GAAG,yBAAyB,CAAC;;IACxC,CAAC;IACH,8BAAC;AAAD,CAAC,AAND,CAA6C,wBAAiB,GAM7D;AANY,0DAAuB;AAQpC;;;;;GAKG;AACH;IAAgD,8CAAiB;IAC/D,oCAAY,aAAgC;QAA5C,iBAYC;QAXS,IAAA,GAAG,GAAK,aAAa,IAAlB,CAAmB;QAC9B,IAAI,GAAG,EAAE;YACP,QAAA,kBACE,8DAAuD,GAAG,qEAA2D,WAAW,CAAE,CACnI,SAAC;SACH;aAAM;YACL,QAAA,kBACE,uGAAgG,WAAW,CAAE,CAC9G,SAAC;SACH;QACD,KAAI,CAAC,IAAI,GAAG,4BAA4B,CAAC;;IAC3C,CAAC;IACH,iCAAC;AAAD,CAAC,AAdD,CAAgD,wBAAiB,GAchE;AAdY,gEAA0B;AAgBvC;;;GAGG;AACH;IAA2C,yCAAiB;IAC1D,+BAAY,aAAgC;QAA5C,iBASC;QARS,IAAA,GAAG,GAAK,aAAa,IAAlB,CAAmB;QAC9B,IAAI,GAAG,EAAE;YACP,QAAA,kBAAM,oBAAa,GAAG,+BAA4B,CAAC,SAAC;SACrD;aAAM;YACL,QAAA,kBAAM,4CAA4C,CAAC,SAAC;SACrD;QAED,KAAI,CAAC,IAAI,GAAG,uBAAuB,CAAC;;IACtC,CAAC;IACH,4BAAC;AAAD,CAAC,AAXD,CAA2C,wBAAiB,GAW3D;AAXY,sDAAqB;AAalC;;;KAGK;AACL;IAA2C,yCAAiB;IAC1D,+BAAY,aAAgC;QAA5C,iBAWC;QAVS,IAAA,GAAG,GAAc,aAAa,IAA3B,EAAE,OAAO,GAAK,aAAa,QAAlB,CAAmB;QACvC,IAAI,GAAG,EAAE;YACP,QAAA,kBACE,oBAAa,GAAG,wCAA8B,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAE,CACvE,SAAC;SACH;aAAM;YACL,QAAA,kBAAM,2DAA2D,CAAC,SAAC;SACpE;QAED,KAAI,CAAC,IAAI,GAAG,uBAAuB,CAAC;;IACtC,CAAC;IACH,4BAAC;AAAD,CAAC,AAbD,CAA2C,wBAAiB,GAa3D;AAbY,sDAAqB;AAelC;;;;;;GAMG;AACH;IAAiD,+CAAiB;IAChE,qCAAY,aAAgC;QAA5C,iBASC;QARS,IAAA,GAAG,GAAW,aAAa,IAAxB,EAAE,IAAI,GAAK,aAAa,KAAlB,CAAmB;QACpC,IAAM,KAAK,GAAG,GAAG;YACf,CAAC,CAAC,6DAAsD,GAAG,eAAY;YACvE,CAAC,CAAC,EAAE,CAAC;QACP,IAAM,IAAI,GAAG,kWAAkW,CAAC;QAChX,IAAM,WAAW,GAAG,IAAI,CAAC,CAAC,CAAC,gBAAS,IAAI,CAAE,CAAC,CAAC,CAAC,EAAE,CAAC;gBAChD,kBAAM,CAAC,KAAK,EAAE,IAAI,EAAE,WAAW,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;QAClD,KAAI,CAAC,IAAI,GAAG,6BAA6B,CAAC;;IAC5C,CAAC;IACH,kCAAC;AAAD,CAAC,AAXD,CAAiD,wBAAiB,GAWjE;AAXY,kEAA2B;AAaxC;;;;;;;GAOG;AACH;IAAiD,+CAAiB;IAChE,qCAAY,WAA8B;QAA1C,iBAUC;QATS,IAAA,GAAG,GAAc,WAAW,IAAzB,EAAE,OAAO,GAAK,WAAW,QAAhB,CAAiB;QACrC,IAAI,GAAG,EAAE;YACP,QAAA,kBACE,oBAAa,GAAG,wCAA8B,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAE,CACvE,SAAC;SACH;aAAM;YACL,QAAA,iBAAO,SAAC;SACT;QACD,KAAI,CAAC,IAAI,GAAG,6BAA6B,CAAC;;IAC5C,CAAC;IACH,kCAAC;AAAD,CAAC,AAZD,CAAiD,wBAAiB,GAYjE;AAZY,kEAA2B;AAcxC;;;GAGG;AACH;IAA+C,6CAAiB;IAC9D,mCAAY,aAAgC;QAA5C,iBAUC;QATS,IAAA,GAAG,GAA4B,aAAa,IAAzC,EAAE,MAAM,GAAoB,aAAa,OAAjC,EAAE,IAAI,GAAc,aAAa,KAA3B,EAAE,OAAO,GAAK,aAAa,QAAlB,CAAmB;QACrD,IAAM,KAAK,GAAG,GAAG;YACf,CAAC,CAAC,wDAAiD,GAAG,gBAAa;YACnE,CAAC,CAAC,EAAE,CAAC;QACP,IAAM,SAAS,GAAG,MAAM,CAAC,CAAC,CAAC,kBAAW,MAAM,OAAI,CAAC,CAAC,CAAC,EAAE,CAAC;QACtD,IAAM,OAAO,GAAG,IAAI,CAAC,CAAC,CAAC,gBAAS,IAAI,CAAE,CAAC,CAAC,CAAC,EAAE,CAAC;gBAE5C,kBAAM,CAAC,KAAK,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;QAC5D,KAAI,CAAC,IAAI,GAAG,2BAA2B,CAAC;;IAC1C,CAAC;IACH,gCAAC;AAAD,CAAC,AAZD,CAA+C,wBAAiB,GAY/D;AAZY,8DAAyB;AActC,gBAAgB;AACT,IAAM,kBAAkB,GAAG,UAAC,iBAAoC;IACrE,QAAQ,iBAAiB,CAAC,MAAM,EAAE;QAChC,KAAK,GAAG;YACN,OAAO,IAAI,uBAAuB,CAAC,iBAAiB,CAAC,CAAC;QACxD,KAAK,GAAG;YACN,OAAO,IAAI,0BAA0B,CAAC,iBAAiB,CAAC,CAAC;QAC3D,KAAK,GAAG;YACN,OAAO,IAAI,qBAAqB,CAAC,iBAAiB,CAAC,CAAC;QACtD,KAAK,GAAG;YACN,OAAO,IAAI,qBAAqB,CAAC,iBAAiB,CAAC,CAAC;QACtD,KAAK,GAAG;YACN,OAAO,IAAI,2BAA2B,CAAC,iBAAiB,CAAC,CAAC;QAC5D,KAAK,GAAG;YACN,OAAO,IAAI,2BAA2B,CAAC,iBAAiB,CAAC,CAAC;QAC5D;YACE,MAAM,IAAI,yBAAyB,CAAC,iBAAiB,CAAC,CAAC;KAC1D;AACH,CAAC,CAAC;AAjBW,QAAA,kBAAkB,sBAiB7B"}