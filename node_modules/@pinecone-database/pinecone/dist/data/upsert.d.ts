import { buildConfigValidator } from '../validator';
import { DataOperationsProvider } from './dataOperationsProvider';
import type { PineconeRecord, RecordMetadata } from './types';
export declare class UpsertCommand<T extends RecordMetadata = RecordMetadata> {
    apiProvider: DataOperationsProvider;
    namespace: string;
    validator: ReturnType<typeof buildConfigValidator>;
    constructor(apiProvider: any, namespace: any);
    run(records: Array<PineconeRecord<T>>): Promise<void>;
}
