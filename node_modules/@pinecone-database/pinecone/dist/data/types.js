"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PineconeRecordSchema = exports.RecordSparseValuesSchema = exports.RecordValuesSchema = exports.RecordIdSchema = exports.PineconeConfigurationSchema = void 0;
var typebox_1 = require("@sinclair/typebox");
exports.PineconeConfigurationSchema = typebox_1.Type.Object({
    apiKey: typebox_1.Type.String({ minLength: 1 }),
    controllerHostUrl: typebox_1.Type.Optional(typebox_1.Type.String({ minLength: 1 })),
    // fetchApi is a complex type that I don't really want to recreate in the
    // form of a json schema (seems difficult and error prone). So we will
    // rely on TypeScript to guide people in the right direction here.
    // But declaring it here as Type.Any() is needed to avoid getting caught
    // in the additionalProperties check.
    fetchApi: typebox_1.Type.Optional(typebox_1.Type.Any()),
    additionalHeaders: typebox_1.Type.Optional(typebox_1.Type.Any()),
    sourceTag: typebox_1.Type.Optional(typebox_1.Type.String({ minLength: 1 })),
}, { additionalProperties: false });
exports.RecordIdSchema = typebox_1.Type.String({ minLength: 1 });
exports.RecordValuesSchema = typebox_1.Type.Array(typebox_1.Type.Number());
exports.RecordSparseValuesSchema = typebox_1.Type.Object({
    indices: typebox_1.Type.Array(typebox_1.Type.Integer()),
    values: typebox_1.Type.Array(typebox_1.Type.Number()),
}, { additionalProperties: false });
exports.PineconeRecordSchema = typebox_1.Type.Object({
    id: exports.RecordIdSchema,
    values: exports.RecordValuesSchema,
    sparseValues: typebox_1.Type.Optional(exports.RecordSparseValuesSchema),
    metadata: typebox_1.Type.Optional(typebox_1.Type.Object({}, { additionalProperties: true })),
}, { additionalProperties: false });
//# sourceMappingURL=types.js.map