{"version": 3, "file": "validator.js", "sourceRoot": "", "sources": ["../src/validator.ts"], "names": [], "mappings": ";;;;;;AAAA,4CAAsB;AAEtB,mCAAiD;AACjD,mDAA6C;AAE7C,IAAM,OAAO,GAAG,UAAC,MAAc,EAAE,OAAe;IAC9C,OAAO,UAAG,MAAM,cAAI,OAAO,CAAE,CAAC;AAChC,CAAC,CAAC;AAEF,IAAM,uBAAuB,GAAG,oBAAoB,CAAC;AACrD,IAAM,4BAA4B,GAAG,yBAAyB,CAAC;AAC/D,IAAM,0BAA0B,GAAG,oBAAoB,CAAC;AACxD,IAAM,0BAA0B,GAAG,QAAQ,CAAC;AAE5C,2DAA2D;AAC3D,2CAA2C;AAC3C,IAAM,SAAS,GAAG,CAAC,CAAC;AAEpB,IAAM,qBAAqB,GAAG,UAAC,CAAC,EAAE,oBAAoB;IACpD,IAAI,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,EAAE;QAC3C,wBAAwB;QACxB,IAAI,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE;YACtC,uBAAuB;YACvB,IAAM,aAAa,GAAG,4BAA4B,CAAC,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;YACtE,IAAM,QAAQ,GAAG,aAAa,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;YAC9D,IAAM,cAAc,GAAG,0BAA0B,CAAC,IAAI,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC;YACvE,IAAM,SAAS,GAAG,cAAc,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;YACjE,oBAAoB,CAAC,IAAI,CACvB,wBAAiB,SAAS,sBAAY,QAAQ,qBAAW,CAAC,CAAC,OAAO,CAAE,CACrE,CAAC;SACH;aAAM;YACL,2BAA2B;YAC3B,IAAM,aAAa,GAAG,uBAAuB,CAAC,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;YACjE,IAAM,QAAQ,GAAG,aAAa,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;YAC9D,oBAAoB,CAAC,IAAI,CAAC,oBAAa,QAAQ,eAAK,CAAC,CAAC,OAAO,CAAE,CAAC,CAAC;SAClE;KACF;SAAM,IAAI,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE;QAC7C,mBAAmB;QACnB,IAAM,cAAc,GAAG,0BAA0B,CAAC,IAAI,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC;QACvE,IAAM,SAAS,GAAG,cAAc,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;QACjE,oBAAoB,CAAC,IAAI,CACvB,wBAAiB,SAAS,2BAAiB,CAAC,CAAC,OAAO,CAAE,CACvD,CAAC;KACH;SAAM,IAAI,CAAC,CAAC,YAAY,KAAK,EAAE,EAAE;QAChC,2DAA2D;QAC3D,oBAAoB,CAAC,IAAI,CAAC,mBAAY,CAAC,CAAC,OAAO,CAAE,CAAC,CAAC;KACpD;AACH,CAAC,CAAC;AAEF,IAAM,uBAAuB,GAAG,UAC9B,OAAe,EACf,MAA0B,EAC1B,YAA2B;IAE3B,IAAM,oBAAoB,GAAG,MAAM;SAChC,MAAM,CAAC,UAAC,KAAK,IAAK,OAAA,KAAK,CAAC,OAAO,KAAK,UAAU,EAA5B,CAA4B,CAAC;SAC/C,GAAG,CAAC,UAAC,KAAK;QACT,OAAO,KAAK,CAAC,MAAM,CAAC,eAAe,KAAK,SAAS;YAC/C,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,eAAe;YAC9B,CAAC,CAAC,SAAS,CAAC;IAChB,CAAC,CAAC,CAAC;IACL,IAAI,oBAAoB,CAAC,MAAM,GAAG,CAAC,EAAE;QACnC,IAAM,cAAc,GAAG,OAAO,CAC5B,OAAO,EACP,UAAG,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,+BACpC,oBAAoB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,UAAU,eACxD,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,MAAG,CACxC,CAAC;QACF,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;KACnC;AACH,CAAC,CAAC;AAEF,IAAM,WAAW,GAAG,UAClB,OAAe,EACf,MAA0B,EAC1B,YAA2B;IAE3B,IAAM,mBAAmB,GAAG,MAAM;SAC/B,MAAM,CAAC,UAAC,KAAK,IAAK,OAAA,KAAK,CAAC,OAAO,KAAK,KAAK,EAAvB,CAAuB,CAAC;SAC1C,GAAG,CAAC,UAAC,KAAK;QACT,OAAO,KAAK,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IACrC,CAAC,CAAC,CAAC;IACL,IAAI,mBAAmB,CAAC,MAAM,GAAG,CAAC,EAAE;QAClC,IAAM,YAAY,GAAG,OAAO,CAC1B,OAAO,EACP,wBACE,mBAAmB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,UAAU,eACvD,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,MAAG,CACvC,CAAC;QACF,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;KACjC;AACH,CAAC,CAAC;AAEF,IAAM,UAAU,GAAG,UACjB,OAAe,EACf,MAA0B,EAC1B,YAA2B;IAE3B,IAAM,cAAc,GAAkB,EAAE,CAAC;IACzC,IAAM,oBAAoB,GAAuB,MAAM,CAAC,MAAM,CAC5D,UAAC,KAAK;QACJ,OAAA,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;YACtC,KAAK,CAAC,OAAO,KAAK,OAAO;YACzB,KAAK,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC;IAF7B,CAE6B,CAChC,CAAC;IACF,IAAI,UAAU,GAAG,CAAC,CAAC;IAEnB,sCAAsC;IACtC,IAAM,eAAe,GAA0C,EAAE,CAAC;IAClE,IAAI,oBAAoB,CAAC,MAAM,GAAG,CAAC,EAAE;QACnC,KAAoB,UAAoB,EAApB,6CAAoB,EAApB,kCAAoB,EAApB,IAAoB,EAAE;YAArC,IAAM,KAAK,6BAAA;YACd,IAAM,UAAU,GAAG,KAAK,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAE/C,IAAI,eAAe,CAAC,UAAU,CAAC,EAAE;gBAC/B,eAAe,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aACzC;iBAAM;gBACL,eAAe,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;aACvC;SACF;QACD,IAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAEhD,UAAU,CAAC,OAAO,CAAC,UAAC,QAAQ;YAC1B,IAAM,gBAAgB,GAAG,eAAe,CAAC,QAAQ,CAAC,CAAC;YAEnD,cAAc,CAAC,IAAI,CACjB,oBAAa,QAAQ,gCAA6B;gBAChD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;qBAC5B,GAAG,CAAC,UAAC,KAAK,IAAK,OAAA,WAAI,KAAK,CAAC,MAAM,CAAC,YAAY,MAAG,EAAhC,CAAgC,CAAC;qBAChD,IAAI,CAAC,IAAI,CAAC,CAChB,CAAC;QACJ,CAAC,CAAC,CAAC;KACJ;IAED,yFAAyF;IACzF,IAAM,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;IAC/C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACtC,IAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;QAEpB,IAAI,CAAC,CAAC,OAAO,KAAK,MAAM,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;YACxE,UAAU,IAAI,CAAC,CAAC;YAChB,IAAI,UAAU,IAAI,SAAS,EAAE;gBAC3B,qBAAqB,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC;aAC1C;SACF;KACF;IAED,IAAI,UAAU,GAAG,SAAS,EAAE;QAC1B,cAAc,CAAC,IAAI,CAAC,cAAO,UAAU,GAAG,SAAS,kBAAe,CAAC,CAAC;KACnE;IAED,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE;QAC7B,IAAM,MAAM,GACV,YAAY,CAAC,MAAM,GAAG,CAAC;YACrB,CAAC,CAAC,8BAA8B;YAChC,CAAC,CAAC,UAAG,OAAO,sBAAmB,CAAC;QACpC,IAAM,gBAAgB,GAAG,OAAO,CAAC,MAAM,EAAE,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC;QAE1E,YAAY,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;KACrC;AACH,CAAC,CAAC;AAEF,IAAM,gBAAgB,GAAG,UACvB,OAAe,EACf,MAA0B,EAC1B,YAA2B;IAE3B,IAAM,gBAAgB,GAAkB,EAAE,CAAC;IAC3C,IAAI,UAAU,GAAG,CAAC,CAAC;IAEnB,4EAA4E;IAC5E,KAAgB,UAAM,EAAN,iBAAM,EAAN,oBAAM,EAAN,IAAM,EAAE;QAAnB,IAAM,CAAC,eAAA;QACV,IAAI,CAAC,CAAC,OAAO,KAAK,WAAW,IAAI,CAAC,CAAC,MAAM,CAAC,KAAK,KAAK,CAAC,EAAE;YACrD,CAAC,CAAC,OAAO,GAAG,mBAAmB,CAAC;SACjC;QAED,QAAQ,CAAC,CAAC,OAAO,EAAE;YACjB,KAAK,SAAS,CAAC;YACf,KAAK,SAAS,CAAC;YACf,KAAK,kBAAkB,CAAC;YACxB,KAAK,kBAAkB,CAAC;YACxB,KAAK,WAAW,CAAC;YACjB,KAAK,WAAW,CAAC;YACjB,KAAK,eAAe,CAAC;YACrB,KAAK,eAAe,CAAC;YACrB,KAAK,UAAU,CAAC;YAChB,KAAK,UAAU,CAAC;YAChB,KAAK,iBAAiB,CAAC;YACvB,KAAK,sBAAsB;gBACzB,UAAU,IAAI,CAAC,CAAC;gBAChB,IAAI,UAAU,GAAG,SAAS,EAAE;oBAC1B,SAAS;iBACV;qBAAM;oBACL,qBAAqB,CAAC,CAAC,EAAE,gBAAgB,CAAC,CAAC;iBAC5C;gBACD,MAAM;YACR,QAAQ;YACR,qDAAqD;SACtD;KACF;IAED,IAAI,UAAU,GAAG,SAAS,EAAE;QAC1B,gBAAgB,CAAC,IAAI,CAAC,cAAO,UAAU,GAAG,SAAS,kBAAe,CAAC,CAAC;KACrE;IAED,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE;QAC/B,IAAM,MAAM,GACV,YAAY,CAAC,MAAM,GAAG,CAAC;YACrB,CAAC,CAAC,oCAAoC;YACtC,CAAC,CAAC,UAAG,OAAO,4BAAyB,CAAC;QAC1C,IAAM,sBAAsB,GAC1B,OAAO,CAAC,MAAM,EAAE,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC;QAErD,YAAY,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;KAC3C;AACH,CAAC,CAAC;AAEK,IAAM,cAAc,GAAG,UAAC,OAAe,EAAE,MAA0B;IACxE,IAAM,mBAAmB,GAAG,MAAM,CAAC,MAAM,CACvC,UAAC,KAAK;QACJ,OAAA,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;YACtC,KAAK,CAAC,OAAO,KAAK,OAAO;YACzB,KAAK,CAAC,OAAO,KAAK,OAAO;YACzB,KAAK,CAAC,OAAO,KAAK,MAAM;IAHxB,CAGwB,CAC3B,CAAC;IAEF,IAAI,mBAAmB,CAAC,MAAM,GAAG,CAAC,EAAE;QAClC,IAAM,MAAM,GAAG,EAAE,CAAC;QAClB,KAAoB,UAAmB,EAAnB,2CAAmB,EAAnB,iCAAmB,EAAnB,IAAmB,EAAE;YAApC,IAAM,KAAK,4BAAA;YACd,IAAM,eAAe,GAAG,0BAA0B,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;YAC1E,IAAM,WAAW,GAAG,eAAe,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;YACrE,qEAAqE;YACrE,6CAA6C;YAC7C,KAAK,CAAC,UAAU,GAAG,eAAe,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;YAEpE,IAAI,MAAM,CAAC,WAAW,CAAC,EAAE;gBACvB,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aACjC;iBAAM;gBACL,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;aAC/B;SACF;QAED,qCAAqC;QACrC,OAAO,CACL,UAAG,OAAO,qCAAkC;YAC5C,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC;iBACnB,GAAG,CACF,UAAC,EAAY;oBAAX,GAAG,QAAA,EAAE,KAAK,QAAA;gBACV,OAAA,UAAG,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,MAAG;oBACvB,IAAA,sBAAc,EAAC,EAAE,EAAE,KAA2B,CAAC;YAD/C,CAC+C,CAClD;iBACA,IAAI,CAAC,GAAG,CAAC,CACb,CAAC;KACH;IAED,IAAM,YAAY,GAAkB,EAAE,CAAC;IAEvC,WAAW,CAAC,OAAO,EAAE,MAAM,EAAE,YAAY,CAAC,CAAC;IAC3C,uBAAuB,CAAC,OAAO,EAAE,MAAM,EAAE,YAAY,CAAC,CAAC;IACvD,UAAU,CAAC,OAAO,EAAE,MAAM,EAAE,YAAY,CAAC,CAAC;IAC1C,gBAAgB,CAAC,OAAO,EAAE,MAAM,EAAE,YAAY,CAAC,CAAC;IAEhD,OAAO,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAChC,CAAC,CAAC;AA9CW,QAAA,cAAc,kBA8CzB;AAEK,IAAM,cAAc,GAAG,UAAC,kBAA0B,EAAE,MAAW;IACpE,IAAI,IAAA,oBAAM,GAAE,EAAE;QACZ,4DAA4D;QAC5D,OAAO,UAAC,IAAS,IAAM,CAAC,CAAC,CAAC,sBAAsB;KACjD;IAED,IACE,OAAO,OAAO,KAAK,WAAW;QAC9B,OAAO;QACP,OAAO,CAAC,GAAG;QACX,OAAO,CAAC,GAAG,CAAC,oCAAoC,EAChD;QACA,6EAA6E;QAC7E,6EAA6E;QAC7E,+EAA+E;QAC/E,+EAA+E;QAC/E,2EAA2E;QAC3E,0BAA0B;QAC1B,EAAE;QACF,6EAA6E;QAC7E,iFAAiF;QACjF,4CAA4C;QAC5C,OAAO,UAAC,IAAS,IAAM,CAAC,CAAC,CAAC,sBAAsB;KACjD;IAED,IAAM,GAAG,GAAG,IAAI,aAAG,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;IACzC,IAAM,QAAQ,GAAG,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;IAErC,OAAO,UAAC,IAAS;QACf,IAAM,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;QAC7B,IAAI,CAAC,KAAK,EAAE;YACV,IAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,IAAK,EAAyB,CAAC;YAC7D,IAAM,GAAG,GAAG,IAAA,sBAAc,EAAC,kBAAkB,EAAE,MAAM,CAAC,CAAC;YACvD,MAAM,IAAI,8BAAqB,CAAC,GAAG,CAAC,CAAC;SACtC;QACD,OAAO,IAAI,CAAC;IACd,CAAC,CAAC;AACJ,CAAC,CAAC;AArCW,QAAA,cAAc,kBAqCzB;AAEK,IAAM,oBAAoB,GAAG,UAAC,MAAW,EAAE,UAAkB;IAClE,IAAM,MAAM,GAAG,0BAAmB,UAAU,CAAE,CAAC;IAC/C,OAAO,IAAA,sBAAc,EAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AACxC,CAAC,CAAC;AAHW,QAAA,oBAAoB,wBAG/B"}