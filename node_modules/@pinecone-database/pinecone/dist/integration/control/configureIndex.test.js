"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var index_1 = require("../../index");
var test_helpers_1 = require("../test-helpers");
// TODO: Re-enable
describe.skip('configure index', function () {
    var indexName;
    var pinecone;
    beforeEach(function () { return __awaiter(void 0, void 0, void 0, function () {
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    pinecone = new index_1.Pinecone();
                    indexName = (0, test_helpers_1.randomIndexName)('configureIndex');
                    return [4 /*yield*/, pinecone.createIndex({
                            name: indexName,
                            dimension: 5,
                            metric: 'cosine',
                            spec: {
                                pod: {
                                    environment: 'us-east1-gcp',
                                    podType: 'p1.x1',
                                    pods: 2,
                                },
                            },
                            waitUntilReady: true,
                        })];
                case 1:
                    _a.sent();
                    return [2 /*return*/];
            }
        });
    }); });
    afterEach(function () { return __awaiter(void 0, void 0, void 0, function () {
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0: return [4 /*yield*/, pinecone.deleteIndex(indexName)];
                case 1:
                    _a.sent();
                    return [2 /*return*/];
            }
        });
    }); });
    describe.skip('error handling', function () {
        test('configure index with invalid index name', function () { return __awaiter(void 0, void 0, void 0, function () {
            var e_1, err;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        _a.trys.push([0, 2, , 3]);
                        return [4 /*yield*/, pinecone.configureIndex('non-existent-index', {
                                replicas: 2,
                            })];
                    case 1:
                        _a.sent();
                        return [3 /*break*/, 3];
                    case 2:
                        e_1 = _a.sent();
                        err = e_1;
                        expect(err.name).toEqual('PineconeNotFoundError');
                        return [3 /*break*/, 3];
                    case 3: return [2 /*return*/];
                }
            });
        }); });
        test('configure index when exceeding quota', function () { return __awaiter(void 0, void 0, void 0, function () {
            var e_2, err;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        _a.trys.push([0, 2, , 3]);
                        return [4 /*yield*/, pinecone.configureIndex(indexName, {
                                replicas: 20,
                            })];
                    case 1:
                        _a.sent();
                        return [3 /*break*/, 3];
                    case 2:
                        e_2 = _a.sent();
                        err = e_2;
                        expect(err.name).toEqual('PineconeBadRequestError');
                        expect(err.message).toContain('The index exceeds the project quota');
                        expect(err.message).toContain('Upgrade your account or change the project settings to increase the quota.');
                        return [3 /*break*/, 3];
                    case 3: return [2 /*return*/];
                }
            });
        }); });
    });
    describe.skip('scaling replicas', function () {
        test('scaling up', function () { return __awaiter(void 0, void 0, void 0, function () {
            var description, description2;
            var _a, _b;
            return __generator(this, function (_c) {
                switch (_c.label) {
                    case 0: return [4 /*yield*/, pinecone.describeIndex(indexName)];
                    case 1:
                        description = _c.sent();
                        expect((_a = description.spec.pod) === null || _a === void 0 ? void 0 : _a.replicas).toEqual(2);
                        return [4 /*yield*/, pinecone.configureIndex(indexName, {
                                replicas: 3,
                            })];
                    case 2:
                        _c.sent();
                        return [4 /*yield*/, pinecone.describeIndex(indexName)];
                    case 3:
                        description2 = _c.sent();
                        expect((_b = description2.spec.pod) === null || _b === void 0 ? void 0 : _b.replicas).toEqual(3);
                        return [2 /*return*/];
                }
            });
        }); });
        test('scaling down', function () { return __awaiter(void 0, void 0, void 0, function () {
            var description, description3;
            var _a, _b;
            return __generator(this, function (_c) {
                switch (_c.label) {
                    case 0: return [4 /*yield*/, pinecone.describeIndex(indexName)];
                    case 1:
                        description = _c.sent();
                        expect((_a = description.spec.pod) === null || _a === void 0 ? void 0 : _a.replicas).toEqual(2);
                        return [4 /*yield*/, pinecone.configureIndex(indexName, {
                                replicas: 1,
                            })];
                    case 2:
                        _c.sent();
                        return [4 /*yield*/, pinecone.describeIndex(indexName)];
                    case 3:
                        description3 = _c.sent();
                        expect((_b = description3.spec.pod) === null || _b === void 0 ? void 0 : _b.replicas).toEqual(1);
                        return [2 /*return*/];
                }
            });
        }); });
    });
    describe.skip('scaling pod type', function () {
        test('scaling podType: changing base pod type', function () { return __awaiter(void 0, void 0, void 0, function () {
            var description, e_3, err;
            var _a;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, pinecone.describeIndex(indexName)];
                    case 1:
                        description = _b.sent();
                        expect((_a = description.spec.pod) === null || _a === void 0 ? void 0 : _a.podType).toEqual('p1.x1');
                        _b.label = 2;
                    case 2:
                        _b.trys.push([2, 4, , 5]);
                        // Try to change the base pod type
                        return [4 /*yield*/, pinecone.configureIndex(indexName, {
                                podType: 'p2.x1',
                            })];
                    case 3:
                        // Try to change the base pod type
                        _b.sent();
                        return [3 /*break*/, 5];
                    case 4:
                        e_3 = _b.sent();
                        err = e_3;
                        expect(err.name).toEqual('PineconeBadRequestError');
                        expect(err.message).toContain('updating base pod type is not supported');
                        return [3 /*break*/, 5];
                    case 5: return [2 /*return*/];
                }
            });
        }); });
        test('scaling podType: size increase', function () { return __awaiter(void 0, void 0, void 0, function () {
            var description, description2;
            var _a, _b;
            return __generator(this, function (_c) {
                switch (_c.label) {
                    case 0: return [4 /*yield*/, pinecone.describeIndex(indexName)];
                    case 1:
                        description = _c.sent();
                        expect((_a = description.spec.pod) === null || _a === void 0 ? void 0 : _a.podType).toEqual('p1.x1');
                        return [4 /*yield*/, pinecone.configureIndex(indexName, {
                                podType: 'p1.x2',
                            })];
                    case 2:
                        _c.sent();
                        return [4 /*yield*/, pinecone.describeIndex(indexName)];
                    case 3:
                        description2 = _c.sent();
                        expect((_b = description2.spec.pod) === null || _b === void 0 ? void 0 : _b.podType).toEqual('p1.x2');
                        return [2 /*return*/];
                }
            });
        }); });
        test('scaling podType: size down', function () { return __awaiter(void 0, void 0, void 0, function () {
            var description, description2, description3, e_4, err;
            var _a, _b, _c;
            return __generator(this, function (_d) {
                switch (_d.label) {
                    case 0: return [4 /*yield*/, pinecone.describeIndex(indexName)];
                    case 1:
                        description = _d.sent();
                        expect((_a = description.spec.pod) === null || _a === void 0 ? void 0 : _a.podType).toEqual('p1.x1');
                        // Size up
                        return [4 /*yield*/, pinecone.configureIndex(indexName, {
                                podType: 'p1.x2',
                            })];
                    case 2:
                        // Size up
                        _d.sent();
                        return [4 /*yield*/, pinecone.describeIndex(indexName)];
                    case 3:
                        description2 = _d.sent();
                        expect((_b = description2.spec.pod) === null || _b === void 0 ? void 0 : _b.podType).toEqual('p1.x2');
                        return [4 /*yield*/, (0, test_helpers_1.waitUntilReady)(indexName)];
                    case 4:
                        _d.sent();
                        _d.label = 5;
                    case 5:
                        _d.trys.push([5, 8, , 9]);
                        // try to size down
                        return [4 /*yield*/, pinecone.configureIndex(indexName, {
                                podType: 'p1.x1',
                            })];
                    case 6:
                        // try to size down
                        _d.sent();
                        return [4 /*yield*/, pinecone.describeIndex(indexName)];
                    case 7:
                        description3 = _d.sent();
                        expect((_c = description3.spec.pod) === null || _c === void 0 ? void 0 : _c.podType).toEqual('p1.x1');
                        return [3 /*break*/, 9];
                    case 8:
                        e_4 = _d.sent();
                        err = e_4;
                        if (err.name === 'PineconeBadRequestError') {
                            expect(err.message).toContain('scaling down pod type is not supported');
                        }
                        else if (err.name === 'PineconeInternalServerError') {
                            // noop. Seems like the API is sometimes returns 500 when scaling up
                            // and down in quick succession. But it's not a client issue so I
                            // don't want to fail the test in that case.
                        }
                        else {
                            throw err;
                        }
                        return [3 /*break*/, 9];
                    case 9: return [2 /*return*/];
                }
            });
        }); });
    });
});
//# sourceMappingURL=configureIndex.test.js.map