import { TravelAnalysisInput } from '../types';
export interface MCPRequest {
    type: 'flight' | 'hotel' | 'experience' | 'event' | 'weather' | 'analytics';
    locations: string[];
    dateRange?: {
        start: string;
        end: string;
    } | undefined;
    parameters?: Record<string, any>;
}
export interface MCPResponse {
    success: boolean;
    data: any;
    source: string;
    timestamp: number;
    error?: string;
}
export declare class MCPRouter {
    private flightConnector;
    private hotelConnector;
    private eventsConnector;
    private weatherConnector;
    private analyticsConnector;
    constructor();
    routeRequest(request: MCPRequest): Promise<MCPResponse>;
    fetchComprehensiveData(locations: string[], dateRange?: {
        start: string;
        end: string;
    }): Promise<TravelAnalysisInput>;
    private mergeResponseData;
    getHealthStatus(): Promise<Record<string, boolean>>;
    refreshCache(): Promise<void>;
}
//# sourceMappingURL=MCPRouter.d.ts.map