{"version": 3, "file": "MCPRouter.js", "sourceRoot": "", "sources": ["../../src/mcp/MCPRouter.ts"], "names": [], "mappings": ";;;AAAA,wEAAqE;AACrE,sEAAmE;AACnE,wEAAqE;AACrE,0EAAuE;AACvE,wEAAqE;AAqBrE,MAAa,SAAS;IAOpB;QACE,IAAI,CAAC,eAAe,GAAG,IAAI,uCAAkB,EAAE,CAAC;QAChD,IAAI,CAAC,cAAc,GAAG,IAAI,qCAAiB,EAAE,CAAC;QAC9C,IAAI,CAAC,eAAe,GAAG,IAAI,uCAAkB,EAAE,CAAC;QAChD,IAAI,CAAC,gBAAgB,GAAG,IAAI,yCAAmB,EAAE,CAAC;QAClD,IAAI,CAAC,kBAAkB,GAAG,IAAI,uCAAkB,EAAE,CAAC;IACrD,CAAC;IAKD,KAAK,CAAC,YAAY,CAAC,OAAmB;QACpC,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,wBAAwB,OAAO,CAAC,IAAI,mBAAmB,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAEnG,QAAQ,OAAO,CAAC,IAAI,EAAE,CAAC;gBACrB,KAAK,QAAQ;oBACX,OAAO,MAAM,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;gBACvD,KAAK,OAAO;oBACV,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;gBACtD,KAAK,YAAY;oBACf,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;gBACtD,KAAK,OAAO;oBACV,OAAO,MAAM,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;gBACvD,KAAK,SAAS;oBACZ,OAAO,MAAM,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;gBACxD,KAAK,WAAW;oBACd,OAAO,MAAM,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;gBAC1D;oBACE,MAAM,IAAI,KAAK,CAAC,6BAA6B,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;YACjE,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,yBAAyB,OAAO,CAAC,IAAI,GAAG,EAAE,KAAK,CAAC,CAAC;YAC/D,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,IAAI,EAAE,IAAI;gBACV,MAAM,EAAE,GAAG,OAAO,CAAC,IAAI,MAAM;gBAC7B,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;gBACrB,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;aAChE,CAAC;QACJ,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,sBAAsB,CAC1B,SAAmB,EACnB,SAA0C;QAE1C,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAC;QAErD,MAAM,QAAQ,GAAiB;YAC7B,EAAE,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,IAAI,SAAS,EAAE;YAChE,EAAE,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,IAAI,SAAS,EAAE;YAC/D,EAAE,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,IAAI,SAAS,EAAE;YAC/D,EAAE,IAAI,EAAE,WAAW,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,IAAI,SAAS,EAAE;SACpE,CAAC;QAGF,MAAM,SAAS,GAAG,MAAM,OAAO,CAAC,UAAU,CACxC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CACpD,CAAC;QAGF,MAAM,cAAc,GAAwB;YAC1C,oBAAoB,EAAE,EAAE;YACxB,mBAAmB,EAAE,EAAE;YACvB,wBAAwB,EAAE,EAAE;YAC5B,gBAAgB,EAAE,EAAE;YACpB,kBAAkB,EAAE,EAAE;YACtB,eAAe,EAAE;gBACf,OAAO,EAAE,EAAE,WAAW,EAAE,GAAG,EAAE,iBAAiB,EAAE,CAAC,EAAE;gBACnD,MAAM,EAAE,EAAE,WAAW,EAAE,GAAG,EAAE,iBAAiB,EAAE,CAAC,EAAE;gBAClD,WAAW,EAAE,EAAE,WAAW,EAAE,GAAG,EAAE,iBAAiB,EAAE,CAAC,EAAE;aACxD;YACD,MAAM,EAAE,EAAE;SACX,CAAC;QAEF,SAAS,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YAClC,IAAI,MAAM,CAAC,MAAM,KAAK,WAAW,IAAI,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;gBAC1D,MAAM,QAAQ,GAAG,MAAM,CAAC,KAAK,CAAC;gBAC9B,MAAM,WAAW,GAAG,QAAQ,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;gBAE1C,IAAI,CAAC,iBAAiB,CAAC,cAAc,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC;YAChE,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,IAAI,CAAC,mBAAmB,QAAQ,CAAC,KAAK,CAAC,EAAE,IAAI,QAAQ,EAC3D,MAAM,CAAC,MAAM,KAAK,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YACvE,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,cAAc,CAAC;IACxB,CAAC;IAKO,iBAAiB,CACvB,UAA+B,EAC/B,QAAqB,EACrB,WAAoB;QAEpB,MAAM,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC;QAE3B,QAAQ,WAAW,EAAE,CAAC;YACpB,KAAK,QAAQ;gBACX,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;oBAC9B,UAAU,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC,CAAC;gBACrE,CAAC;gBACD,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;oBAC1B,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;gBACpE,CAAC;gBACD,MAAM;YAER,KAAK,OAAO;gBACV,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;oBAC7B,UAAU,CAAC,mBAAmB,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC,CAAC;gBACnE,CAAC;gBACD,IAAI,IAAI,CAAC,wBAAwB,EAAE,CAAC;oBAClC,UAAU,CAAC,wBAAwB,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,wBAAwB,CAAC,CAAC;gBAC7E,CAAC;gBACD,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;oBAC1B,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;gBACpE,CAAC;gBACD,MAAM;YAER,KAAK,OAAO;gBACV,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;oBAChB,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC;gBACzC,CAAC;gBACD,MAAM;YAER,KAAK,WAAW;gBACd,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;oBAC5B,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,kBAAkB,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;gBACxE,CAAC;gBACD,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;oBACzB,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,eAAe,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;gBAClE,CAAC;gBACD,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;oBAC1B,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;gBACpE,CAAC;gBACD,MAAM;QACV,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,eAAe;QACnB,MAAM,UAAU,GAAG;YACjB,MAAM,EAAE,IAAI,CAAC,eAAe;YAC5B,KAAK,EAAE,IAAI,CAAC,cAAc;YAC1B,MAAM,EAAE,IAAI,CAAC,eAAe;YAC5B,OAAO,EAAE,IAAI,CAAC,gBAAgB;YAC9B,SAAS,EAAE,IAAI,CAAC,kBAAkB;SACnC,CAAC;QAEF,MAAM,YAAY,GAA4B,EAAE,CAAC;QAEjD,KAAK,MAAM,CAAC,IAAI,EAAE,SAAS,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC;YAC3D,IAAI,CAAC;gBACH,YAAY,CAAC,IAAI,CAAC,GAAG,MAAM,SAAS,CAAC,WAAW,EAAE,CAAC;YACrD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,2BAA2B,IAAI,GAAG,EAAE,KAAK,CAAC,CAAC;gBACzD,YAAY,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;YAC7B,CAAC;QACH,CAAC;QAED,OAAO,YAAY,CAAC;IACtB,CAAC;IAKD,KAAK,CAAC,YAAY;QAChB,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;QAElD,MAAM,UAAU,GAAG;YACjB,IAAI,CAAC,eAAe;YACpB,IAAI,CAAC,cAAc;YACnB,IAAI,CAAC,eAAe;YACpB,IAAI,CAAC,gBAAgB;YACrB,IAAI,CAAC,kBAAkB;SACxB,CAAC;QAEF,MAAM,OAAO,CAAC,UAAU,CACtB,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE;YACzB,IAAI,cAAc,IAAI,SAAS,IAAI,OAAO,SAAS,CAAC,YAAY,KAAK,UAAU,EAAE,CAAC;gBAChF,OAAO,SAAS,CAAC,YAAY,EAAE,CAAC;YAClC,CAAC;YACD,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;QAC3B,CAAC,CAAC,CACH,CAAC;QAEF,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAC;IACvD,CAAC;CACF;AA5MD,8BA4MC"}