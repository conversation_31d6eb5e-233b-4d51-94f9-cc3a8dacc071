import { MCPRequest, MCPResponse } from '../MCPRouter';
export declare class HotelAPIConnector {
    private baseUrl;
    private apiKey;
    private cache;
    private cacheTimeout;
    constructor();
    fetchData(request: MCPRequest): Promise<MCPResponse>;
    private fetchHotelSearchTrends;
    private fetchExperienceSearchTrends;
    private fetchHotelMetadata;
    private generateSyntheticHotelData;
    private determineLocationType;
    private addDays;
    private generateCacheKey;
    healthCheck(): Promise<boolean>;
    refreshCache(): Promise<void>;
}
//# sourceMappingURL=HotelAPIConnector.d.ts.map