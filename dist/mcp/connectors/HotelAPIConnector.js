"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.HotelAPIConnector = void 0;
class HotelAPIConnector {
    constructor() {
        this.cache = new Map();
        this.cacheTimeout = 10 * 60 * 1000;
        this.baseUrl = process.env.HOTEL_API_BASE_URL || 'https://api.booking.com';
        this.apiKey = process.env.HOTEL_API_KEY || '';
    }
    async fetchData(request) {
        try {
            const cacheKey = this.generateCacheKey(request);
            if (this.cache.has(cacheKey)) {
                const cached = this.cache.get(cacheKey);
                if (Date.now() - cached.timestamp < this.cacheTimeout) {
                    console.log('Returning cached hotel data');
                    return {
                        success: true,
                        data: cached.data,
                        source: 'hotel_api_cache',
                        timestamp: cached.timestamp,
                    };
                }
            }
            const hotelData = await this.fetchHotelSearchTrends(request);
            const experienceData = await this.fetchExperienceSearchTrends(request);
            const contentMetadata = await this.fetchHotelMetadata(request);
            const responseData = {
                hotel_search_trends: hotelData,
                experience_search_trends: experienceData,
                content_metadata: contentMetadata,
            };
            this.cache.set(cacheKey, {
                data: responseData,
                timestamp: Date.now(),
            });
            return {
                success: true,
                data: responseData,
                source: 'hotel_api',
                timestamp: Date.now(),
            };
        }
        catch (error) {
            console.error('Hotel API error:', error);
            return {
                success: true,
                data: this.generateSyntheticHotelData(request),
                source: 'hotel_api_synthetic',
                timestamp: Date.now(),
            };
        }
    }
    async fetchHotelSearchTrends(request) {
        const trends = [];
        for (const location of request.locations) {
            const checkinDate = request.dateRange?.start || new Date().toISOString().split('T')[0];
            const checkoutDate = request.dateRange?.end || this.addDays(checkinDate, 3);
            trends.push({
                city: location,
                checkin_date: checkinDate,
                checkout_date: checkoutDate,
                rooms: Math.floor(Math.random() * 3) + 1,
                guests: {
                    adult: Math.floor(Math.random() * 4) + 1,
                    child: Math.floor(Math.random() * 2),
                    child_ages: Math.random() > 0.5 ? [Math.floor(Math.random() * 12) + 3] : [],
                },
                search_count: Math.floor(Math.random() * 12000) + 3000,
                booking_conversion: Math.random() * 0.35 + 0.15,
            });
        }
        return trends;
    }
    async fetchExperienceSearchTrends(request) {
        const experiences = [];
        for (const location of request.locations) {
            const checkinDate = request.dateRange?.start || new Date().toISOString().split('T')[0];
            const checkoutDate = request.dateRange?.end || checkinDate;
            const locationType = this.determineLocationType(location);
            const experience = {
                checkin_date: checkinDate,
                checkout_date: checkoutDate,
                search_count: Math.floor(Math.random() * 8000) + 2000,
            };
            if (locationType === 'city') {
                experience.city = location;
            }
            else if (locationType === 'region') {
                experience.region = location;
            }
            else {
                experience.country = location;
            }
            experiences.push(experience);
        }
        return experiences;
    }
    async fetchHotelMetadata(request) {
        const metadata = {};
        for (const location of request.locations) {
            const accommodationTypes = [
                `${location} hotels`,
                `${location} 3-star hotels`,
                `${location} 4-star hotels`,
                `${location} resorts`,
                `${location} cottages`,
                `${location} villas`,
            ];
            const selectedTypes = accommodationTypes.slice(0, Math.floor(Math.random() * 3) + 1);
            for (const type of selectedTypes) {
                metadata[type] = {
                    avg_price: `₹${Math.floor(Math.random() * 8000) + 1500}`,
                    rating: Math.round((Math.random() * 2 + 3) * 10) / 10,
                    inventory_status: Math.random() > 0.7 ? 'Limited' : 'Available',
                };
            }
        }
        return metadata;
    }
    generateSyntheticHotelData(request) {
        const hotelTrends = [];
        const experienceTrends = [];
        const metadata = {};
        for (const location of request.locations) {
            const checkinDate = request.dateRange?.start || new Date().toISOString().split('T')[0];
            const checkoutDate = request.dateRange?.end || this.addDays(checkinDate, 2);
            hotelTrends.push({
                city: location,
                checkin_date: checkinDate,
                checkout_date: checkoutDate,
                rooms: 1,
                guests: { adult: 2, child: 0 },
                search_count: 5000 + Math.floor(Math.random() * 3000),
                booking_conversion: 0.25 + Math.random() * 0.15,
            });
            experienceTrends.push({
                city: location,
                checkin_date: checkinDate,
                checkout_date: checkoutDate,
                search_count: 3000 + Math.floor(Math.random() * 2000),
            });
            metadata[`${location} hotels`] = {
                avg_price: `₹${Math.floor(Math.random() * 5000) + 2000}`,
                rating: Math.round((Math.random() * 1.5 + 3.5) * 10) / 10,
                inventory_status: 'Available',
            };
        }
        return {
            hotel_search_trends: hotelTrends,
            experience_search_trends: experienceTrends,
            content_metadata: metadata,
        };
    }
    determineLocationType(location) {
        const regions = ['Dubai Desert', 'Sahara', 'Alps', 'Himalayas', 'Caribbean'];
        const countries = ['Thailand', 'India', 'USA', 'France', 'Japan', 'Australia'];
        if (regions.some(region => location.includes(region))) {
            return 'region';
        }
        if (countries.includes(location)) {
            return 'country';
        }
        return 'city';
    }
    addDays(dateString, days) {
        const date = new Date(dateString);
        date.setDate(date.getDate() + days);
        return date.toISOString().split('T')[0];
    }
    generateCacheKey(request) {
        return `hotel_${request.locations.join('_')}_${request.dateRange?.start || 'no_date'}`;
    }
    async healthCheck() {
        try {
            return true;
        }
        catch (error) {
            console.error('Hotel API health check failed:', error);
            return false;
        }
    }
    async refreshCache() {
        this.cache.clear();
        console.log('Hotel API cache cleared');
    }
}
exports.HotelAPIConnector = HotelAPIConnector;
//# sourceMappingURL=HotelAPIConnector.js.map