{"version": 3, "file": "WeatherAPIConnector.js", "sourceRoot": "", "sources": ["../../../src/mcp/connectors/WeatherAPIConnector.ts"], "names": [], "mappings": ";;;AAGA,MAAa,mBAAmB;IAM9B;QAHQ,UAAK,GAAqB,IAAI,GAAG,EAAE,CAAC;QACpC,iBAAY,GAAW,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;QAG5C,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,oBAAoB,IAAI,gCAAgC,CAAC;QACpF,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,eAAe,IAAI,EAAE,CAAC;IAClD,CAAC;IAKD,KAAK,CAAC,SAAS,CAAC,OAAmB;QACjC,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;YAGhD,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC7B,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;gBACxC,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;oBACtD,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;oBAC7C,OAAO;wBACL,OAAO,EAAE,IAAI;wBACb,IAAI,EAAE,MAAM,CAAC,IAAI;wBACjB,MAAM,EAAE,mBAAmB;wBAC3B,SAAS,EAAE,MAAM,CAAC,SAAS;qBAC5B,CAAC;gBACJ,CAAC;YACH,CAAC;YAGD,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,CAAC;YAEjE,MAAM,YAAY,GAAG;gBACnB,YAAY,EAAE,WAAW;aAC1B,CAAC;YAGF,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,EAAE;gBACvB,IAAI,EAAE,YAAY;gBAClB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;aACtB,CAAC,CAAC;YAEH,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE,YAAY;gBAClB,MAAM,EAAE,aAAa;gBACrB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;aACtB,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,oBAAoB,EAAE,KAAK,CAAC,CAAC;YAG3C,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE,IAAI,CAAC,4BAA4B,CAAC,OAAO,CAAC;gBAChD,MAAM,EAAE,uBAAuB;gBAC/B,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;aACtB,CAAC;QACJ,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,wBAAwB,CAAC,OAAmB;QACxD,MAAM,WAAW,GAAG,EAAE,CAAC;QAEvB,KAAK,MAAM,QAAQ,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;YACzC,IAAI,CAAC;gBAEH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,QAAQ,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;gBAC9E,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC5B,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,IAAI,CAAC,+BAA+B,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;gBAEhE,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAC,CAAC;YAC3D,CAAC;QACH,CAAC;QAED,OAAO,WAAW,CAAC;IACrB,CAAC;IAKO,KAAK,CAAC,qBAAqB,CAAC,QAAgB,EAAE,SAA0C;QAE9F,MAAM,iBAAiB,GAAG,CAAC,OAAO,EAAE,eAAe,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;QAClF,MAAM,SAAS,GAAG,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC;QAG1F,MAAM,QAAQ,GAAG,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;QACnD,MAAM,WAAW,GAAG,QAAQ,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;QAExD,OAAO;YACL,QAAQ;YACR,IAAI,EAAE,SAAS,EAAE,KAAK,IAAI,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAChE,SAAS;YACT,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC;YACpC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE;YAC7C,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC;YAC9C,oBAAoB,EAAE,IAAI,CAAC,sBAAsB,CAAC,SAAS,CAAC;YAC5D,aAAa,EAAE,IAAI,CAAC,kBAAkB,CAAC,SAAS,EAAE,WAAW,CAAC;SAC/D,CAAC;IACJ,CAAC;IAKO,kBAAkB,CAAC,QAAgB;QACzC,MAAM,aAAa,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC;QAG7C,MAAM,cAAc,GAA2B;YAC7C,QAAQ,EAAE,EAAE;YACZ,OAAO,EAAE,EAAE;YACX,WAAW,EAAE,EAAE;YACf,KAAK,EAAE,EAAE;YACT,QAAQ,EAAE,EAAE;YACZ,QAAQ,EAAE,EAAE;YACZ,OAAO,EAAE,EAAE;YACX,OAAO,EAAE,EAAE;YACX,QAAQ,EAAE,EAAE;YACZ,SAAS,EAAE,EAAE;YACb,WAAW,EAAE,EAAE;YACf,OAAO,EAAE,EAAE;YACX,QAAQ,EAAE,EAAE;YACZ,UAAU,EAAE,EAAE;YACd,aAAa,EAAE,EAAE;SAClB,CAAC;QAEF,KAAK,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,EAAE,CAAC;YAC1D,IAAI,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;gBACjC,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;QAGD,OAAO,EAAE,CAAC;IACZ,CAAC;IAKO,sBAAsB,CAAC,SAAiB;QAC9C,QAAQ,SAAS,EAAE,CAAC;YAClB,KAAK,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC;YACvB,KAAK,eAAe,CAAC,CAAC,OAAO,EAAE,CAAC;YAChC,KAAK,QAAQ,CAAC,CAAC,OAAO,EAAE,CAAC;YACzB,KAAK,OAAO,CAAC,CAAC,OAAO,EAAE,CAAC;YACxB,KAAK,QAAQ,CAAC,CAAC,OAAO,EAAE,CAAC;YACzB,OAAO,CAAC,CAAC,OAAO,EAAE,CAAC;QACrB,CAAC;IACH,CAAC;IAKO,kBAAkB,CAAC,SAAiB,EAAE,WAAmB;QAC/D,IAAI,SAAS,KAAK,QAAQ,IAAI,SAAS,KAAK,OAAO,EAAE,CAAC;YACpD,OAAO,aAAa,CAAC;QACvB,CAAC;QAED,IAAI,WAAW,GAAG,EAAE,IAAI,WAAW,GAAG,CAAC,EAAE,CAAC;YACxC,OAAO,eAAe,CAAC;QACzB,CAAC;QAED,IAAI,SAAS,KAAK,QAAQ,EAAE,CAAC;YAC3B,OAAO,YAAY,CAAC;QACtB,CAAC;QAED,OAAO,gBAAgB,CAAC;IAC1B,CAAC;IAKO,uBAAuB,CAAC,QAAgB;QAC9C,MAAM,QAAQ,GAAG,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;QACnD,MAAM,UAAU,GAAG,CAAC,OAAO,EAAE,eAAe,EAAE,QAAQ,CAAC,CAAC;QACxD,MAAM,SAAS,GAAG,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;QAE5E,OAAO;YACL,QAAQ;YACR,IAAI,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC5C,SAAS;YACT,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;YAC3D,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE;YAC7C,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC;YAC9C,oBAAoB,EAAE,IAAI,CAAC,sBAAsB,CAAC,SAAU,CAAC;YAC7D,aAAa,EAAE,gBAAgB;SAChC,CAAC;IACJ,CAAC;IAKO,4BAA4B,CAAC,OAAmB;QACtD,MAAM,WAAW,GAAG,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CACnD,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CACvC,CAAC;QAEF,OAAO,EAAE,YAAY,EAAE,WAAW,EAAE,CAAC;IACvC,CAAC;IAKO,gBAAgB,CAAC,OAAmB;QAC1C,OAAO,WAAW,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,OAAO,CAAC,SAAS,EAAE,KAAK,IAAI,SAAS,EAAE,CAAC;IAC3F,CAAC;IAKD,KAAK,CAAC,WAAW;QACf,IAAI,CAAC;YAEH,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;YACzD,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,YAAY;QAChB,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;QACnB,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;IAC3C,CAAC;CACF;AA3OD,kDA2OC"}