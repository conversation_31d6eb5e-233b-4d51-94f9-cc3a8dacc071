import { MCPRequest, MCPResponse } from '../MCPRouter';
export declare class AnalyticsConnector {
    private baseUrl;
    private apiKey;
    private cache;
    private cacheTimeout;
    constructor();
    fetchData(request: MCPRequest): Promise<MCPResponse>;
    private fetchBookingConversion;
    private fetchAPIPerformance;
    private fetchContentMetadata;
    private generateConversionRate;
    private generateLatency;
    private generateSyntheticAnalyticsData;
    private generateCacheKey;
    healthCheck(): Promise<boolean>;
    refreshCache(): Promise<void>;
}
//# sourceMappingURL=AnalyticsConnector.d.ts.map