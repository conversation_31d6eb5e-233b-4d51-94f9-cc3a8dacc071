import { MCPRequest, MCPResponse } from '../MCPRouter';
export declare class EventsAPIConnector {
    private baseUrl;
    private apiKey;
    private cache;
    private cacheTimeout;
    constructor();
    fetchData(request: MCPRequest): Promise<MCPResponse>;
    private fetchEvents;
    private getHolidaysInRange;
    private getLocationEvents;
    private generateSyntheticEventsData;
    private generateCacheKey;
    healthCheck(): Promise<boolean>;
    refreshCache(): Promise<void>;
}
//# sourceMappingURL=EventsAPIConnector.d.ts.map