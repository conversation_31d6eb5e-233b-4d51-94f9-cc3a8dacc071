{"version": 3, "file": "HotelAPIConnector.js", "sourceRoot": "", "sources": ["../../../src/mcp/connectors/HotelAPIConnector.ts"], "names": [], "mappings": ";;;AAGA,MAAa,iBAAiB;IAM5B;QAHQ,UAAK,GAAqB,IAAI,GAAG,EAAE,CAAC;QACpC,iBAAY,GAAW,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;QAG5C,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,kBAAkB,IAAI,yBAAyB,CAAC;QAC3E,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,aAAa,IAAI,EAAE,CAAC;IAChD,CAAC;IAKD,KAAK,CAAC,SAAS,CAAC,OAAmB;QACjC,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;YAGhD,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC7B,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;gBACxC,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;oBACtD,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;oBAC3C,OAAO;wBACL,OAAO,EAAE,IAAI;wBACb,IAAI,EAAE,MAAM,CAAC,IAAI;wBACjB,MAAM,EAAE,iBAAiB;wBACzB,SAAS,EAAE,MAAM,CAAC,SAAS;qBAC5B,CAAC;gBACJ,CAAC;YACH,CAAC;YAGD,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;YAC7D,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,2BAA2B,CAAC,OAAO,CAAC,CAAC;YACvE,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;YAE/D,MAAM,YAAY,GAAG;gBACnB,mBAAmB,EAAE,SAAS;gBAC9B,wBAAwB,EAAE,cAAc;gBACxC,gBAAgB,EAAE,eAAe;aAClC,CAAC;YAGF,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,EAAE;gBACvB,IAAI,EAAE,YAAY;gBAClB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;aACtB,CAAC,CAAC;YAEH,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE,YAAY;gBAClB,MAAM,EAAE,WAAW;gBACnB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;aACtB,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;YAGzC,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC;gBAC9C,MAAM,EAAE,qBAAqB;gBAC7B,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;aACtB,CAAC;QACJ,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,sBAAsB,CAAC,OAAmB;QACtD,MAAM,MAAM,GAAG,EAAE,CAAC;QAElB,KAAK,MAAM,QAAQ,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;YACzC,MAAM,WAAW,GAAG,OAAO,CAAC,SAAS,EAAE,KAAK,IAAI,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YACvF,MAAM,YAAY,GAAG,OAAO,CAAC,SAAS,EAAE,GAAG,IAAI,IAAI,CAAC,OAAO,CAAC,WAAY,EAAE,CAAC,CAAC,CAAC;YAE7E,MAAM,CAAC,IAAI,CAAC;gBACV,IAAI,EAAE,QAAQ;gBACd,YAAY,EAAE,WAAW;gBACzB,aAAa,EAAE,YAAY;gBAC3B,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC;gBACxC,MAAM,EAAE;oBACN,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC;oBACxC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;oBACpC,UAAU,EAAE,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;iBAC5E;gBACD,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,KAAK,CAAC,GAAG,IAAI;gBACtD,kBAAkB,EAAE,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,GAAG,IAAI;aAChD,CAAC,CAAC;QACL,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAKO,KAAK,CAAC,2BAA2B,CAAC,OAAmB;QAC3D,MAAM,WAAW,GAAG,EAAE,CAAC;QAEvB,KAAK,MAAM,QAAQ,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;YACzC,MAAM,WAAW,GAAG,OAAO,CAAC,SAAS,EAAE,KAAK,IAAI,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YACvF,MAAM,YAAY,GAAG,OAAO,CAAC,SAAS,EAAE,GAAG,IAAI,WAAW,CAAC;YAG3D,MAAM,YAAY,GAAG,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;YAE1D,MAAM,UAAU,GAAQ;gBACtB,YAAY,EAAE,WAAW;gBACzB,aAAa,EAAE,YAAY;gBAC3B,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,IAAI;aACtD,CAAC;YAEF,IAAI,YAAY,KAAK,MAAM,EAAE,CAAC;gBAC5B,UAAU,CAAC,IAAI,GAAG,QAAQ,CAAC;YAC7B,CAAC;iBAAM,IAAI,YAAY,KAAK,QAAQ,EAAE,CAAC;gBACrC,UAAU,CAAC,MAAM,GAAG,QAAQ,CAAC;YAC/B,CAAC;iBAAM,CAAC;gBACN,UAAU,CAAC,OAAO,GAAG,QAAQ,CAAC;YAChC,CAAC;YAED,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC/B,CAAC;QAED,OAAO,WAAW,CAAC;IACrB,CAAC;IAKO,KAAK,CAAC,kBAAkB,CAAC,OAAmB;QAClD,MAAM,QAAQ,GAAwB,EAAE,CAAC;QAEzC,KAAK,MAAM,QAAQ,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;YAEzC,MAAM,kBAAkB,GAAG;gBACzB,GAAG,QAAQ,SAAS;gBACpB,GAAG,QAAQ,gBAAgB;gBAC3B,GAAG,QAAQ,gBAAgB;gBAC3B,GAAG,QAAQ,UAAU;gBACrB,GAAG,QAAQ,WAAW;gBACtB,GAAG,QAAQ,SAAS;aACrB,CAAC;YAEF,MAAM,aAAa,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YAErF,KAAK,MAAM,IAAI,IAAI,aAAa,EAAE,CAAC;gBACjC,QAAQ,CAAC,IAAI,CAAC,GAAG;oBACf,SAAS,EAAE,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,IAAI,EAAE;oBACxD,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE;oBACrD,gBAAgB,EAAE,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,WAAW;iBAChE,CAAC;YACJ,CAAC;QACH,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAKO,0BAA0B,CAAC,OAAmB;QACpD,MAAM,WAAW,GAAG,EAAE,CAAC;QACvB,MAAM,gBAAgB,GAAG,EAAE,CAAC;QAC5B,MAAM,QAAQ,GAAwB,EAAE,CAAC;QAEzC,KAAK,MAAM,QAAQ,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;YACzC,MAAM,WAAW,GAAG,OAAO,CAAC,SAAS,EAAE,KAAK,IAAI,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YACvF,MAAM,YAAY,GAAG,OAAO,CAAC,SAAS,EAAE,GAAG,IAAI,IAAI,CAAC,OAAO,CAAC,WAAY,EAAE,CAAC,CAAC,CAAC;YAG7E,WAAW,CAAC,IAAI,CAAC;gBACf,IAAI,EAAE,QAAQ;gBACd,YAAY,EAAE,WAAW;gBACzB,aAAa,EAAE,YAAY;gBAC3B,KAAK,EAAE,CAAC;gBACR,MAAM,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE;gBAC9B,YAAY,EAAE,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC;gBACrD,kBAAkB,EAAE,IAAI,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI;aAChD,CAAC,CAAC;YAGH,gBAAgB,CAAC,IAAI,CAAC;gBACpB,IAAI,EAAE,QAAQ;gBACd,YAAY,EAAE,WAAW;gBACzB,aAAa,EAAE,YAAY;gBAC3B,YAAY,EAAE,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC;aACtD,CAAC,CAAC;YAGH,QAAQ,CAAC,GAAG,QAAQ,SAAS,CAAC,GAAG;gBAC/B,SAAS,EAAE,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,IAAI,EAAE;gBACxD,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE;gBACzD,gBAAgB,EAAE,WAAW;aAC9B,CAAC;QACJ,CAAC;QAED,OAAO;YACL,mBAAmB,EAAE,WAAW;YAChC,wBAAwB,EAAE,gBAAgB;YAC1C,gBAAgB,EAAE,QAAQ;SAC3B,CAAC;IACJ,CAAC;IAKO,qBAAqB,CAAC,QAAgB;QAC5C,MAAM,OAAO,GAAG,CAAC,cAAc,EAAE,QAAQ,EAAE,MAAM,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC;QAC7E,MAAM,SAAS,GAAG,CAAC,UAAU,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC;QAE/E,IAAI,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC;YACtD,OAAO,QAAQ,CAAC;QAClB,CAAC;QAED,IAAI,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YACjC,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAKO,OAAO,CAAC,UAAkB,EAAE,IAAY;QAC9C,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC;QAClC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC;QACpC,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAE,CAAC;IAC3C,CAAC;IAKO,gBAAgB,CAAC,OAAmB;QAC1C,OAAO,SAAS,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,OAAO,CAAC,SAAS,EAAE,KAAK,IAAI,SAAS,EAAE,CAAC;IACzF,CAAC;IAKD,KAAK,CAAC,WAAW;QACf,IAAI,CAAC;YAEH,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;YACvD,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,YAAY;QAChB,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;QACnB,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC;IACzC,CAAC;CACF;AApQD,8CAoQC"}