{"version": 3, "file": "FlightAPIConnector.js", "sourceRoot": "", "sources": ["../../../src/mcp/connectors/FlightAPIConnector.ts"], "names": [], "mappings": ";;;AAGA,MAAa,kBAAkB;IAM7B;QAHQ,UAAK,GAAqB,IAAI,GAAG,EAAE,CAAC;QACpC,iBAAY,GAAW,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC;QAG3C,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI,yBAAyB,CAAC;QAC5E,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI,EAAE,CAAC;IACjD,CAAC;IAKD,KAAK,CAAC,SAAS,CAAC,OAAmB;QACjC,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;YAGhD,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC7B,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;gBACxC,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;oBACtD,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;oBAC5C,OAAO;wBACL,OAAO,EAAE,IAAI;wBACb,IAAI,EAAE,MAAM,CAAC,IAAI;wBACjB,MAAM,EAAE,kBAAkB;wBAC1B,SAAS,EAAE,MAAM,CAAC,SAAS;qBAC5B,CAAC;gBACJ,CAAC;YACH,CAAC;YAGD,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAC;YAC/D,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;YAEhE,MAAM,YAAY,GAAG;gBACnB,oBAAoB,EAAE,UAAU;gBAChC,gBAAgB,EAAE,eAAe;aAClC,CAAC;YAGF,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,EAAE;gBACvB,IAAI,EAAE,YAAY;gBAClB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;aACtB,CAAC,CAAC;YAEH,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE,YAAY;gBAClB,MAAM,EAAE,YAAY;gBACpB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;aACtB,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,mBAAmB,EAAE,KAAK,CAAC,CAAC;YAG1C,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE,IAAI,CAAC,2BAA2B,CAAC,OAAO,CAAC;gBAC/C,MAAM,EAAE,sBAAsB;gBAC9B,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;aACtB,CAAC;QACJ,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,uBAAuB,CAAC,OAAmB;QAIvD,MAAM,MAAM,GAAG,EAAE,CAAC;QAClB,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;QAEpC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YAC9C,MAAM,QAAQ,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;YAC9B,MAAM,MAAM,GAAG,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YAEhC,IAAI,QAAQ,IAAI,MAAM,EAAE,CAAC;gBACvB,MAAM,CAAC,IAAI,CAAC;oBACV,SAAS,EAAE,QAAQ;oBACnB,OAAO,EAAE,MAAM;oBACf,SAAS,EAAE,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,SAAS;oBACzD,UAAU,EAAE,OAAO,CAAC,SAAS,EAAE,KAAK,IAAI,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;oBAC9E,QAAQ,EAAE,OAAO,CAAC,SAAS,EAAE,GAAG;oBAChC,GAAG,EAAE;wBACH,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC;wBACxC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;wBACpC,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;qBACtC;oBACD,aAAa,EAAE,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG;oBAClC,SAAS,EAAE,SAAS;oBACpB,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,KAAK,CAAC,GAAG,IAAI;oBACtD,kBAAkB,EAAE,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,IAAI;iBAC/C,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAGD,IAAI,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YAChE,MAAM,CAAC,IAAI,CAAC;gBACV,SAAS,EAAE,QAAQ;gBACnB,OAAO,EAAE,OAAO;gBAChB,SAAS,EAAE,SAAS;gBACpB,UAAU,EAAE,OAAO,CAAC,SAAS,EAAE,KAAK,IAAI,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gBAC9E,GAAG,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE;gBACtC,aAAa,EAAE,IAAI;gBACnB,SAAS,EAAE,SAAS;gBACpB,YAAY,EAAE,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC;gBACtD,kBAAkB,EAAE,IAAI,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI;aAChD,CAAC,CAAC;QACL,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAKO,KAAK,CAAC,mBAAmB,CAAC,OAAmB;QACnD,MAAM,QAAQ,GAAwB,EAAE,CAAC;QAGzC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YACtD,MAAM,QAAQ,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YACtC,MAAM,MAAM,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YAExC,IAAI,QAAQ,IAAI,MAAM,EAAE,CAAC;gBACvB,MAAM,QAAQ,GAAG,GAAG,QAAQ,IAAI,MAAM,UAAU,CAAC;gBACjD,QAAQ,CAAC,QAAQ,CAAC,GAAG;oBACnB,SAAS,EAAE,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,IAAI,EAAE;oBACxD,QAAQ,EAAE,IAAI,CAAC,iBAAiB,EAAE;oBAClC,gBAAgB,EAAE,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,WAAW;iBAChE,CAAC;YACJ,CAAC;QACH,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAKO,2BAA2B,CAAC,OAAmB;QACrD,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;QACpC,MAAM,MAAM,GAAG,EAAE,CAAC;QAClB,MAAM,QAAQ,GAAwB,EAAE,CAAC;QAGzC,SAAS,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,KAAK,EAAE,EAAE;YACpC,IAAI,KAAK,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACjC,MAAM,YAAY,GAAG,SAAS,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;gBAC1C,IAAI,YAAY,EAAE,CAAC;oBACjB,MAAM,CAAC,IAAI,CAAC;wBACV,SAAS,EAAE,QAAQ;wBACnB,OAAO,EAAE,YAAY;wBACrB,SAAS,EAAE,YAAY;wBACvB,UAAU,EAAE,OAAO,CAAC,SAAS,EAAE,KAAK,IAAI,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;wBAC9E,GAAG,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE;wBACtC,aAAa,EAAE,IAAI;wBACnB,SAAS,EAAE,SAAS;wBACpB,YAAY,EAAE,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC;wBACrD,kBAAkB,EAAE,IAAI,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG;qBAC/C,CAAC,CAAC;oBAEH,QAAQ,CAAC,GAAG,QAAQ,IAAI,YAAY,UAAU,CAAC,GAAG;wBAChD,SAAS,EAAE,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,IAAI,EAAE;wBACxD,QAAQ,EAAE,IAAI,CAAC,iBAAiB,EAAE;wBAClC,gBAAgB,EAAE,WAAW;qBAC9B,CAAC;gBACJ,CAAC;YACH,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO;YACL,oBAAoB,EAAE,MAAM;YAC5B,gBAAgB,EAAE,QAAQ;SAC3B,CAAC;IACJ,CAAC;IAKO,iBAAiB;QACvB,MAAM,QAAQ,GAAG;YACf,QAAQ,EAAE,WAAW,EAAE,UAAU,EAAE,SAAS,EAAE,OAAO,EAAE,eAAe;YACtE,UAAU,EAAE,eAAe,EAAE,oBAAoB,EAAE,WAAW;YAC9D,iBAAiB,EAAE,YAAY,EAAE,KAAK,EAAE,kBAAkB;SAC3D,CAAC;QAEF,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;QAChD,MAAM,QAAQ,GAAG,EAAE,CAAC;QAEpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC;YAC/B,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;YAChE,MAAM,OAAO,GAAG,QAAQ,CAAC,WAAW,CAAC,CAAC;YACtC,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;gBAC3C,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACzB,CAAC;QACH,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAKO,gBAAgB,CAAC,OAAmB;QAC1C,OAAO,UAAU,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,OAAO,CAAC,SAAS,EAAE,KAAK,IAAI,SAAS,EAAE,CAAC;IAC1F,CAAC;IAKD,KAAK,CAAC,WAAW;QACf,IAAI,CAAC;YAGH,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;YACxD,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,YAAY;QAChB,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;QACnB,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;IAC1C,CAAC;CACF;AA1OD,gDA0OC"}