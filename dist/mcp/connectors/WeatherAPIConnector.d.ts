import { MCPRequest, MCPResponse } from '../MCPRouter';
export declare class WeatherAPIConnector {
    private baseUrl;
    private apiKey;
    private cache;
    private cacheTimeout;
    constructor();
    fetchData(request: MCPRequest): Promise<MCPResponse>;
    private fetchWeatherForLocations;
    private getWeatherForLocation;
    private getBaseTemperature;
    private getPrecipitationChance;
    private assessTravelImpact;
    private generateLocationWeather;
    private generateSyntheticWeatherData;
    private generateCacheKey;
    healthCheck(): Promise<boolean>;
    refreshCache(): Promise<void>;
}
//# sourceMappingURL=WeatherAPIConnector.d.ts.map