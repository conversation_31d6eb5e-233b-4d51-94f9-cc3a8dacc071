"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventsAPIConnector = void 0;
class EventsAPIConnector {
    constructor() {
        this.cache = new Map();
        this.cacheTimeout = 60 * 60 * 1000;
        this.baseUrl = process.env.EVENTS_API_BASE_URL || 'https://api.eventbrite.com';
        this.apiKey = process.env.EVENTS_API_KEY || '';
    }
    async fetchData(request) {
        try {
            const cacheKey = this.generateCacheKey(request);
            if (this.cache.has(cacheKey)) {
                const cached = this.cache.get(cacheKey);
                if (Date.now() - cached.timestamp < this.cacheTimeout) {
                    console.log('Returning cached events data');
                    return {
                        success: true,
                        data: cached.data,
                        source: 'events_api_cache',
                        timestamp: cached.timestamp,
                    };
                }
            }
            const eventsData = await this.fetchEvents(request);
            const responseData = {
                events: eventsData,
            };
            this.cache.set(cacheKey, {
                data: responseData,
                timestamp: Date.now(),
            });
            return {
                success: true,
                data: responseData,
                source: 'events_api',
                timestamp: Date.now(),
            };
        }
        catch (error) {
            console.error('Events API error:', error);
            return {
                success: true,
                data: this.generateSyntheticEventsData(request),
                source: 'events_api_synthetic',
                timestamp: Date.now(),
            };
        }
    }
    async fetchEvents(request) {
        const events = [];
        const now = new Date();
        const startDate = request.dateRange?.start ? new Date(request.dateRange.start) : now;
        const endDate = request.dateRange?.end ? new Date(request.dateRange.end) : new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
        events.push(...this.getHolidaysInRange(startDate, endDate));
        for (const location of request.locations) {
            events.push(...this.getLocationEvents(location, startDate, endDate));
        }
        return [...new Set(events)];
    }
    getHolidaysInRange(startDate, endDate) {
        const holidays = [];
        const year = startDate.getFullYear();
        const holidayDates = [
            { date: new Date(year, 0, 1), name: 'New Year\'s Day' },
            { date: new Date(year, 0, 26), name: 'Republic Day (India)' },
            { date: new Date(year, 2, 8), name: 'Holi' },
            { date: new Date(year, 7, 15), name: 'Independence Day (India)' },
            { date: new Date(year, 9, 2), name: 'Gandhi Jayanti' },
            { date: new Date(year, 10, 14), name: 'Diwali' },
            { date: new Date(year, 11, 25), name: 'Christmas Day' },
            { date: new Date(year, 11, 31), name: 'New Year\'s Eve' },
        ];
        if (year === 2025) {
            holidayDates.push({ date: new Date(2025, 7, 14), name: 'Independence Day (15 Aug)' }, { date: new Date(2025, 7, 18), name: 'Raksha Bandhan (18 Aug)' }, { date: new Date(2025, 1, 10), name: 'Goa Carnival (10–12 Feb)' }, { date: new Date(2025, 2, 13), name: 'Holi (14 Mar)' }, { date: new Date(2025, 9, 20), name: 'Diwali (21 Oct)' });
        }
        for (const holiday of holidayDates) {
            if (holiday.date >= startDate && holiday.date <= endDate) {
                holidays.push(holiday.name);
            }
        }
        return holidays;
    }
    getLocationEvents(location, startDate, endDate) {
        const events = [];
        const locationLower = location.toLowerCase();
        const locationEvents = {
            'goa': ['Goa Carnival', 'Sunburn Festival', 'Goa Food Festival'],
            'mumbai': ['Mumbai Film Festival', 'Ganesh Chaturthi', 'Navratri Festival'],
            'delhi': ['Delhi Book Fair', 'Dussehra Celebrations', 'India Gate Festival'],
            'bangalore': ['Bangalore Literature Festival', 'Karaga Festival', 'Dussehra'],
            'paris': ['Paris Fashion Week', 'Bastille Day', 'Nuit Blanche'],
            'london': ['London Fashion Week', 'Notting Hill Carnival', 'Thames Festival'],
            'dubai': ['Dubai Shopping Festival', 'Dubai Food Festival', 'Global Village'],
            'thailand': ['Songkran Festival', 'Loy Krathong', 'Thai New Year'],
            'manali': ['Manali Winter Carnival', 'Hadimba Devi Fair', 'Doongri Forest Festival'],
        };
        for (const [eventLocation, locationEventList] of Object.entries(locationEvents)) {
            if (locationLower.includes(eventLocation)) {
                const selectedEvents = locationEventList.slice(0, Math.floor(Math.random() * 2) + 1);
                events.push(...selectedEvents);
            }
        }
        const month = startDate.getMonth();
        if (month >= 2 && month <= 4) {
            events.push('Spring Festival Season');
        }
        else if (month >= 5 && month <= 7) {
            events.push('Summer Tourism Peak');
        }
        else if (month >= 8 && month <= 10) {
            events.push('Festival Season');
        }
        else {
            events.push('Winter Holiday Season');
        }
        return events;
    }
    generateSyntheticEventsData(request) {
        const events = [];
        events.push('Local Festival Season');
        events.push('Tourism Peak Period');
        for (const location of request.locations) {
            events.push(`${location} Cultural Festival`);
        }
        if (request.dateRange) {
            const startDate = new Date(request.dateRange.start);
            const month = startDate.getMonth();
            if (month === 7) {
                events.push('Independence Day Season');
            }
            else if (month === 10 || month === 11) {
                events.push('Holiday Season');
            }
        }
        return { events };
    }
    generateCacheKey(request) {
        return `events_${request.locations.join('_')}_${request.dateRange?.start || 'no_date'}`;
    }
    async healthCheck() {
        try {
            return true;
        }
        catch (error) {
            console.error('Events API health check failed:', error);
            return false;
        }
    }
    async refreshCache() {
        this.cache.clear();
        console.log('Events API cache cleared');
    }
}
exports.EventsAPIConnector = EventsAPIConnector;
//# sourceMappingURL=EventsAPIConnector.js.map