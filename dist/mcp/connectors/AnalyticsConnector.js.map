{"version": 3, "file": "AnalyticsConnector.js", "sourceRoot": "", "sources": ["../../../src/mcp/connectors/AnalyticsConnector.ts"], "names": [], "mappings": ";;;AAGA,MAAa,kBAAkB;IAM7B;QAHQ,UAAK,GAAqB,IAAI,GAAG,EAAE,CAAC;QACpC,iBAAY,GAAW,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;QAG5C,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,sBAAsB,IAAI,2BAA2B,CAAC;QACjF,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI,EAAE,CAAC;IACpD,CAAC;IAKD,KAAK,CAAC,SAAS,CAAC,OAAmB;QACjC,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;YAGhD,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC7B,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;gBACxC,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;oBACtD,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC;oBAC/C,OAAO;wBACL,OAAO,EAAE,IAAI;wBACb,IAAI,EAAE,MAAM,CAAC,IAAI;wBACjB,MAAM,EAAE,iBAAiB;wBACzB,SAAS,EAAE,MAAM,CAAC,SAAS;qBAC5B,CAAC;gBACJ,CAAC;YACH,CAAC;YAGD,MAAM,iBAAiB,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;YACrE,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;YAC/D,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;YAEjE,MAAM,YAAY,GAAG;gBACnB,kBAAkB,EAAE,iBAAiB;gBACrC,eAAe,EAAE,cAAc;gBAC/B,gBAAgB,EAAE,eAAe;aAClC,CAAC;YAGF,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,EAAE;gBACvB,IAAI,EAAE,YAAY;gBAClB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;aACtB,CAAC,CAAC;YAEH,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE,YAAY;gBAClB,MAAM,EAAE,eAAe;gBACvB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;aACtB,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;YAG7C,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE,IAAI,CAAC,8BAA8B,CAAC,OAAO,CAAC;gBAClD,MAAM,EAAE,qBAAqB;gBAC7B,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;aACtB,CAAC;QACJ,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,sBAAsB,CAAC,OAAmB;QACtD,MAAM,WAAW,GAA2B,EAAE,CAAC;QAG/C,KAAK,MAAM,QAAQ,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;YAEzC,WAAW,CAAC,GAAG,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC;YAC3E,WAAW,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,sBAAsB,CAAC,SAAS,CAAC,CAAC;YAG/D,WAAW,CAAC,GAAG,QAAQ,SAAS,CAAC,GAAG,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;YAGzE,WAAW,CAAC,GAAG,QAAQ,cAAc,CAAC,GAAG,IAAI,CAAC,sBAAsB,CAAC,YAAY,CAAC,CAAC;QACrF,CAAC;QAGD,IAAI,OAAO,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;YAClC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;gBACtD,MAAM,IAAI,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;gBAClC,MAAM,EAAE,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;gBACpC,IAAI,IAAI,IAAI,EAAE,EAAE,CAAC;oBACf,WAAW,CAAC,GAAG,IAAI,IAAI,EAAE,EAAE,CAAC,GAAG,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC;gBACvE,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,WAAW,CAAC;IACrB,CAAC;IAKO,KAAK,CAAC,mBAAmB,CAAC,OAAmB;QAEnD,OAAO;YACL,OAAO,EAAE;gBACP,WAAW,EAAE,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC;gBAC3C,iBAAiB,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;aACjD;YACD,MAAM,EAAE;gBACN,WAAW,EAAE,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC;gBAC1C,iBAAiB,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;aACjD;YACD,WAAW,EAAE;gBACX,WAAW,EAAE,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC;gBAC/C,iBAAiB,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;aACjD;SACF,CAAC;IACJ,CAAC;IAKO,KAAK,CAAC,oBAAoB,CAAC,OAAmB;QACpD,MAAM,QAAQ,GAAwB,EAAE,CAAC;QAEzC,KAAK,MAAM,QAAQ,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;YAEzC,MAAM,YAAY,GAAG;gBACnB,GAAG,QAAQ,cAAc;gBACzB,GAAG,QAAQ,cAAc;gBACzB,GAAG,QAAQ,WAAW;gBACtB,GAAG,QAAQ,YAAY;aACxB,CAAC;YAEF,KAAK,MAAM,WAAW,IAAI,YAAY,EAAE,CAAC;gBACvC,QAAQ,CAAC,WAAW,CAAC,GAAG;oBACtB,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE;oBACzD,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,GAAG;oBACpD,gBAAgB,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC;oBACjD,YAAY,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;iBACvC,CAAC;YACJ,CAAC;QACH,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAKO,sBAAsB,CAAC,IAAY;QACzC,MAAM,SAAS,GAAG;YAChB,MAAM,EAAE,IAAI;YACZ,KAAK,EAAE,IAAI;YACX,UAAU,EAAE,IAAI;YAChB,OAAO,EAAE,IAAI;SACd,CAAC;QAEF,MAAM,QAAQ,GAAG,SAAS,CAAC,IAA8B,CAAC,IAAI,IAAI,CAAC;QACnE,MAAM,SAAS,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC;QAE/C,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,GAAG,SAAS,CAAC,CAAC,CAAC;IAC7D,CAAC;IAKO,eAAe,CAAC,IAAY;QAClC,MAAM,aAAa,GAAG;YACpB,MAAM,EAAE,GAAG;YACX,KAAK,EAAE,GAAG;YACV,UAAU,EAAE,GAAG;SAChB,CAAC;QAEF,MAAM,WAAW,GAAG,aAAa,CAAC,IAAkC,CAAC,IAAI,GAAG,CAAC;QAC7E,MAAM,SAAS,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;QAE9C,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,WAAW,GAAG,SAAS,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;IACxE,CAAC;IAKO,8BAA8B,CAAC,OAAmB;QACxD,MAAM,iBAAiB,GAA2B,EAAE,CAAC;QACrD,MAAM,eAAe,GAAwB,EAAE,CAAC;QAGhD,KAAK,MAAM,QAAQ,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;YACzC,iBAAiB,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,sBAAsB,CAAC,SAAS,CAAC,CAAC;YACrE,iBAAiB,CAAC,GAAG,QAAQ,SAAS,CAAC,GAAG,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;YAE/E,eAAe,CAAC,GAAG,QAAQ,cAAc,CAAC,GAAG;gBAC3C,UAAU,EAAE,GAAG;gBACf,YAAY,EAAE,IAAI;gBAClB,gBAAgB,EAAE,EAAE;gBACpB,YAAY,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACvC,CAAC;QACJ,CAAC;QAED,MAAM,cAAc,GAAG;YACrB,OAAO,EAAE,EAAE,WAAW,EAAE,GAAG,EAAE,iBAAiB,EAAE,CAAC,EAAE;YACnD,MAAM,EAAE,EAAE,WAAW,EAAE,GAAG,EAAE,iBAAiB,EAAE,CAAC,EAAE;YAClD,WAAW,EAAE,EAAE,WAAW,EAAE,GAAG,EAAE,iBAAiB,EAAE,CAAC,EAAE;SACxD,CAAC;QAEF,OAAO;YACL,kBAAkB,EAAE,iBAAiB;YACrC,eAAe,EAAE,cAAc;YAC/B,gBAAgB,EAAE,eAAe;SAClC,CAAC;IACJ,CAAC;IAKO,gBAAgB,CAAC,OAAmB;QAC1C,OAAO,aAAa,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IAC1F,CAAC;IAKD,KAAK,CAAC,WAAW;QACf,IAAI,CAAC;YAEH,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,0CAA0C,EAAE,KAAK,CAAC,CAAC;YACjE,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,YAAY;QAChB,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;QACnB,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;IACnD,CAAC;CACF;AAnPD,gDAmPC"}