import { MCPRequest, MCPResponse } from '../MCPRouter';
export declare class FlightAPIConnector {
    private baseUrl;
    private apiKey;
    private cache;
    private cacheTimeout;
    constructor();
    fetchData(request: MCPRequest): Promise<MCPResponse>;
    private fetchFlightSearchTrends;
    private fetchFlightMetadata;
    private generateSyntheticFlightData;
    private getRandomAirlines;
    private generateCacheKey;
    healthCheck(): Promise<boolean>;
    refreshCache(): Promise<void>;
}
//# sourceMappingURL=FlightAPIConnector.d.ts.map