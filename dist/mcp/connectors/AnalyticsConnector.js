"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnalyticsConnector = void 0;
class AnalyticsConnector {
    constructor() {
        this.cache = new Map();
        this.cacheTimeout = 15 * 60 * 1000;
        this.baseUrl = process.env.ANALYTICS_API_BASE_URL || 'https://api.analytics.com';
        this.apiKey = process.env.ANALYTICS_API_KEY || '';
    }
    async fetchData(request) {
        try {
            const cacheKey = this.generateCacheKey(request);
            if (this.cache.has(cacheKey)) {
                const cached = this.cache.get(cacheKey);
                if (Date.now() - cached.timestamp < this.cacheTimeout) {
                    console.log('Returning cached analytics data');
                    return {
                        success: true,
                        data: cached.data,
                        source: 'analytics_cache',
                        timestamp: cached.timestamp,
                    };
                }
            }
            const bookingConversion = await this.fetchBookingConversion(request);
            const apiPerformance = await this.fetchAPIPerformance(request);
            const contentMetadata = await this.fetchContentMetadata(request);
            const responseData = {
                booking_conversion: bookingConversion,
                api_performance: apiPerformance,
                content_metadata: contentMetadata,
            };
            this.cache.set(cacheKey, {
                data: responseData,
                timestamp: Date.now(),
            });
            return {
                success: true,
                data: responseData,
                source: 'analytics_api',
                timestamp: Date.now(),
            };
        }
        catch (error) {
            console.error('Analytics API error:', error);
            return {
                success: true,
                data: this.generateSyntheticAnalyticsData(request),
                source: 'analytics_synthetic',
                timestamp: Date.now(),
            };
        }
    }
    async fetchBookingConversion(request) {
        const conversions = {};
        for (const location of request.locations) {
            conversions[`${location} flights`] = this.generateConversionRate('flight');
            conversions[location] = this.generateConversionRate('general');
            conversions[`${location} hotels`] = this.generateConversionRate('hotel');
            conversions[`${location} experiences`] = this.generateConversionRate('experience');
        }
        if (request.locations.length >= 2) {
            for (let i = 0; i < request.locations.length - 1; i++) {
                const from = request.locations[i];
                const to = request.locations[i + 1];
                if (from && to) {
                    conversions[`${from}–${to}`] = this.generateConversionRate('flight');
                }
            }
        }
        return conversions;
    }
    async fetchAPIPerformance(request) {
        return {
            flights: {
                avg_latency: this.generateLatency('flight'),
                failures_last_24h: Math.floor(Math.random() * 5),
            },
            hotels: {
                avg_latency: this.generateLatency('hotel'),
                failures_last_24h: Math.floor(Math.random() * 3),
            },
            experiences: {
                avg_latency: this.generateLatency('experience'),
                failures_last_24h: Math.floor(Math.random() * 4),
            },
        };
    }
    async fetchContentMetadata(request) {
        const metadata = {};
        for (const location of request.locations) {
            const contentTypes = [
                `${location} attractions`,
                `${location} restaurants`,
                `${location} shopping`,
                `${location} nightlife`,
            ];
            for (const contentType of contentTypes) {
                metadata[contentType] = {
                    avg_rating: Math.round((Math.random() * 2 + 3) * 10) / 10,
                    review_count: Math.floor(Math.random() * 5000) + 500,
                    popularity_score: Math.round(Math.random() * 100),
                    last_updated: new Date().toISOString(),
                };
            }
        }
        return metadata;
    }
    generateConversionRate(type) {
        const baseRates = {
            flight: 0.35,
            hotel: 0.28,
            experience: 0.42,
            general: 0.30,
        };
        const baseRate = baseRates[type] || 0.30;
        const variation = (Math.random() * 0.3) - 0.15;
        return Math.max(0.05, Math.min(0.8, baseRate + variation));
    }
    generateLatency(type) {
        const baseLatencies = {
            flight: 2.1,
            hotel: 1.6,
            experience: 2.4,
        };
        const baseLatency = baseLatencies[type] || 2.0;
        const variation = (Math.random() * 1.0) - 0.5;
        return Math.max(0.5, Math.round((baseLatency + variation) * 10) / 10);
    }
    generateSyntheticAnalyticsData(request) {
        const bookingConversion = {};
        const contentMetadata = {};
        for (const location of request.locations) {
            bookingConversion[location] = this.generateConversionRate('general');
            bookingConversion[`${location} hotels`] = this.generateConversionRate('hotel');
            contentMetadata[`${location} attractions`] = {
                avg_rating: 4.2,
                review_count: 1500,
                popularity_score: 75,
                last_updated: new Date().toISOString(),
            };
        }
        const apiPerformance = {
            flights: { avg_latency: 2.0, failures_last_24h: 1 },
            hotels: { avg_latency: 1.5, failures_last_24h: 0 },
            experiences: { avg_latency: 2.2, failures_last_24h: 1 },
        };
        return {
            booking_conversion: bookingConversion,
            api_performance: apiPerformance,
            content_metadata: contentMetadata,
        };
    }
    generateCacheKey(request) {
        return `analytics_${request.locations.join('_')}_${Date.now().toString().slice(0, -5)}`;
    }
    async healthCheck() {
        try {
            return true;
        }
        catch (error) {
            console.error('Analytics connector health check failed:', error);
            return false;
        }
    }
    async refreshCache() {
        this.cache.clear();
        console.log('Analytics connector cache cleared');
    }
}
exports.AnalyticsConnector = AnalyticsConnector;
//# sourceMappingURL=AnalyticsConnector.js.map