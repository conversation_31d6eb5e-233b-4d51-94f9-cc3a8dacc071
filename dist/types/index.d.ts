export type StalenessRisk = 'Low' | 'Medium' | 'High';
export type TripType = 'One Way' | 'Round Trip';
export type FareType = 'Regular' | 'Premium' | 'Budget';
export type InventoryStatus = 'Available' | 'Limited' | 'Unavailable';
export interface PassengerInfo {
    adult: number;
    child: number;
    infant: number;
}
export interface GuestInfo {
    adult: number;
    child: number;
    child_ages?: number[];
}
export interface FlightSearchTrend {
    from_city: string;
    to_city: string;
    trip_type: TripType;
    start_date: string;
    end_date?: string;
    pax: PassengerInfo;
    is_refundable: boolean;
    fare_type: FareType;
    search_count: number;
    booking_conversion: number;
}
export interface HotelSearchTrend {
    city: string;
    checkin_date: string;
    checkout_date: string;
    rooms: number;
    guests: GuestInfo;
    search_count: number;
    booking_conversion: number;
}
export interface ExperienceSearchTrend {
    region?: string;
    city?: string;
    country?: string;
    checkin_date: string;
    checkout_date: string;
    search_count: number;
}
export interface ContentMetadata {
    avg_price: string;
    rating?: number;
    inventory_status: InventoryStatus;
    airlines?: string[];
}
export interface ApiPerformance {
    avg_latency: number;
    failures_last_24h: number;
}
export interface TravelAnalysisInput {
    flight_search_trends: FlightSearchTrend[];
    hotel_search_trends: HotelSearchTrend[];
    experience_search_trends: ExperienceSearchTrend[];
    content_metadata: Record<string, ContentMetadata>;
    booking_conversion: Record<string, number>;
    api_performance: {
        flights: ApiPerformance;
        hotels: ApiPerformance;
        experiences: ApiPerformance;
    };
    events: string[];
}
export interface CachingRecommendation {
    entity_type: string;
    entity_id_or_name: string;
    reasoning: string;
    suggested_ttl_hours: number;
    staleness_risk: StalenessRisk;
}
export interface AnalysisContext {
    high_demand_threshold: number;
    high_conversion_threshold: number;
    high_latency_threshold: number;
    default_ttl_hours: number;
    max_ttl_hours: number;
    min_ttl_hours: number;
}
export interface DomainClassification {
    primary_domain: 'flights' | 'hotels' | 'experiences' | 'mixed';
    locations: string[];
    date_range: {
        start: string;
        end: string;
    };
    events: string[];
}
//# sourceMappingURL=index.d.ts.map