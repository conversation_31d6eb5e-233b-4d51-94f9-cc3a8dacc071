"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Config = void 0;
const dotenv_1 = __importDefault(require("dotenv"));
dotenv_1.default.config();
class Config {
    static getAnalysisContext() {
        return {
            high_demand_threshold: this.HIGH_SEARCH_VOLUME_THRESHOLD,
            high_conversion_threshold: this.HIGH_CONVERSION_THRESHOLD,
            high_latency_threshold: this.HIGH_LATENCY_THRESHOLD,
            default_ttl_hours: this.DEFAULT_TTL_HOURS,
            max_ttl_hours: this.MAX_TTL_HOURS,
            min_ttl_hours: this.MIN_TTL_HOURS,
        };
    }
    static isDevelopment() {
        return this.NODE_ENV === 'development';
    }
    static isProduction() {
        return this.NODE_ENV === 'production';
    }
}
exports.Config = Config;
Config.PORT = parseInt(process.env.PORT || '3000', 10);
Config.NODE_ENV = process.env.NODE_ENV || 'development';
Config.DEFAULT_TTL_HOURS = parseInt(process.env.DEFAULT_TTL_HOURS || '12', 10);
Config.MAX_TTL_HOURS = parseInt(process.env.MAX_TTL_HOURS || '72', 10);
Config.MIN_TTL_HOURS = parseInt(process.env.MIN_TTL_HOURS || '1', 10);
Config.HIGH_SEARCH_VOLUME_THRESHOLD = parseInt(process.env.HIGH_SEARCH_VOLUME_THRESHOLD || '10000', 10);
Config.HIGH_CONVERSION_THRESHOLD = parseFloat(process.env.HIGH_CONVERSION_THRESHOLD || '0.4');
Config.HIGH_LATENCY_THRESHOLD = parseFloat(process.env.HIGH_LATENCY_THRESHOLD || '2.0');
Config.INVENTORY_LIMITED_RISK_MULTIPLIER = parseFloat(process.env.INVENTORY_LIMITED_RISK_MULTIPLIER || '0.5');
Config.HIGH_VOLATILITY_RISK_MULTIPLIER = parseFloat(process.env.HIGH_VOLATILITY_RISK_MULTIPLIER || '0.3');
//# sourceMappingURL=index.js.map