{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/config/index.ts"], "names": [], "mappings": ";;;;;;AAAA,oDAA4B;AAI5B,gBAAM,CAAC,MAAM,EAAE,CAAC;AAEhB,MAAa,MAAM;IAkBV,MAAM,CAAC,kBAAkB;QAC9B,OAAO;YACL,qBAAqB,EAAE,IAAI,CAAC,4BAA4B;YACxD,yBAAyB,EAAE,IAAI,CAAC,yBAAyB;YACzD,sBAAsB,EAAE,IAAI,CAAC,sBAAsB;YACnD,iBAAiB,EAAE,IAAI,CAAC,iBAAiB;YACzC,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,aAAa,EAAE,IAAI,CAAC,aAAa;SAClC,CAAC;IACJ,CAAC;IAEM,MAAM,CAAC,aAAa;QACzB,OAAO,IAAI,CAAC,QAAQ,KAAK,aAAa,CAAC;IACzC,CAAC;IAEM,MAAM,CAAC,YAAY;QACxB,OAAO,IAAI,CAAC,QAAQ,KAAK,YAAY,CAAC;IACxC,CAAC;;AAnCH,wBAoCC;AAnCwB,WAAI,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,IAAI,MAAM,EAAE,EAAE,CAAC,CAAC;AAChD,eAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,IAAI,aAAa,CAAC;AAGjD,wBAAiB,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI,IAAI,EAAE,EAAE,CAAC,CAAC;AACxE,oBAAa,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,aAAa,IAAI,IAAI,EAAE,EAAE,CAAC,CAAC;AAChE,oBAAa,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,aAAa,IAAI,GAAG,EAAE,EAAE,CAAC,CAAC;AAG/D,mCAA4B,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,4BAA4B,IAAI,OAAO,EAAE,EAAE,CAAC,CAAC;AACjG,gCAAyB,GAAG,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,yBAAyB,IAAI,KAAK,CAAC,CAAC;AACvF,6BAAsB,GAAG,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,sBAAsB,IAAI,KAAK,CAAC,CAAC;AAGjF,wCAAiC,GAAG,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,iCAAiC,IAAI,KAAK,CAAC,CAAC;AACvG,sCAA+B,GAAG,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,+BAA+B,IAAI,KAAK,CAAC,CAAC"}