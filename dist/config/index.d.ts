import { AnalysisContext } from '../types';
export declare class Config {
    static readonly PORT: number;
    static readonly NODE_ENV: string;
    static readonly DEFAULT_TTL_HOURS: number;
    static readonly MAX_TTL_HOURS: number;
    static readonly MIN_TTL_HOURS: number;
    static readonly HIGH_SEARCH_VOLUME_THRESHOLD: number;
    static readonly HIGH_CONVERSION_THRESHOLD: number;
    static readonly HIGH_LATENCY_THRESHOLD: number;
    static readonly INVENTORY_LIMITED_RISK_MULTIPLIER: number;
    static readonly HIGH_VOLATILITY_RISK_MULTIPLIER: number;
    static getAnalysisContext(): AnalysisContext;
    static isDevelopment(): boolean;
    static isProduction(): boolean;
}
//# sourceMappingURL=index.d.ts.map