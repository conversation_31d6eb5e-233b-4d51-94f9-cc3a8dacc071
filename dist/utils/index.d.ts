import { StalenessRisk, InventoryStatus } from '../types';
export declare class DateUtils {
    static parseDate(dateString: string): Date;
    static isWithinDays(dateString: string, days: number): boolean;
    static getDateRange(dates: string[]): {
        start: string;
        end: string;
    };
}
export declare class AnalysisUtils {
    static calculateStalenessRisk(inventoryStatus: InventoryStatus, volatilityScore: number, apiLatency: number): StalenessRisk;
    static calculateTTL(baseHours: number, stalenessRisk: StalenessRisk, searchVolume: number, conversionRate: number, minHours: number, maxHours: number): number;
    static extractLocations(data: any): string[];
    static generateReasoning(entityType: string, searchVolume: number, conversionRate: number, inventoryStatus: InventoryStatus, events: string[], apiLatency?: number): string;
}
//# sourceMappingURL=index.d.ts.map