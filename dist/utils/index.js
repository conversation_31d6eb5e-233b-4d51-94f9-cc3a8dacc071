"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnalysisUtils = exports.DateUtils = void 0;
class DateUtils {
    static parseDate(dateString) {
        return new Date(dateString);
    }
    static isWithinDays(dateString, days) {
        const targetDate = this.parseDate(dateString);
        const now = new Date();
        const diffTime = targetDate.getTime() - now.getTime();
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        return diffDays <= days && diffDays >= 0;
    }
    static getDateRange(dates) {
        if (dates.length === 0) {
            const now = new Date().toISOString().split('T')[0];
            return { start: now, end: now };
        }
        const sortedDates = dates.sort();
        return {
            start: sortedDates[0],
            end: sortedDates[sortedDates.length - 1],
        };
    }
}
exports.DateUtils = DateUtils;
class AnalysisUtils {
    static calculateStalenessRisk(inventoryStatus, volatilityScore, apiLatency) {
        let riskScore = 0;
        switch (inventoryStatus) {
            case 'Limited':
                riskScore += 3;
                break;
            case 'Unavailable':
                riskScore += 5;
                break;
            case 'Available':
                riskScore += 1;
                break;
        }
        riskScore += volatilityScore * 3;
        if (apiLatency > 2.5)
            riskScore += 2;
        else if (apiLatency > 2.0)
            riskScore += 1;
        if (riskScore <= 2)
            return 'Low';
        if (riskScore <= 4)
            return 'Medium';
        return 'High';
    }
    static calculateTTL(baseHours, stalenessRisk, searchVolume, conversionRate, minHours, maxHours) {
        let ttl = baseHours;
        switch (stalenessRisk) {
            case 'High':
                ttl *= 0.3;
                break;
            case 'Medium':
                ttl *= 0.7;
                break;
            case 'Low':
                ttl *= 1.5;
                break;
        }
        const demandMultiplier = searchVolume > 10000 ? 0.8 : 1.2;
        const conversionMultiplier = conversionRate > 0.4 ? 0.9 : 1.1;
        ttl *= demandMultiplier * conversionMultiplier;
        return Math.max(minHours, Math.min(maxHours, Math.round(ttl)));
    }
    static extractLocations(data) {
        const locations = new Set();
        if (data.flight_search_trends) {
            data.flight_search_trends.forEach((trend) => {
                locations.add(trend.from_city);
                locations.add(trend.to_city);
            });
        }
        if (data.hotel_search_trends) {
            data.hotel_search_trends.forEach((trend) => {
                locations.add(trend.city);
            });
        }
        if (data.experience_search_trends) {
            data.experience_search_trends.forEach((trend) => {
                if (trend.city)
                    locations.add(trend.city);
                if (trend.region)
                    locations.add(trend.region);
                if (trend.country)
                    locations.add(trend.country);
            });
        }
        return Array.from(locations);
    }
    static generateReasoning(entityType, searchVolume, conversionRate, inventoryStatus, events, apiLatency) {
        const reasons = [];
        if (searchVolume > 10000) {
            reasons.push(`High search volume (${searchVolume.toLocaleString()})`);
        }
        else if (searchVolume > 5000) {
            reasons.push(`Moderate search volume (${searchVolume.toLocaleString()})`);
        }
        if (conversionRate > 0.4) {
            reasons.push(`excellent conversion rate (${(conversionRate * 100).toFixed(0)}%)`);
        }
        else if (conversionRate > 0.3) {
            reasons.push(`solid conversion rate (${(conversionRate * 100).toFixed(0)}%)`);
        }
        else if (conversionRate > 0.2) {
            reasons.push(`decent conversion rate (${(conversionRate * 100).toFixed(0)}%)`);
        }
        if (inventoryStatus === 'Limited') {
            reasons.push('limited inventory requires frequent updates');
        }
        else if (inventoryStatus === 'Available') {
            reasons.push('good inventory availability');
        }
        if (events.length > 0) {
            const eventList = events.slice(0, 2).join(' and ');
            reasons.push(`${eventList} driving demand`);
        }
        if (apiLatency && apiLatency > 2.0) {
            reasons.push(`higher API latency (${apiLatency}s) requires caching`);
        }
        return reasons.join(' with ') + '.';
    }
}
exports.AnalysisUtils = AnalysisUtils;
//# sourceMappingURL=index.js.map