{"version": 3, "file": "basic-usage.js", "sourceRoot": "", "sources": ["../../src/examples/basic-usage.ts"], "names": [], "mappings": ";;AA8LS,8CAAiB;AA9L1B,iFAA8E;AAM9E,KAAK,UAAU,iBAAiB;IAC9B,OAAO,CAAC,GAAG,CAAC,sDAAsD,CAAC,CAAC;IAGpE,MAAM,WAAW,GAAwB;QACvC,oBAAoB,EAAE;YACpB;gBACE,SAAS,EAAE,QAAQ;gBACnB,OAAO,EAAE,OAAO;gBAChB,SAAS,EAAE,SAAS;gBACpB,UAAU,EAAE,YAAY;gBACxB,GAAG,EAAE;oBACH,KAAK,EAAE,CAAC;oBACR,KAAK,EAAE,CAAC;oBACR,MAAM,EAAE,CAAC;iBACV;gBACD,aAAa,EAAE,IAAI;gBACnB,SAAS,EAAE,SAAS;gBACpB,YAAY,EAAE,KAAK;gBACnB,kBAAkB,EAAE,IAAI;aACzB;YACD;gBACE,SAAS,EAAE,WAAW;gBACtB,OAAO,EAAE,KAAK;gBACd,SAAS,EAAE,YAAY;gBACvB,UAAU,EAAE,YAAY;gBACxB,QAAQ,EAAE,YAAY;gBACtB,GAAG,EAAE;oBACH,KAAK,EAAE,CAAC;oBACR,KAAK,EAAE,CAAC;oBACR,MAAM,EAAE,CAAC;iBACV;gBACD,aAAa,EAAE,KAAK;gBACpB,SAAS,EAAE,SAAS;gBACpB,YAAY,EAAE,IAAI;gBAClB,kBAAkB,EAAE,IAAI;aACzB;SACF;QACD,mBAAmB,EAAE;YACnB;gBACE,IAAI,EAAE,KAAK;gBACX,YAAY,EAAE,YAAY;gBAC1B,aAAa,EAAE,YAAY;gBAC3B,KAAK,EAAE,CAAC;gBACR,MAAM,EAAE;oBACN,KAAK,EAAE,CAAC;oBACR,KAAK,EAAE,CAAC;oBACR,UAAU,EAAE,CAAC,CAAC,CAAC;iBAChB;gBACD,YAAY,EAAE,KAAK;gBACnB,kBAAkB,EAAE,IAAI;aACzB;YACD;gBACE,IAAI,EAAE,QAAQ;gBACd,YAAY,EAAE,YAAY;gBAC1B,aAAa,EAAE,YAAY;gBAC3B,KAAK,EAAE,CAAC;gBACR,MAAM,EAAE;oBACN,KAAK,EAAE,CAAC;oBACR,KAAK,EAAE,CAAC;oBACR,UAAU,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;iBACpB;gBACD,YAAY,EAAE,IAAI;gBAClB,kBAAkB,EAAE,IAAI;aACzB;SACF;QACD,wBAAwB,EAAE;YACxB;gBACE,MAAM,EAAE,cAAc;gBACtB,YAAY,EAAE,YAAY;gBAC1B,aAAa,EAAE,YAAY;gBAC3B,YAAY,EAAE,IAAI;aACnB;YACD;gBACE,IAAI,EAAE,OAAO;gBACb,YAAY,EAAE,YAAY;gBAC1B,aAAa,EAAE,YAAY;gBAC3B,YAAY,EAAE,IAAI;aACnB;YACD;gBACE,OAAO,EAAE,UAAU;gBACnB,YAAY,EAAE,YAAY;gBAC1B,aAAa,EAAE,YAAY;gBAC3B,YAAY,EAAE,IAAI;aACnB;SACF;QACD,gBAAgB,EAAE;YAChB,mBAAmB,EAAE;gBACnB,SAAS,EAAE,OAAO;gBAClB,MAAM,EAAE,GAAG;gBACX,gBAAgB,EAAE,WAAW;aAC9B;YACD,iBAAiB,EAAE;gBACjB,SAAS,EAAE,OAAO;gBAClB,MAAM,EAAE,GAAG;gBACX,gBAAgB,EAAE,SAAS;aAC5B;YACD,sBAAsB,EAAE;gBACtB,SAAS,EAAE,OAAO;gBAClB,QAAQ,EAAE,CAAC,QAAQ,EAAE,WAAW,CAAC;gBACjC,gBAAgB,EAAE,WAAW;aAC9B;YACD,uBAAuB,EAAE;gBACvB,SAAS,EAAE,OAAO;gBAClB,QAAQ,EAAE,CAAC,UAAU,EAAE,SAAS,CAAC;gBACjC,gBAAgB,EAAE,WAAW;aAC9B;SACF;QACD,kBAAkB,EAAE;YAClB,YAAY,EAAE,IAAI;YAClB,iBAAiB,EAAE,IAAI;YACvB,cAAc,EAAE,IAAI;YACpB,eAAe,EAAE,IAAI;YACrB,cAAc,EAAE,IAAI;YACpB,OAAO,EAAE,IAAI;YACb,UAAU,EAAE,IAAI;SACjB;QACD,eAAe,EAAE;YACf,OAAO,EAAE;gBACP,WAAW,EAAE,GAAG;gBAChB,iBAAiB,EAAE,CAAC;aACrB;YACD,MAAM,EAAE;gBACN,WAAW,EAAE,GAAG;gBAChB,iBAAiB,EAAE,CAAC;aACrB;YACD,WAAW,EAAE;gBACX,WAAW,EAAE,GAAG;gBAChB,iBAAiB,EAAE,CAAC;aACrB;SACF;QACD,MAAM,EAAE;YACN,2BAA2B;YAC3B,yBAAyB;YACzB,0BAA0B;SAC3B;KACF,CAAC;IAEF,IAAI,CAAC;QAEH,MAAM,QAAQ,GAAG,IAAI,iDAAuB,EAAE,CAAC;QAE/C,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;QAG7C,MAAM,eAAe,GAAG,MAAM,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;QAE5D,OAAO,CAAC,GAAG,CAAC,mDAAmD,CAAC,CAAC;QACjE,OAAO,CAAC,GAAG,CAAC,GAAG,CAAE,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;QAG7B,eAAe,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE;YACrC,OAAO,CAAC,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,WAAW,CAAC,WAAW,EAAE,KAAK,GAAG,CAAC,iBAAiB,EAAE,CAAC,CAAC;YACxF,OAAO,CAAC,GAAG,CAAC,WAAW,GAAG,CAAC,mBAAmB,QAAQ,CAAC,CAAC;YACxD,OAAO,CAAC,GAAG,CAAC,YAAY,GAAG,CAAC,cAAc,EAAE,CAAC,CAAC;YAC9C,OAAO,CAAC,GAAG,CAAC,iBAAiB,GAAG,CAAC,SAAS,EAAE,CAAC,CAAC;YAC9C,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC;QAGH,MAAM,OAAO,GAAG;YACd,KAAK,EAAE,eAAe,CAAC,MAAM;YAC7B,SAAS,EAAE,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,cAAc,KAAK,MAAM,CAAC,CAAC,MAAM;YAC1E,WAAW,EAAE,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,cAAc,KAAK,QAAQ,CAAC,CAAC,MAAM;YAC9E,QAAQ,EAAE,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,cAAc,KAAK,KAAK,CAAC,CAAC,MAAM;YACxE,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,mBAAmB,EAAE,CAAC,CAAC,GAAG,eAAe,CAAC,MAAM,CAAC;SACjH,CAAC;QAEF,OAAO,CAAC,GAAG,CAAC,GAAG,CAAE,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;QAC7B,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;QAC3B,OAAO,CAAC,GAAG,CAAC,6BAA6B,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC;QAC1D,OAAO,CAAC,GAAG,CAAC,iBAAiB,OAAO,CAAC,SAAS,kBAAkB,OAAO,CAAC,WAAW,eAAe,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;QACtH,OAAO,CAAC,GAAG,CAAC,mBAAmB,OAAO,CAAC,OAAO,QAAQ,CAAC,CAAC;IAE1D,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;IACnD,CAAC;AACH,CAAC;AAGD,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;IAC5B,iBAAiB,EAAE,CAAC;AACtB,CAAC"}