"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.basicUsageExample = basicUsageExample;
const TravelCachingStrategist_1 = require("../analyzer/TravelCachingStrategist");
async function basicUsageExample() {
    console.log('🚀 Travel Caching Strategist - Basic Usage Example\n');
    const sampleInput = {
        flight_search_trends: [
            {
                from_city: "Mumbai",
                to_city: "Delhi",
                trip_type: "One Way",
                start_date: "2025-08-14",
                pax: {
                    adult: 1,
                    child: 1,
                    infant: 0
                },
                is_refundable: true,
                fare_type: "Regular",
                search_count: 13200,
                booking_conversion: 0.42
            },
            {
                from_city: "Bangalore",
                to_city: "Goa",
                trip_type: "Round Trip",
                start_date: "2025-08-15",
                end_date: "2025-08-18",
                pax: {
                    adult: 2,
                    child: 0,
                    infant: 1
                },
                is_refundable: false,
                fare_type: "Regular",
                search_count: 9500,
                booking_conversion: 0.29
            }
        ],
        hotel_search_trends: [
            {
                city: "Goa",
                checkin_date: "2025-08-15",
                checkout_date: "2025-08-17",
                rooms: 1,
                guests: {
                    adult: 2,
                    child: 1,
                    child_ages: [7]
                },
                search_count: 11400,
                booking_conversion: 0.36
            },
            {
                city: "Manali",
                checkin_date: "2025-08-14",
                checkout_date: "2025-08-18",
                rooms: 2,
                guests: {
                    adult: 3,
                    child: 2,
                    child_ages: [5, 11]
                },
                search_count: 6700,
                booking_conversion: 0.21
            }
        ],
        experience_search_trends: [
            {
                region: "Dubai Desert",
                checkin_date: "2025-08-16",
                checkout_date: "2025-08-16",
                search_count: 5400
            },
            {
                city: "Paris",
                checkin_date: "2025-08-14",
                checkout_date: "2025-08-17",
                search_count: 7200
            },
            {
                country: "Thailand",
                checkin_date: "2025-08-13",
                checkout_date: "2025-08-19",
                search_count: 8900
            }
        ],
        content_metadata: {
            "Goa 3-star hotels": {
                avg_price: "₹2800",
                rating: 4.1,
                inventory_status: "Available"
            },
            "Manali cottages": {
                avg_price: "₹3200",
                rating: 4.3,
                inventory_status: "Limited"
            },
            "Mumbai–Delhi flights": {
                avg_price: "₹4200",
                airlines: ["IndiGo", "Air India"],
                inventory_status: "Available"
            },
            "Bangalore–Goa flights": {
                avg_price: "₹3700",
                airlines: ["SpiceJet", "Vistara"],
                inventory_status: "Available"
            }
        },
        booking_conversion: {
            "Goa hotels": 0.36,
            "Manali cottages": 0.21,
            "Mumbai–Delhi": 0.42,
            "Bangalore–Goa": 0.29,
            "Dubai Desert": 0.35,
            "Paris": 0.48,
            "Thailand": 0.51
        },
        api_performance: {
            flights: {
                avg_latency: 2.1,
                failures_last_24h: 3
            },
            hotels: {
                avg_latency: 1.6,
                failures_last_24h: 1
            },
            experiences: {
                avg_latency: 2.4,
                failures_last_24h: 2
            }
        },
        events: [
            "Independence Day (15 Aug)",
            "Raksha Bandhan (18 Aug)",
            "Goa Carnival (10–12 Aug)"
        ]
    };
    try {
        const analyzer = new TravelCachingStrategist_1.TravelCachingStrategist();
        console.log('📊 Analyzing travel data...\n');
        const recommendations = await analyzer.analyze(sampleInput);
        console.log('✅ Analysis complete! Generated recommendations:\n');
        console.log('='.repeat(80));
        recommendations.forEach((rec, index) => {
            console.log(`${index + 1}. ${rec.entity_type.toUpperCase()}: ${rec.entity_id_or_name}`);
            console.log(`   TTL: ${rec.suggested_ttl_hours} hours`);
            console.log(`   Risk: ${rec.staleness_risk}`);
            console.log(`   Reasoning: ${rec.reasoning}`);
            console.log('');
        });
        const summary = {
            total: recommendations.length,
            high_risk: recommendations.filter(r => r.staleness_risk === 'High').length,
            medium_risk: recommendations.filter(r => r.staleness_risk === 'Medium').length,
            low_risk: recommendations.filter(r => r.staleness_risk === 'Low').length,
            avg_ttl: Math.round(recommendations.reduce((sum, r) => sum + r.suggested_ttl_hours, 0) / recommendations.length),
        };
        console.log('='.repeat(80));
        console.log('📈 SUMMARY:');
        console.log(`   Total recommendations: ${summary.total}`);
        console.log(`   High risk: ${summary.high_risk}, Medium risk: ${summary.medium_risk}, Low risk: ${summary.low_risk}`);
        console.log(`   Average TTL: ${summary.avg_ttl} hours`);
    }
    catch (error) {
        console.error('❌ Error during analysis:', error);
    }
}
if (require.main === module) {
    basicUsageExample();
}
//# sourceMappingURL=basic-usage.js.map