{"version": 3, "file": "enhanced-usage.js", "sourceRoot": "", "sources": ["../../src/examples/enhanced-usage.ts"], "names": [], "mappings": ";;AAkNS,oDAAoB;AAAE,sDAAqB;AAlNpD,iGAA8F;AAK9F,KAAK,UAAU,oBAAoB;IACjC,OAAO,CAAC,GAAG,CAAC,6DAA6D,CAAC,CAAC;IAE3E,IAAI,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,uDAAuD,CAAC,CAAC;QACrE,MAAM,UAAU,GAAG,IAAI,iEAA+B,EAAE,CAAC;QAGzD,OAAO,CAAC,GAAG,CAAC,2EAA2E,CAAC,CAAC;QAEzF,MAAM,UAAU,CAAC,UAAU,EAAE,CAAC;QAC9B,OAAO,CAAC,GAAG,CAAC,kDAAkD,CAAC,CAAC;QAGhE,MAAM,aAAa,GAAG;YACpB;gBACE,KAAK,EAAE,iEAAiE;gBACxE,WAAW,EAAE,sDAAsD;aACpE;YACD;gBACE,KAAK,EAAE,yEAAyE;gBAChF,WAAW,EAAE,kCAAkC;aAChD;YACD;gBACE,KAAK,EAAE,0DAA0D;gBACjE,WAAW,EAAE,sDAAsD;aACpE;YACD;gBACE,KAAK,EAAE,0EAA0E;gBACjF,WAAW,EAAE,4CAA4C;aAC1D;SACF,CAAC;QAGF,KAAK,MAAM,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE,WAAW,EAAE,CAAC,IAAI,aAAa,CAAC,OAAO,EAAE,EAAE,CAAC;YACtE,OAAO,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACnC,OAAO,CAAC,GAAG,CAAC,YAAY,KAAK,GAAG,CAAC,KAAK,WAAW,EAAE,CAAC,CAAC;YACrD,OAAO,CAAC,GAAG,CAAC,OAAO,KAAK,GAAG,CAAC,CAAC;YAC7B,OAAO,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;YAEnC,IAAI,CAAC;gBACH,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;gBAC7B,MAAM,MAAM,GAAG,MAAM,UAAU,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;gBACpD,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;gBAG9C,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC;gBACnC,OAAO,CAAC,GAAG,CAAC,YAAY,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC;gBACzD,OAAO,CAAC,GAAG,CAAC,iBAAiB,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,eAAe,EAAE,CAAC,CAAC;gBACjG,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,UAAU,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;gBACzF,IAAI,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;oBACvC,OAAO,CAAC,GAAG,CAAC,kBAAkB,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,OAAO,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC,CAAC;gBACzH,CAAC;gBACD,OAAO,CAAC,GAAG,CAAC,gBAAgB,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,eAAe,EAAE,CAAC,CAAC;gBAG/F,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC;gBACvC,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;oBAC/C,OAAO,CAAC,GAAG,CAAC,QAAQ,MAAM,EAAE,CAAC,CAAC;gBAChC,CAAC,CAAC,CAAC;gBAGH,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;gBAC7C,MAAM,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE;oBACpD,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,WAAW,CAAC,WAAW,EAAE,KAAK,GAAG,CAAC,iBAAiB,EAAE,CAAC,CAAC;oBACvF,OAAO,CAAC,GAAG,CAAC,cAAc,GAAG,CAAC,mBAAmB,kBAAkB,GAAG,CAAC,cAAc,EAAE,CAAC,CAAC;oBACzF,OAAO,CAAC,GAAG,CAAC,oBAAoB,GAAG,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC;gBACxE,CAAC,CAAC,CAAC;gBAEH,IAAI,MAAM,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACtC,OAAO,CAAC,GAAG,CAAC,cAAc,MAAM,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,uBAAuB,CAAC,CAAC;gBACtF,CAAC;gBAGD,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC;gBACvC,OAAO,CAAC,GAAG,CAAC,uBAAuB,cAAc,IAAI,CAAC,CAAC;gBACvD,OAAO,CAAC,GAAG,CAAC,2BAA2B,MAAM,CAAC,QAAQ,CAAC,kBAAkB,EAAE,CAAC,CAAC;gBAC7E,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,MAAM,CAAC,QAAQ,CAAC,eAAe,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;gBAC3F,OAAO,CAAC,GAAG,CAAC,6BAA6B,MAAM,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC,CAAC;gBAG1E,MAAM,OAAO,GAAG;oBACd,SAAS,EAAE,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,cAAc,KAAK,MAAM,CAAC,CAAC,MAAM;oBACjF,WAAW,EAAE,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,cAAc,KAAK,QAAQ,CAAC,CAAC,MAAM;oBACrF,QAAQ,EAAE,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,cAAc,KAAK,KAAK,CAAC,CAAC,MAAM;oBAC/E,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,mBAAmB,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC;iBAC/H,CAAC;gBAEF,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;gBAC7B,OAAO,CAAC,GAAG,CAAC,yBAAyB,OAAO,CAAC,SAAS,UAAU,OAAO,CAAC,WAAW,YAAY,OAAO,CAAC,QAAQ,MAAM,CAAC,CAAC;gBACvH,OAAO,CAAC,GAAG,CAAC,mBAAmB,OAAO,CAAC,OAAO,QAAQ,CAAC,CAAC;YAE1D,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,8BAA8B,KAAK,EAAE,CAAC,CAAC;YACvD,CAAC;YAGD,IAAI,KAAK,GAAG,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACrC,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAC;gBAC1D,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;YAC1D,CAAC;QACH,CAAC;QAGD,OAAO,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACnC,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;QACxC,OAAO,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAEjC,MAAM,YAAY,GAAG,MAAM,UAAU,CAAC,eAAe,EAAE,CAAC;QACxD,KAAK,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,CAAC;YAC/D,MAAM,UAAU,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;YACtC,OAAO,CAAC,GAAG,CAAC,MAAM,UAAU,IAAI,SAAS,KAAK,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC;QACpF,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACnC,OAAO,CAAC,GAAG,CAAC,sDAAsD,CAAC,CAAC;QACpE,OAAO,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAEnC,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;QAEzD,IAAI,KAAK,YAAY,KAAK,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;YAChE,OAAO,CAAC,GAAG,CAAC,4EAA4E,CAAC,CAAC;YAC1F,OAAO,CAAC,GAAG,CAAC,6CAA6C,CAAC,CAAC;YAC3D,OAAO,CAAC,GAAG,CAAC,8CAA8C,CAAC,CAAC;YAC5D,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAC;YACrD,OAAO,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;YACnD,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAC;YACrD,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;QACzD,CAAC;IACH,CAAC;AACH,CAAC;AAKD,KAAK,UAAU,qBAAqB;IAClC,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;IAEtD,IAAI,CAAC;QACH,MAAM,UAAU,GAAG,IAAI,iEAA+B,EAAE,CAAC;QACzD,MAAM,UAAU,CAAC,UAAU,EAAE,CAAC;QAG9B,OAAO,CAAC,GAAG,CAAC,kDAAkD,CAAC,CAAC;QAEhE,MAAM,UAAU,GAAG;YACjB,oBAAoB,EAAE;gBACpB;oBACE,SAAS,EAAE,QAAQ;oBACnB,OAAO,EAAE,OAAO;oBAChB,SAAS,EAAE,SAAkB;oBAC7B,UAAU,EAAE,YAAY;oBACxB,GAAG,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE;oBACtC,aAAa,EAAE,IAAI;oBACnB,SAAS,EAAE,SAAkB;oBAC7B,YAAY,EAAE,KAAK;oBACnB,kBAAkB,EAAE,IAAI;iBACzB;aACF;YACD,mBAAmB,EAAE,EAAE;YACvB,wBAAwB,EAAE,EAAE;YAC5B,gBAAgB,EAAE;gBAChB,sBAAsB,EAAE;oBACtB,SAAS,EAAE,OAAO;oBAClB,QAAQ,EAAE,CAAC,QAAQ,EAAE,WAAW,CAAC;oBACjC,gBAAgB,EAAE,WAAoB;iBACvC;aACF;YACD,kBAAkB,EAAE;gBAClB,cAAc,EAAE,IAAI;aACrB;YACD,eAAe,EAAE;gBACf,OAAO,EAAE,EAAE,WAAW,EAAE,GAAG,EAAE,iBAAiB,EAAE,CAAC,EAAE;gBACnD,MAAM,EAAE,EAAE,WAAW,EAAE,GAAG,EAAE,iBAAiB,EAAE,CAAC,EAAE;gBAClD,WAAW,EAAE,EAAE,WAAW,EAAE,GAAG,EAAE,iBAAiB,EAAE,CAAC,EAAE;aACxD;YACD,MAAM,EAAE,CAAC,2BAA2B,EAAE,yBAAyB,CAAC;SACjE,CAAC;QAGF,MAAM,UAAU,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC;QACpE,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;QAGjD,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;QAC5C,MAAM,WAAW,GAAG,MAAM,UAAU,CAAC,YAAY,CAAC,kDAAkD,CAAC,CAAC;QAEtG,OAAO,CAAC,GAAG,CAAC,qBAAqB,WAAW,CAAC,eAAe,CAAC,MAAM,kBAAkB,CAAC,CAAC;QACvF,OAAO,CAAC,GAAG,CAAC,gBAAgB,WAAW,CAAC,QAAQ,CAAC,kBAAkB,qBAAqB,CAAC,CAAC;IAE5F,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;IACrD,CAAC;AACH,CAAC;AAGD,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;IAC5B,CAAC,KAAK,IAAI,EAAE;QACV,MAAM,oBAAoB,EAAE,CAAC;QAC7B,MAAM,qBAAqB,EAAE,CAAC;IAChC,CAAC,CAAC,EAAE,CAAC;AACP,CAAC"}