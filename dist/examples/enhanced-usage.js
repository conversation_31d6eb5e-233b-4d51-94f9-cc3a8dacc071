"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.enhancedUsageExample = enhancedUsageExample;
exports.demonstrateRAGStorage = demonstrateRAGStorage;
const EnhancedTravelCachingStrategist_1 = require("../analyzer/EnhancedTravelCachingStrategist");
async function enhancedUsageExample() {
    console.log('🚀 Enhanced Travel Caching Strategist - RAG + MCP Example\n');
    try {
        console.log('🔧 Initializing Enhanced Travel Caching Strategist...');
        const strategist = new EnhancedTravelCachingStrategist_1.EnhancedTravelCachingStrategist();
        console.log('⚠️  Note: This example uses synthetic data as API keys are not configured');
        await strategist.initialize();
        console.log('✅ Enhanced strategist initialized successfully\n');
        const sampleQueries = [
            {
                query: "What should I cache for flights from Mumbai to Delhi next week?",
                description: "Flight-specific query with location and time context"
            },
            {
                query: "Cache recommendations for hotels in Goa during Independence Day weekend",
                description: "Hotel query with event awareness"
            },
            {
                query: "Analyze caching needs for Thailand experiences in August",
                description: "Experience-based query for international destination"
            },
            {
                query: "What travel content should I pre-cache for the upcoming festival season?",
                description: "General travel query with seasonal context"
            }
        ];
        for (const [index, { query, description }] of sampleQueries.entries()) {
            console.log(`\n${'='.repeat(80)}`);
            console.log(`📝 Query ${index + 1}: ${description}`);
            console.log(`🔍 "${query}"`);
            console.log(`${'='.repeat(80)}\n`);
            try {
                const startTime = Date.now();
                const result = await strategist.analyzeQuery(query);
                const processingTime = Date.now() - startTime;
                console.log('🧠 Intent Analysis:');
                console.log(`   Type: ${result.ragContext.intent.type}`);
                console.log(`   Locations: ${result.ragContext.intent.locations.join(', ') || 'None detected'}`);
                console.log(`   Confidence: ${(result.ragContext.intent.confidence * 100).toFixed(1)}%`);
                if (result.ragContext.intent.dateRange) {
                    console.log(`   Date Range: ${result.ragContext.intent.dateRange.start} to ${result.ragContext.intent.dateRange.end}`);
                }
                console.log(`   Entities: ${result.ragContext.intent.entities.join(', ') || 'None detected'}`);
                console.log('\n📊 Data Sources Used:');
                result.metadata.dataSourcesUsed.forEach(source => {
                    console.log(`   • ${source}`);
                });
                console.log('\n🎯 Caching Recommendations:');
                result.recommendations.slice(0, 3).forEach((rec, i) => {
                    console.log(`   ${i + 1}. ${rec.entity_type.toUpperCase()}: ${rec.entity_id_or_name}`);
                    console.log(`      TTL: ${rec.suggested_ttl_hours} hours | Risk: ${rec.staleness_risk}`);
                    console.log(`      Reasoning: ${rec.reasoning.substring(0, 100)}...`);
                });
                if (result.recommendations.length > 3) {
                    console.log(`   ... and ${result.recommendations.length - 3} more recommendations`);
                }
                console.log('\n📈 Analysis Metadata:');
                console.log(`   Processing Time: ${processingTime}ms`);
                console.log(`   Retrieved Documents: ${result.metadata.retrievedDocuments}`);
                console.log(`   Confidence Score: ${(result.metadata.confidenceScore * 100).toFixed(1)}%`);
                console.log(`   Total Recommendations: ${result.recommendations.length}`);
                const summary = {
                    high_risk: result.recommendations.filter(r => r.staleness_risk === 'High').length,
                    medium_risk: result.recommendations.filter(r => r.staleness_risk === 'Medium').length,
                    low_risk: result.recommendations.filter(r => r.staleness_risk === 'Low').length,
                    avg_ttl: Math.round(result.recommendations.reduce((sum, r) => sum + r.suggested_ttl_hours, 0) / result.recommendations.length),
                };
                console.log('\n📋 Summary:');
                console.log(`   Risk Distribution: ${summary.high_risk} High, ${summary.medium_risk} Medium, ${summary.low_risk} Low`);
                console.log(`   Average TTL: ${summary.avg_ttl} hours`);
            }
            catch (error) {
                console.error(`❌ Failed to process query: ${error}`);
            }
            if (index < sampleQueries.length - 1) {
                console.log('\n⏳ Waiting 2 seconds before next query...');
                await new Promise(resolve => setTimeout(resolve, 2000));
            }
        }
        console.log(`\n${'='.repeat(80)}`);
        console.log('🏥 System Health Status:');
        console.log(`${'='.repeat(80)}`);
        const healthStatus = await strategist.getHealthStatus();
        for (const [component, status] of Object.entries(healthStatus)) {
            const statusIcon = status ? '✅' : '❌';
            console.log(`   ${statusIcon} ${component}: ${status ? 'Healthy' : 'Unhealthy'}`);
        }
        console.log(`\n${'='.repeat(80)}`);
        console.log('🎉 Enhanced Travel Caching Strategist Demo Complete!');
        console.log(`${'='.repeat(80)}`);
    }
    catch (error) {
        console.error('❌ Enhanced usage example failed:', error);
        if (error instanceof Error && error.message.includes('API key')) {
            console.log('\n💡 Tip: To use real data, configure the following environment variables:');
            console.log('   • PINECONE_API_KEY - for vector database');
            console.log('   • OPENAI_API_KEY - for embeddings and NLP');
            console.log('   • FLIGHT_API_KEY - for flight data');
            console.log('   • HOTEL_API_KEY - for hotel data');
            console.log('   • EVENTS_API_KEY - for events data');
            console.log('   • WEATHER_API_KEY - for weather data');
        }
    }
}
async function demonstrateRAGStorage() {
    console.log('\n🗄️ RAG Storage and Retrieval Demo\n');
    try {
        const strategist = new EnhancedTravelCachingStrategist_1.EnhancedTravelCachingStrategist();
        await strategist.initialize();
        console.log('💾 Storing sample travel data in RAG database...');
        const sampleData = {
            flight_search_trends: [
                {
                    from_city: "Mumbai",
                    to_city: "Delhi",
                    trip_type: "One Way",
                    start_date: "2025-08-15",
                    pax: { adult: 1, child: 0, infant: 0 },
                    is_refundable: true,
                    fare_type: "Regular",
                    search_count: 15000,
                    booking_conversion: 0.45
                }
            ],
            hotel_search_trends: [],
            experience_search_trends: [],
            content_metadata: {
                "Mumbai–Delhi flights": {
                    avg_price: "₹4500",
                    airlines: ["IndiGo", "Air India"],
                    inventory_status: "Available"
                }
            },
            booking_conversion: {
                "Mumbai–Delhi": 0.45
            },
            api_performance: {
                flights: { avg_latency: 2.1, failures_last_24h: 2 },
                hotels: { avg_latency: 1.6, failures_last_24h: 0 },
                experiences: { avg_latency: 2.3, failures_last_24h: 1 }
            },
            events: ["Independence Day (15 Aug)", "Raksha Bandhan (19 Aug)"]
        };
        await strategist['ragEngine'].storeData(sampleData, 'demo_storage');
        console.log('✅ Sample data stored successfully');
        console.log('\n🔍 Querying stored data...');
        const queryResult = await strategist.analyzeQuery("Show me flight caching for Mumbai to Delhi route");
        console.log(`📊 Query returned ${queryResult.recommendations.length} recommendations`);
        console.log(`📚 Retrieved ${queryResult.metadata.retrievedDocuments} documents from RAG`);
    }
    catch (error) {
        console.error('❌ RAG storage demo failed:', error);
    }
}
if (require.main === module) {
    (async () => {
        await enhancedUsageExample();
        await demonstrateRAGStorage();
    })();
}
//# sourceMappingURL=enhanced-usage.js.map