"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.initializeEnhancedStrategist = initializeEnhancedStrategist;
const express_1 = require("express");
const TravelCachingStrategist_1 = require("../analyzer/TravelCachingStrategist");
const EnhancedTravelCachingStrategist_1 = require("../analyzer/EnhancedTravelCachingStrategist");
const router = (0, express_1.Router)();
let enhancedStrategist = null;
async function initializeEnhancedStrategist() {
    if (!enhancedStrategist) {
        enhancedStrategist = new EnhancedTravelCachingStrategist_1.EnhancedTravelCachingStrategist();
        await enhancedStrategist.initialize();
    }
}
router.get('/health', async (req, res) => {
    const healthStatus = enhancedStrategist
        ? await enhancedStrategist.getHealthStatus()
        : { enhanced_strategist: false };
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        service: 'Travel Caching Strategist API (RAG + MCP Enhanced)',
        components: healthStatus,
    });
});
router.post('/analyze', async (req, res) => {
    try {
        const input = req.body;
        if (!input) {
            res.status(400).json({
                error: 'Missing request body',
                message: 'Please provide travel analysis input data',
            });
            return;
        }
        const validationError = validateInput(input);
        if (validationError) {
            res.status(400).json({
                error: 'Invalid input data',
                message: validationError,
            });
            return;
        }
        const analyzer = new TravelCachingStrategist_1.TravelCachingStrategist();
        const recommendations = await analyzer.analyze(input);
        res.json({
            success: true,
            timestamp: new Date().toISOString(),
            recommendations,
            summary: {
                total_recommendations: recommendations.length,
                high_risk_count: recommendations.filter(r => r.staleness_risk === 'High').length,
                medium_risk_count: recommendations.filter(r => r.staleness_risk === 'Medium').length,
                low_risk_count: recommendations.filter(r => r.staleness_risk === 'Low').length,
                avg_ttl_hours: Math.round(recommendations.reduce((sum, r) => sum + r.suggested_ttl_hours, 0) / recommendations.length),
            },
        });
    }
    catch (error) {
        console.error('Analysis error:', error);
        res.status(500).json({
            error: 'Internal server error',
            message: 'Failed to process travel analysis request',
            timestamp: new Date().toISOString(),
        });
    }
});
router.post('/query', async (req, res) => {
    try {
        const { query } = req.body;
        if (!query || typeof query !== 'string') {
            res.status(400).json({
                error: 'Missing or invalid query',
                message: 'Please provide a natural language query string',
                example: 'What should I cache for flights from Mumbai to Delhi next week?',
            });
            return;
        }
        if (!enhancedStrategist) {
            res.status(503).json({
                error: 'Service not ready',
                message: 'Enhanced strategist is not initialized yet',
            });
            return;
        }
        const result = await enhancedStrategist.analyzeQuery(query);
        res.json({
            success: true,
            timestamp: new Date().toISOString(),
            query: result.query,
            intent: result.ragContext.intent,
            recommendations: result.recommendations,
            metadata: result.metadata,
            summary: {
                total_recommendations: result.recommendations.length,
                high_risk_count: result.recommendations.filter(r => r.staleness_risk === 'High').length,
                medium_risk_count: result.recommendations.filter(r => r.staleness_risk === 'Medium').length,
                low_risk_count: result.recommendations.filter(r => r.staleness_risk === 'Low').length,
                avg_ttl_hours: Math.round(result.recommendations.reduce((sum, r) => sum + r.suggested_ttl_hours, 0) / result.recommendations.length),
                processing_time_ms: result.metadata.processingTime,
                data_sources: result.metadata.dataSourcesUsed,
                confidence_score: Math.round(result.metadata.confidenceScore * 100) / 100,
            },
        });
    }
    catch (error) {
        console.error('Enhanced query analysis error:', error);
        res.status(500).json({
            error: 'Internal server error',
            message: 'Failed to process natural language query',
            timestamp: new Date().toISOString(),
        });
    }
});
router.get('/sample-input', (req, res) => {
    const sampleInput = {
        flight_search_trends: [
            {
                from_city: "Mumbai",
                to_city: "Delhi",
                trip_type: "One Way",
                start_date: "2025-08-14",
                pax: { adult: 1, child: 1, infant: 0 },
                is_refundable: true,
                fare_type: "Regular",
                search_count: 13200,
                booking_conversion: 0.42
            }
        ],
        hotel_search_trends: [
            {
                city: "Goa",
                checkin_date: "2025-08-15",
                checkout_date: "2025-08-17",
                rooms: 1,
                guests: { adult: 2, child: 1, child_ages: [7] },
                search_count: 11400,
                booking_conversion: 0.36
            }
        ],
        experience_search_trends: [
            {
                region: "Dubai Desert",
                checkin_date: "2025-08-16",
                checkout_date: "2025-08-16",
                search_count: 5400
            }
        ],
        content_metadata: {
            "Mumbai–Delhi flights": {
                avg_price: "₹4200",
                airlines: ["IndiGo", "Air India"],
                inventory_status: "Available"
            },
            "Goa 3-star hotels": {
                avg_price: "₹2800",
                rating: 4.1,
                inventory_status: "Available"
            }
        },
        booking_conversion: {
            "Mumbai–Delhi": 0.42,
            "Goa hotels": 0.36,
            "Dubai Desert": 0.35
        },
        api_performance: {
            flights: { avg_latency: 2.1, failures_last_24h: 3 },
            hotels: { avg_latency: 1.6, failures_last_24h: 1 },
            experiences: { avg_latency: 2.4, failures_last_24h: 2 }
        },
        events: ["Independence Day (15 Aug)", "Raksha Bandhan (18 Aug)"]
    };
    res.json({
        message: "Sample input data for testing the travel caching strategist",
        sample_input: sampleInput
    });
});
router.get('/sample-queries', (req, res) => {
    const sampleQueries = [
        {
            query: "What should I cache for flights from Mumbai to Delhi next week?",
            description: "Flight-specific caching recommendations for a popular domestic route"
        },
        {
            query: "Cache recommendations for hotels in Goa during Independence Day weekend",
            description: "Hotel caching with event-aware analysis"
        },
        {
            query: "Analyze caching needs for Thailand experiences in August",
            description: "Experience-based caching for international destination"
        },
        {
            query: "What travel content should I pre-cache for the upcoming festival season?",
            description: "General travel caching with seasonal considerations"
        },
        {
            query: "Cache strategy for Paris attractions and hotels for summer vacation",
            description: "Multi-domain caching for international city"
        },
        {
            query: "Pre-cache recommendations for Manali cottages with limited inventory",
            description: "Inventory-aware caching for hill stations"
        }
    ];
    res.json({
        message: "Sample natural language queries for the enhanced RAG + MCP travel caching strategist",
        sample_queries: sampleQueries,
        usage: {
            endpoint: "POST /api/v1/query",
            body: { query: "Your natural language query here" }
        }
    });
});
function validateInput(input) {
    if (!input.flight_search_trends && !input.hotel_search_trends && !input.experience_search_trends) {
        return 'At least one search trend category is required';
    }
    if (!input.api_performance) {
        return 'API performance data is required';
    }
    if (!input.content_metadata) {
        return 'Content metadata is required';
    }
    if (!input.booking_conversion) {
        return 'Booking conversion data is required';
    }
    return null;
}
exports.default = router;
//# sourceMappingURL=routes.js.map