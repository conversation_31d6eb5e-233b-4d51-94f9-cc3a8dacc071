"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const TravelCachingStrategist_1 = require("../analyzer/TravelCachingStrategist");
const router = (0, express_1.Router)();
router.get('/health', (req, res) => {
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        service: 'Travel Caching Strategist API',
    });
});
router.post('/analyze', async (req, res) => {
    try {
        const input = req.body;
        if (!input) {
            res.status(400).json({
                error: 'Missing request body',
                message: 'Please provide travel analysis input data',
            });
            return;
        }
        const validationError = validateInput(input);
        if (validationError) {
            res.status(400).json({
                error: 'Invalid input data',
                message: validationError,
            });
            return;
        }
        const analyzer = new TravelCachingStrategist_1.TravelCachingStrategist();
        const recommendations = await analyzer.analyze(input);
        res.json({
            success: true,
            timestamp: new Date().toISOString(),
            recommendations,
            summary: {
                total_recommendations: recommendations.length,
                high_risk_count: recommendations.filter(r => r.staleness_risk === 'High').length,
                medium_risk_count: recommendations.filter(r => r.staleness_risk === 'Medium').length,
                low_risk_count: recommendations.filter(r => r.staleness_risk === 'Low').length,
                avg_ttl_hours: Math.round(recommendations.reduce((sum, r) => sum + r.suggested_ttl_hours, 0) / recommendations.length),
            },
        });
    }
    catch (error) {
        console.error('Analysis error:', error);
        res.status(500).json({
            error: 'Internal server error',
            message: 'Failed to process travel analysis request',
            timestamp: new Date().toISOString(),
        });
    }
});
router.get('/sample-input', (req, res) => {
    const sampleInput = {
        flight_search_trends: [
            {
                from_city: "Mumbai",
                to_city: "Delhi",
                trip_type: "One Way",
                start_date: "2025-08-14",
                pax: { adult: 1, child: 1, infant: 0 },
                is_refundable: true,
                fare_type: "Regular",
                search_count: 13200,
                booking_conversion: 0.42
            }
        ],
        hotel_search_trends: [
            {
                city: "Goa",
                checkin_date: "2025-08-15",
                checkout_date: "2025-08-17",
                rooms: 1,
                guests: { adult: 2, child: 1, child_ages: [7] },
                search_count: 11400,
                booking_conversion: 0.36
            }
        ],
        experience_search_trends: [
            {
                region: "Dubai Desert",
                checkin_date: "2025-08-16",
                checkout_date: "2025-08-16",
                search_count: 5400
            }
        ],
        content_metadata: {
            "Mumbai–Delhi flights": {
                avg_price: "₹4200",
                airlines: ["IndiGo", "Air India"],
                inventory_status: "Available"
            },
            "Goa 3-star hotels": {
                avg_price: "₹2800",
                rating: 4.1,
                inventory_status: "Available"
            }
        },
        booking_conversion: {
            "Mumbai–Delhi": 0.42,
            "Goa hotels": 0.36,
            "Dubai Desert": 0.35
        },
        api_performance: {
            flights: { avg_latency: 2.1, failures_last_24h: 3 },
            hotels: { avg_latency: 1.6, failures_last_24h: 1 },
            experiences: { avg_latency: 2.4, failures_last_24h: 2 }
        },
        events: ["Independence Day (15 Aug)", "Raksha Bandhan (18 Aug)"]
    };
    res.json({
        message: "Sample input data for testing the travel caching strategist",
        sample_input: sampleInput
    });
});
function validateInput(input) {
    if (!input.flight_search_trends && !input.hotel_search_trends && !input.experience_search_trends) {
        return 'At least one search trend category is required';
    }
    if (!input.api_performance) {
        return 'API performance data is required';
    }
    if (!input.content_metadata) {
        return 'Content metadata is required';
    }
    if (!input.booking_conversion) {
        return 'Booking conversion data is required';
    }
    return null;
}
exports.default = router;
//# sourceMappingURL=routes.js.map