"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const config_1 = require("./config");
const routes_1 = __importDefault(require("./api/routes"));
const app = (0, express_1.default)();
app.use((0, helmet_1.default)());
app.use((0, cors_1.default)({
    origin: config_1.Config.isDevelopment() ? '*' : process.env.ALLOWED_ORIGINS?.split(',') || [],
    methods: ['GET', 'POST'],
    allowedHeaders: ['Content-Type', 'Authorization'],
}));
app.use(express_1.default.json({ limit: '10mb' }));
app.use(express_1.default.urlencoded({ extended: true, limit: '10mb' }));
if (config_1.Config.isDevelopment()) {
    app.use((req, res, next) => {
        console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
        next();
    });
}
app.use('/api/v1', routes_1.default);
app.get('/', (req, res) => {
    res.json({
        service: 'Travel Caching Strategist API',
        version: '2.0.0',
        description: 'AI-powered travel content pre-caching optimization service with RAG + MCP',
        features: [
            'Natural Language Query Processing',
            'RAG (Retrieval-Augmented Generation)',
            'MCP (Model Context Protocol)',
            'Real-time Data Integration',
            'Vector Database Storage',
            'Intelligent Context Retrieval'
        ],
        endpoints: {
            health: '/api/v1/health',
            query: '/api/v1/query (NEW - Natural Language)',
            analyze: '/api/v1/analyze (Legacy - JSON Input)',
            sample_input: '/api/v1/sample-input',
            sample_queries: '/api/v1/sample-queries (NEW)',
        },
        documentation: {
            query: {
                method: 'POST',
                description: 'Analyze natural language query using RAG + MCP',
                input: '{ "query": "What should I cache for flights from Mumbai to Delhi?" }',
                output: 'Enhanced analysis with RAG context and MCP data',
            },
            analyze: {
                method: 'POST',
                description: 'Analyze travel data and generate caching recommendations (Legacy)',
                input: 'TravelAnalysisInput JSON object',
                output: 'Array of CachingRecommendation objects',
            },
        },
    });
});
app.use('*', (req, res) => {
    res.status(404).json({
        error: 'Not Found',
        message: `Route ${req.originalUrl} not found`,
        timestamp: new Date().toISOString(),
    });
});
app.use((err, req, res, next) => {
    console.error('Unhandled error:', err);
    res.status(err.status || 500).json({
        error: 'Internal Server Error',
        message: config_1.Config.isDevelopment() ? err.message : 'Something went wrong',
        timestamp: new Date().toISOString(),
    });
});
exports.default = app;
//# sourceMappingURL=app.js.map