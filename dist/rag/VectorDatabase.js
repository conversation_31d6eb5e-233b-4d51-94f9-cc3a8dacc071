"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VectorDatabase = void 0;
const pinecone_1 = require("@pinecone-database/pinecone");
const openai_1 = __importDefault(require("openai"));
const uuid_1 = require("uuid");
class VectorDatabase {
    constructor() {
        this.pinecone = new pinecone_1.Pinecone({
            apiKey: process.env.PINECONE_API_KEY || '',
        });
        this.openai = new openai_1.default({
            apiKey: process.env.OPENAI_API_KEY || '',
        });
        this.indexName = process.env.PINECONE_INDEX_NAME || 'travel-cache-strategist';
    }
    async initialize() {
        try {
            const indexList = await this.pinecone.listIndexes();
            const indexExists = indexList.indexes?.some(index => index.name === this.indexName);
            if (!indexExists) {
                console.log(`Creating Pinecone index: ${this.indexName}`);
                await this.pinecone.createIndex({
                    name: this.indexName,
                    dimension: 1536,
                    metric: 'cosine',
                    spec: {
                        serverless: {
                            cloud: 'aws',
                            region: 'us-east-1'
                        }
                    }
                });
                await this.waitForIndexReady();
            }
            console.log(`Vector database initialized with index: ${this.indexName}`);
        }
        catch (error) {
            console.error('Failed to initialize vector database:', error);
            throw error;
        }
    }
    async waitForIndexReady() {
        let isReady = false;
        let attempts = 0;
        const maxAttempts = 30;
        while (!isReady && attempts < maxAttempts) {
            try {
                const indexStats = await this.pinecone.index(this.indexName).describeIndexStats();
                isReady = true;
                console.log('Index is ready:', indexStats);
            }
            catch (error) {
                attempts++;
                console.log(`Waiting for index to be ready... (${attempts}/${maxAttempts})`);
                await new Promise(resolve => setTimeout(resolve, 2000));
            }
        }
        if (!isReady) {
            throw new Error('Index failed to become ready within timeout');
        }
    }
    async generateEmbedding(text) {
        try {
            const response = await this.openai.embeddings.create({
                model: 'text-embedding-ada-002',
                input: text,
            });
            return response.data[0]?.embedding || [];
        }
        catch (error) {
            console.error('Failed to generate embedding:', error);
            throw error;
        }
    }
    async storeDocument(document) {
        try {
            const embedding = await this.generateEmbedding(document.content);
            const index = this.pinecone.index(this.indexName);
            await index.upsert([
                {
                    id: document.id,
                    values: embedding,
                    metadata: {
                        content: document.content,
                        ...document.metadata,
                    },
                },
            ]);
            console.log(`Stored document: ${document.id}`);
        }
        catch (error) {
            console.error('Failed to store document:', error);
            throw error;
        }
    }
    async storeDocuments(documents) {
        try {
            const vectors = await Promise.all(documents.map(async (doc) => {
                const embedding = await this.generateEmbedding(doc.content);
                return {
                    id: doc.id,
                    values: embedding,
                    metadata: {
                        content: doc.content,
                        ...doc.metadata,
                    },
                };
            }));
            const index = this.pinecone.index(this.indexName);
            await index.upsert(vectors);
            console.log(`Stored ${documents.length} documents in batch`);
        }
        catch (error) {
            console.error('Failed to store documents:', error);
            throw error;
        }
    }
    async searchSimilar(query, topK = 10, filter) {
        try {
            const queryEmbedding = await this.generateEmbedding(query);
            const index = this.pinecone.index(this.indexName);
            const queryOptions = {
                vector: queryEmbedding,
                topK,
                includeMetadata: true,
            };
            if (filter) {
                queryOptions.filter = filter;
            }
            const searchResponse = await index.query(queryOptions);
            return searchResponse.matches?.map(match => ({
                document: {
                    id: match.id || '',
                    content: match.metadata?.content || '',
                    metadata: {
                        type: match.metadata?.type || 'analytics',
                        location: match.metadata?.location,
                        date: match.metadata?.date,
                        source: match.metadata?.source || 'unknown',
                        timestamp: match.metadata?.timestamp || Date.now(),
                        ...match.metadata,
                    },
                },
                score: match.score || 0,
            })) || [];
        }
        catch (error) {
            console.error('Failed to search documents:', error);
            throw error;
        }
    }
    async deleteDocuments(filter) {
        try {
            const index = this.pinecone.index(this.indexName);
            await index.deleteMany(filter);
            console.log('Deleted documents with filter:', filter);
        }
        catch (error) {
            console.error('Failed to delete documents:', error);
            throw error;
        }
    }
    async getStats() {
        try {
            const index = this.pinecone.index(this.indexName);
            return await index.describeIndexStats();
        }
        catch (error) {
            console.error('Failed to get index stats:', error);
            throw error;
        }
    }
    static createTravelDocument(content, type, metadata = {}) {
        return {
            id: (0, uuid_1.v4)(),
            content,
            metadata: {
                type,
                source: 'api',
                timestamp: Date.now(),
                ...metadata,
            },
        };
    }
}
exports.VectorDatabase = VectorDatabase;
//# sourceMappingURL=VectorDatabase.js.map