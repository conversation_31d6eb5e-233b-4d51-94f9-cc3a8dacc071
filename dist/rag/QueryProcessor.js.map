{"version": 3, "file": "QueryProcessor.js", "sourceRoot": "", "sources": ["../../src/rag/QueryProcessor.ts"], "names": [], "mappings": ";;;;;;AAAA,oDAA4B;AAa5B,MAAa,cAAc;IAGzB;QACE,IAAI,CAAC,MAAM,GAAG,IAAI,gBAAM,CAAC;YACvB,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI,EAAE;SACzC,CAAC,CAAC;IACL,CAAC;IAKD,KAAK,CAAC,WAAW,CAAC,KAAa;QAC7B,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,IAAI,CAAC,2BAA2B,CAAC,KAAK,CAAC,CAAC;YAEvD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBACzD,KAAK,EAAE,eAAe;gBACtB,QAAQ,EAAE;oBACR;wBACE,IAAI,EAAE,QAAQ;wBACd,OAAO,EAAE,4FAA4F;qBACtG;oBACD;wBACE,IAAI,EAAE,MAAM;wBACZ,OAAO,EAAE,MAAM;qBAChB;iBACF;gBACD,WAAW,EAAE,GAAG;gBAChB,UAAU,EAAE,GAAG;aAChB,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,OAAO,CAAC;YACrD,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;YAC7C,CAAC;YAED,OAAO,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;QACjD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,yEAAyE,EAAE,KAAK,CAAC,CAAC;YAChG,OAAO,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC;QAC3C,CAAC;IACH,CAAC;IAKO,2BAA2B,CAAC,KAAa;QAC/C,OAAO;;;UAGD,KAAK;;;;;;;;;;;;;;;;;;;;;;;CAuBd,CAAC;IACA,CAAC;IAKO,mBAAmB,CAAC,QAAgB,EAAE,aAAqB;QACjE,IAAI,CAAC;YAEH,MAAM,SAAS,GAAG,QAAQ,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;YAChD,IAAI,CAAC,SAAS,EAAE,CAAC;gBACf,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;YAC/C,CAAC;YAED,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;YAExC,OAAO;gBACL,IAAI,EAAE,MAAM,CAAC,IAAI,IAAI,SAAS;gBAC9B,SAAS,EAAE,MAAM,CAAC,SAAS,IAAI,EAAE;gBACjC,SAAS,EAAE,MAAM,CAAC,SAAS;gBAC3B,QAAQ,EAAE,MAAM,CAAC,QAAQ,IAAI,EAAE;gBAC/B,UAAU,EAAE,MAAM,CAAC,UAAU,IAAI,GAAG;aACrC,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;YACzD,OAAO,IAAI,CAAC,qBAAqB,CAAC,aAAa,CAAC,CAAC;QACnD,CAAC;IACH,CAAC;IAKO,qBAAqB,CAAC,KAAa;QACzC,MAAM,UAAU,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;QAGvC,IAAI,IAAI,GAAkD,SAAS,CAAC;QACpE,IAAI,IAAI,CAAC,sBAAsB,CAAC,UAAU,CAAC,EAAE,CAAC;YAC5C,IAAI,GAAG,QAAQ,CAAC;QAClB,CAAC;aAAM,IAAI,IAAI,CAAC,qBAAqB,CAAC,UAAU,CAAC,EAAE,CAAC;YAClD,IAAI,GAAG,OAAO,CAAC;QACjB,CAAC;aAAM,IAAI,IAAI,CAAC,0BAA0B,CAAC,UAAU,CAAC,EAAE,CAAC;YACvD,IAAI,GAAG,YAAY,CAAC;QACtB,CAAC;QAGD,MAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;QAG/C,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;QAG7C,MAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;QAE/C,OAAO;YACL,IAAI;YACJ,SAAS;YACT,SAAS;YACT,QAAQ;YACR,UAAU,EAAE,GAAG;SAChB,CAAC;IACJ,CAAC;IAKO,sBAAsB,CAAC,KAAa;QAC1C,MAAM,cAAc,GAAG;YACrB,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,UAAU;YAC/D,WAAW,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU;YACnE,UAAU,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,YAAY;SACpD,CAAC;QACF,OAAO,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;IACjE,CAAC;IAKO,qBAAqB,CAAC,KAAa;QACzC,MAAM,aAAa,GAAG;YACpB,OAAO,EAAE,QAAQ,EAAE,eAAe,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO;YAC3D,SAAS,EAAE,aAAa,EAAE,UAAU,EAAE,WAAW,EAAE,QAAQ;YAC3D,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS;SAC5C,CAAC;QACF,OAAO,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;IAChE,CAAC;IAKO,0BAA0B,CAAC,KAAa;QAC9C,MAAM,kBAAkB,GAAG;YACzB,YAAY,EAAE,aAAa,EAAE,UAAU,EAAE,YAAY,EAAE,MAAM,EAAE,OAAO;YACtE,YAAY,EAAE,aAAa,EAAE,aAAa,EAAE,WAAW,EAAE,WAAW;YACpE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,eAAe;SACzD,CAAC;QACF,OAAO,kBAAkB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;IACrE,CAAC;IAKO,gBAAgB,CAAC,KAAa;QACpC,MAAM,SAAS,GAAa,EAAE,CAAC;QAG/B,MAAM,YAAY,GAAG;YACnB,mKAAmK;YACnK,4HAA4H;YAC5H,8HAA8H;YAC9H,mFAAmF;YACnF,+DAA+D;YAC/D,0DAA0D;YAC1D,4EAA4E;YAC5E,8DAA8D;YAC9D,+DAA+D;YAC/D,0GAA0G;SAC3G,CAAC;QAEF,YAAY,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YAC7B,MAAM,OAAO,GAAG,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YACrC,IAAI,OAAO,EAAE,CAAC;gBACZ,SAAS,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;YACxD,CAAC;QACH,CAAC,CAAC,CAAC;QAGH,OAAO,CAAC,GAAG,IAAI,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC;IACjC,CAAC;IAKO,eAAe,CAAC,KAAa;QACnC,MAAM,QAAQ,GAAa,EAAE,CAAC;QAG9B,MAAM,cAAc,GAAG,+MAA+M,CAAC;QACvO,MAAM,cAAc,GAAG,KAAK,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;QACnD,IAAI,cAAc,EAAE,CAAC;YACnB,QAAQ,CAAC,IAAI,CAAC,GAAG,cAAc,CAAC,CAAC;QACnC,CAAC;QAGD,MAAM,YAAY,GAAG,sIAAsI,CAAC;QAC5J,MAAM,YAAY,GAAG,KAAK,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;QAC/C,IAAI,YAAY,EAAE,CAAC;YACjB,QAAQ,CAAC,IAAI,CAAC,GAAG,YAAY,CAAC,CAAC;QACjC,CAAC;QAGD,MAAM,YAAY,GAAG,mJAAmJ,CAAC;QACzK,MAAM,YAAY,GAAG,KAAK,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;QAC/C,IAAI,YAAY,EAAE,CAAC;YACjB,QAAQ,CAAC,IAAI,CAAC,GAAG,YAAY,CAAC,CAAC;QACjC,CAAC;QAED,OAAO,CAAC,GAAG,IAAI,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC;IAChC,CAAC;IAKO,gBAAgB,CAAC,KAAa;QACpC,MAAM,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;QAG7C,MAAM,YAAY,GAAG;YACnB,sBAAsB;YACtB,4BAA4B;YAC5B,0BAA0B;SAC3B,CAAC;QAEF,MAAM,KAAK,GAAa,EAAE,CAAC;QAC3B,YAAY,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YAC7B,MAAM,OAAO,GAAG,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YACrC,IAAI,OAAO,EAAE,CAAC;gBACZ,KAAK,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,CAAC;YACzB,CAAC;QACH,CAAC,CAAC,CAAC;QAGH,MAAM,YAAY,GAAG,wHAAwH,CAAC;QAC9I,MAAM,YAAY,GAAG,KAAK,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;QAC/C,IAAI,YAAY,EAAE,CAAC;YACjB,YAAY,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;gBAC3B,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;gBACjC,MAAM,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;gBACvB,MAAM,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;gBACvC,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,IAAI,WAAW,CAAC,QAAQ,EAAE,CAAC;gBAEhD,MAAM,QAAQ,GAAG,IAAI,IAAI,CAAC,GAAG,KAAK,UAAU,CAAC,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;gBAC7D,MAAM,aAAa,GAAG,GAAG,IAAI,IAAI,QAAQ,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,GAAG,EAAE,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC;gBACjG,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAC5B,CAAC,CAAC,CAAC;QACL,CAAC;QAED,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;YACtB,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC;YACjC,OAAO;gBACL,KAAK,EAAE,WAAW,CAAC,CAAC,CAAE;gBACtB,GAAG,EAAE,WAAW,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAE;aAC1C,CAAC;QACJ,CAAC;aAAM,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC9B,OAAO;gBACL,KAAK,EAAE,KAAK,CAAC,CAAC,CAAE;gBAChB,GAAG,EAAE,KAAK,CAAC,CAAC,CAAE;aACf,CAAC;QACJ,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;CACF;AA9RD,wCA8RC"}