export interface QueryIntent {
    type: 'flight' | 'hotel' | 'experience' | 'general';
    locations: string[];
    dateRange?: {
        start: string;
        end: string;
    } | undefined;
    entities: string[];
    confidence: number;
}
export declare class QueryProcessor {
    private openai;
    constructor();
    parseIntent(query: string): Promise<QueryIntent>;
    private buildIntentExtractionPrompt;
    private parseIntentResponse;
    private fallbackIntentParsing;
    private containsFlightKeywords;
    private containsHotelKeywords;
    private containsExperienceKeywords;
    private extractLocations;
    private extractEntities;
    private extractDateRange;
}
//# sourceMappingURL=QueryProcessor.d.ts.map