import { SearchResult } from './VectorDatabase';
import { TravelAnalysisInput } from '../types';
export interface RAGContext {
    query: string;
    intent: {
        type: 'flight' | 'hotel' | 'experience' | 'general';
        locations: string[];
        dateRange?: {
            start: string;
            end: string;
        } | undefined;
        entities: string[];
        confidence: number;
    };
    retrievedDocuments: SearchResult[];
    synthesizedData: TravelAnalysisInput;
}
export declare class RAGEngine {
    private vectorDb;
    private queryProcessor;
    constructor();
    initialize(): Promise<void>;
    processQuery(query: string): Promise<RAGContext>;
    private retrieveRelevantContext;
    private deduplicateResults;
    private synthesizeData;
    private tryParseJSON;
    private mergeDataIntoSynthesis;
    private extractDataFromText;
    private ensureMinimumData;
    storeData(data: TravelAnalysisInput, source?: string): Promise<void>;
    private extractLocationsFromFlights;
    private extractLocationsFromHotels;
    getStats(): Promise<any>;
}
//# sourceMappingURL=RAGEngine.d.ts.map