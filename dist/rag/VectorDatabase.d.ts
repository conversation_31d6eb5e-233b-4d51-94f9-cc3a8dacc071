export interface TravelDocument {
    id: string;
    content: string;
    metadata: {
        type: 'flight' | 'hotel' | 'experience' | 'event' | 'analytics';
        location?: string;
        date?: string;
        source: string;
        timestamp: number;
        [key: string]: any;
    };
}
export interface SearchResult {
    document: TravelDocument;
    score: number;
}
export declare class VectorDatabase {
    private pinecone;
    private openai;
    private indexName;
    constructor();
    initialize(): Promise<void>;
    private waitForIndexReady;
    generateEmbedding(text: string): Promise<number[]>;
    storeDocument(document: TravelDocument): Promise<void>;
    storeDocuments(documents: TravelDocument[]): Promise<void>;
    searchSimilar(query: string, topK?: number, filter?: Record<string, any>): Promise<SearchResult[]>;
    deleteDocuments(filter: Record<string, any>): Promise<void>;
    getStats(): Promise<any>;
    static createTravelDocument(content: string, type: 'flight' | 'hotel' | 'experience' | 'event' | 'analytics', metadata?: Partial<TravelDocument['metadata']>): TravelDocument;
}
//# sourceMappingURL=VectorDatabase.d.ts.map