{"version": 3, "file": "RAGEngine.js", "sourceRoot": "", "sources": ["../../src/rag/RAGEngine.ts"], "names": [], "mappings": ";;;AAAA,qDAAgF;AAChF,qDAAkD;AAmBlD,MAAa,SAAS;IAIpB;QACE,IAAI,CAAC,QAAQ,GAAG,IAAI,+BAAc,EAAE,CAAC;QACrC,IAAI,CAAC,cAAc,GAAG,IAAI,+BAAc,EAAE,CAAC;IAC7C,CAAC;IAKD,KAAK,CAAC,UAAU;QACd,MAAM,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC;QACjC,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC;IACxC,CAAC;IAKD,KAAK,CAAC,YAAY,CAAC,KAAa;QAC9B,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,sBAAsB,KAAK,GAAG,CAAC,CAAC;YAG5C,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;YAC5D,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,MAAM,CAAC,CAAC;YAGtC,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;YAC7E,OAAO,CAAC,GAAG,CAAC,aAAa,kBAAkB,CAAC,MAAM,qBAAqB,CAAC,CAAC;YAGzE,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,kBAAkB,EAAE,MAAM,CAAC,CAAC;YAE9E,OAAO;gBACL,KAAK;gBACL,MAAM;gBACN,kBAAkB;gBAClB,eAAe;aAChB,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;YACjD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,uBAAuB,CACnC,KAAa,EACb,MAAW;QAEX,MAAM,aAAa,GAAmB,EAAE,CAAC;QAGzC,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QACnE,aAAa,CAAC,IAAI,CAAC,GAAG,cAAc,CAAC,CAAC;QAGtC,IAAI,MAAM,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;YAC9B,MAAM,UAAU,GAAG,EAAE,IAAI,EAAE,MAAM,CAAC,IAAI,EAAE,CAAC;YACzC,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC,EAAE,UAAU,CAAC,CAAC;YAC5E,aAAa,CAAC,IAAI,CAAC,GAAG,WAAW,CAAC,CAAC;QACrC,CAAC;QAGD,KAAK,MAAM,QAAQ,IAAI,MAAM,CAAC,SAAS,EAAE,CAAC;YACxC,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,aAAa,CACvD,GAAG,QAAQ,IAAI,MAAM,CAAC,IAAI,SAAS,EACnC,CAAC,EACD,EAAE,QAAQ,EAAE,CACb,CAAC;YACF,aAAa,CAAC,IAAI,CAAC,GAAG,eAAe,CAAC,CAAC;QACzC,CAAC;QAGD,MAAM,aAAa,GAAG,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,CAAC;QAC7D,OAAO,aAAa,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IACpC,CAAC;IAKO,kBAAkB,CAAC,OAAuB;QAChD,MAAM,IAAI,GAAG,IAAI,GAAG,EAAU,CAAC;QAC/B,OAAO,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;YAC7B,IAAI,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC;gBACjC,OAAO,KAAK,CAAC;YACf,CAAC;YACD,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YAC7B,OAAO,IAAI,CAAC;QACd,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;IACvC,CAAC;IAKO,KAAK,CAAC,cAAc,CAC1B,SAAyB,EACzB,MAAW;QAGX,MAAM,eAAe,GAAwB;YAC3C,oBAAoB,EAAE,EAAE;YACxB,mBAAmB,EAAE,EAAE;YACvB,wBAAwB,EAAE,EAAE;YAC5B,gBAAgB,EAAE,EAAE;YACpB,kBAAkB,EAAE,EAAE;YACtB,eAAe,EAAE;gBACf,OAAO,EAAE,EAAE,WAAW,EAAE,GAAG,EAAE,iBAAiB,EAAE,CAAC,EAAE;gBACnD,MAAM,EAAE,EAAE,WAAW,EAAE,GAAG,EAAE,iBAAiB,EAAE,CAAC,EAAE;gBAClD,WAAW,EAAE,EAAE,WAAW,EAAE,GAAG,EAAE,iBAAiB,EAAE,CAAC,EAAE;aACxD;YACD,MAAM,EAAE,EAAE;SACX,CAAC;QAGF,KAAK,MAAM,MAAM,IAAI,SAAS,EAAE,CAAC;YAC/B,MAAM,GAAG,GAAG,MAAM,CAAC,QAAQ,CAAC;YAE5B,IAAI,CAAC;gBAEH,MAAM,aAAa,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;gBAErD,IAAI,aAAa,EAAE,CAAC;oBAClB,IAAI,CAAC,sBAAsB,CAAC,eAAe,EAAE,aAAa,EAAE,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;gBACjF,CAAC;qBAAM,CAAC;oBAEN,IAAI,CAAC,mBAAmB,CAAC,eAAe,EAAE,GAAG,CAAC,CAAC;gBACjD,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,IAAI,CAAC,8BAA8B,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;YAC/D,CAAC;QACH,CAAC;QAGD,IAAI,CAAC,iBAAiB,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC;QAEhD,OAAO,eAAe,CAAC;IACzB,CAAC;IAKO,YAAY,CAAC,OAAe;QAClC,IAAI,CAAC;YACH,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAC7B,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAKO,sBAAsB,CAC5B,SAA8B,EAC9B,IAAS,EACT,IAAY;QAEZ,QAAQ,IAAI,EAAE,CAAC;YACb,KAAK,QAAQ;gBACX,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;oBAC9B,SAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC,CAAC;gBACpE,CAAC;gBACD,MAAM;YACR,KAAK,OAAO;gBACV,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;oBAC7B,SAAS,CAAC,mBAAmB,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC,CAAC;gBAClE,CAAC;gBACD,MAAM;YACR,KAAK,YAAY;gBACf,IAAI,IAAI,CAAC,wBAAwB,EAAE,CAAC;oBAClC,SAAS,CAAC,wBAAwB,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,wBAAwB,CAAC,CAAC;gBAC5E,CAAC;gBACD,MAAM;YACR,KAAK,OAAO;gBACV,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;oBAChB,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC;gBACxC,CAAC;gBACD,MAAM;YACR,KAAK,WAAW;gBACd,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;oBAC1B,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;gBACnE,CAAC;gBACD,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;oBAC5B,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,kBAAkB,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;gBACvE,CAAC;gBACD,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;oBACzB,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,eAAe,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;gBACjE,CAAC;gBACD,MAAM;QACV,CAAC;IACH,CAAC;IAKO,mBAAmB,CAAC,SAA8B,EAAE,GAAmB;QAC7E,MAAM,OAAO,GAAG,GAAG,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;QAC1C,MAAM,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC;QAG9B,IAAI,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;YAC7F,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,8DAA8D,CAAC,CAAC;YACrG,IAAI,UAAU,IAAI,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC;gBAChC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;YAC9C,CAAC;QACH,CAAC;QAGD,IAAI,QAAQ,CAAC,QAAQ,EAAE,CAAC;YACtB,MAAM,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAC;YAGnC,IAAI,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;gBAE9D,SAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC;oBAClC,SAAS,EAAE,QAAQ;oBACnB,OAAO,EAAE,qBAAqB;oBAC9B,SAAS,EAAE,YAAY;oBACvB,UAAU,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAE;oBACnD,GAAG,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE;oBACtC,aAAa,EAAE,IAAI;oBACnB,SAAS,EAAE,SAAS;oBACpB,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,KAAK,CAAC,GAAG,IAAI;oBACtD,kBAAkB,EAAE,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,GAAG;iBAC9C,CAAC,CAAC;YACL,CAAC;QACH,CAAC;IACH,CAAC;IAKO,iBAAiB,CAAC,SAA8B,EAAE,MAAW;QAEnE,IACE,SAAS,CAAC,oBAAoB,CAAC,MAAM,KAAK,CAAC;YAC3C,SAAS,CAAC,mBAAmB,CAAC,MAAM,KAAK,CAAC;YAC1C,SAAS,CAAC,wBAAwB,CAAC,MAAM,KAAK,CAAC,EAC/C,CAAC;YACD,OAAO,CAAC,GAAG,CAAC,mEAAmE,CAAC,CAAC;YAEjF,KAAK,MAAM,QAAQ,IAAI,MAAM,CAAC,SAAS,EAAE,CAAC;gBACxC,IAAI,MAAM,CAAC,IAAI,KAAK,QAAQ,IAAI,MAAM,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;oBAC1D,SAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC;wBAClC,SAAS,EAAE,QAAQ;wBACnB,OAAO,EAAE,qBAAqB;wBAC9B,SAAS,EAAE,YAAY;wBACvB,UAAU,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAE;wBACnD,GAAG,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE;wBACtC,aAAa,EAAE,IAAI;wBACnB,SAAS,EAAE,SAAS;wBACpB,YAAY,EAAE,IAAI;wBAClB,kBAAkB,EAAE,IAAI;qBACzB,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,SAAS,CAAC,IAAyB,EAAE,SAAiB,KAAK;QAC/D,MAAM,SAAS,GAAqB,EAAE,CAAC;QAGvC,IAAI,IAAI,CAAC,oBAAoB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACzC,SAAS,CAAC,IAAI,CACZ,+BAAc,CAAC,oBAAoB,CACjC,IAAI,CAAC,SAAS,CAAC,EAAE,oBAAoB,EAAE,IAAI,CAAC,oBAAoB,EAAE,CAAC,EACnE,QAAQ,EACR,EAAE,MAAM,EAAE,SAAS,EAAE,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,oBAAoB,CAAC,EAAE,CACnF,CACF,CAAC;QACJ,CAAC;QAGD,IAAI,IAAI,CAAC,mBAAmB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACxC,SAAS,CAAC,IAAI,CACZ,+BAAc,CAAC,oBAAoB,CACjC,IAAI,CAAC,SAAS,CAAC,EAAE,mBAAmB,EAAE,IAAI,CAAC,mBAAmB,EAAE,CAAC,EACjE,OAAO,EACP,EAAE,MAAM,EAAE,SAAS,EAAE,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,mBAAmB,CAAC,EAAE,CACjF,CACF,CAAC;QACJ,CAAC;QAGD,IAAI,IAAI,CAAC,wBAAwB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC7C,SAAS,CAAC,IAAI,CACZ,+BAAc,CAAC,oBAAoB,CACjC,IAAI,CAAC,SAAS,CAAC,EAAE,wBAAwB,EAAE,IAAI,CAAC,wBAAwB,EAAE,CAAC,EAC3E,YAAY,EACZ,EAAE,MAAM,EAAE,CACX,CACF,CAAC;QACJ,CAAC;QAGD,SAAS,CAAC,IAAI,CACZ,+BAAc,CAAC,oBAAoB,CACjC,IAAI,CAAC,SAAS,CAAC;YACb,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;YACvC,kBAAkB,EAAE,IAAI,CAAC,kBAAkB;YAC3C,eAAe,EAAE,IAAI,CAAC,eAAe;SACtC,CAAC,EACF,WAAW,EACX,EAAE,MAAM,EAAE,CACX,CACF,CAAC;QAGF,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC3B,SAAS,CAAC,IAAI,CACZ,+BAAc,CAAC,oBAAoB,CACjC,IAAI,CAAC,SAAS,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,EACvC,OAAO,EACP,EAAE,MAAM,EAAE,CACX,CACF,CAAC;QACJ,CAAC;QAED,MAAM,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;QAC9C,OAAO,CAAC,GAAG,CAAC,UAAU,SAAS,CAAC,MAAM,mBAAmB,MAAM,EAAE,CAAC,CAAC;IACrE,CAAC;IAEO,2BAA2B,CAAC,OAAc;QAChD,MAAM,SAAS,GAAG,IAAI,GAAG,EAAU,CAAC;QACpC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YACvB,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAChC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC;QACH,OAAO,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1C,CAAC;IAEO,0BAA0B,CAAC,MAAa;QAC9C,MAAM,SAAS,GAAG,IAAI,GAAG,EAAU,CAAC;QACpC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YACrB,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC;QACH,OAAO,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1C,CAAC;IAKD,KAAK,CAAC,QAAQ;QACZ,IAAI,CAAC;YACH,OAAO,MAAM,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC;QACxC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;YACjD,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;CACF;AAvWD,8BAuWC"}