"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RAGEngine = void 0;
const VectorDatabase_1 = require("./VectorDatabase");
const QueryProcessor_1 = require("./QueryProcessor");
class RAGEngine {
    constructor() {
        this.vectorDb = new VectorDatabase_1.VectorDatabase();
        this.queryProcessor = new QueryProcessor_1.QueryProcessor();
    }
    async initialize() {
        await this.vectorDb.initialize();
        console.log('RAG Engine initialized');
    }
    async processQuery(query) {
        try {
            console.log(`Processing query: "${query}"`);
            const intent = await this.queryProcessor.parseIntent(query);
            console.log('Parsed intent:', intent);
            const retrievedDocuments = await this.retrieveRelevantContext(query, intent);
            console.log(`Retrieved ${retrievedDocuments.length} relevant documents`);
            const synthesizedData = await this.synthesizeData(retrievedDocuments, intent);
            return {
                query,
                intent,
                retrievedDocuments,
                synthesizedData,
            };
        }
        catch (error) {
            console.error('Failed to process query:', error);
            throw error;
        }
    }
    async retrieveRelevantContext(query, intent) {
        const searchResults = [];
        const generalResults = await this.vectorDb.searchSimilar(query, 5);
        searchResults.push(...generalResults);
        if (intent.type !== 'general') {
            const typeFilter = { type: intent.type };
            const typeResults = await this.vectorDb.searchSimilar(query, 3, typeFilter);
            searchResults.push(...typeResults);
        }
        for (const location of intent.locations) {
            const locationResults = await this.vectorDb.searchSimilar(`${location} ${intent.type} trends`, 2, { location });
            searchResults.push(...locationResults);
        }
        const uniqueResults = this.deduplicateResults(searchResults);
        return uniqueResults.slice(0, 15);
    }
    deduplicateResults(results) {
        const seen = new Set();
        return results.filter(result => {
            if (seen.has(result.document.id)) {
                return false;
            }
            seen.add(result.document.id);
            return true;
        }).sort((a, b) => b.score - a.score);
    }
    async synthesizeData(documents, intent) {
        const synthesizedData = {
            flight_search_trends: [],
            hotel_search_trends: [],
            experience_search_trends: [],
            content_metadata: {},
            booking_conversion: {},
            api_performance: {
                flights: { avg_latency: 2.0, failures_last_24h: 0 },
                hotels: { avg_latency: 1.5, failures_last_24h: 0 },
                experiences: { avg_latency: 2.2, failures_last_24h: 0 },
            },
            events: [],
        };
        for (const result of documents) {
            const doc = result.document;
            try {
                const parsedContent = this.tryParseJSON(doc.content);
                if (parsedContent) {
                    this.mergeDataIntoSynthesis(synthesizedData, parsedContent, doc.metadata.type);
                }
                else {
                    this.extractDataFromText(synthesizedData, doc);
                }
            }
            catch (error) {
                console.warn(`Failed to process document ${doc.id}:`, error);
            }
        }
        this.ensureMinimumData(synthesizedData, intent);
        return synthesizedData;
    }
    tryParseJSON(content) {
        try {
            return JSON.parse(content);
        }
        catch {
            return null;
        }
    }
    mergeDataIntoSynthesis(synthesis, data, type) {
        switch (type) {
            case 'flight':
                if (data.flight_search_trends) {
                    synthesis.flight_search_trends.push(...data.flight_search_trends);
                }
                break;
            case 'hotel':
                if (data.hotel_search_trends) {
                    synthesis.hotel_search_trends.push(...data.hotel_search_trends);
                }
                break;
            case 'experience':
                if (data.experience_search_trends) {
                    synthesis.experience_search_trends.push(...data.experience_search_trends);
                }
                break;
            case 'event':
                if (data.events) {
                    synthesis.events.push(...data.events);
                }
                break;
            case 'analytics':
                if (data.content_metadata) {
                    Object.assign(synthesis.content_metadata, data.content_metadata);
                }
                if (data.booking_conversion) {
                    Object.assign(synthesis.booking_conversion, data.booking_conversion);
                }
                if (data.api_performance) {
                    Object.assign(synthesis.api_performance, data.api_performance);
                }
                break;
        }
    }
    extractDataFromText(synthesis, doc) {
        const content = doc.content.toLowerCase();
        const metadata = doc.metadata;
        if (content.includes('event') || content.includes('holiday') || content.includes('festival')) {
            const eventMatch = doc.content.match(/([A-Z][^.!?]*(?:event|holiday|festival|celebration)[^.!?]*)/i);
            if (eventMatch && eventMatch[1]) {
                synthesis.events.push(eventMatch[1].trim());
            }
        }
        if (metadata.location) {
            const location = metadata.location;
            if (content.includes('flight') || content.includes('airline')) {
                synthesis.flight_search_trends.push({
                    from_city: location,
                    to_city: 'Popular Destination',
                    trip_type: 'Round Trip',
                    start_date: new Date().toISOString().split('T')[0],
                    pax: { adult: 2, child: 0, infant: 0 },
                    is_refundable: true,
                    fare_type: 'Regular',
                    search_count: Math.floor(Math.random() * 10000) + 1000,
                    booking_conversion: Math.random() * 0.5 + 0.2,
                });
            }
        }
    }
    ensureMinimumData(synthesis, intent) {
        if (synthesis.flight_search_trends.length === 0 &&
            synthesis.hotel_search_trends.length === 0 &&
            synthesis.experience_search_trends.length === 0) {
            console.log('No specific data found, generating synthetic data based on intent');
            for (const location of intent.locations) {
                if (intent.type === 'flight' || intent.type === 'general') {
                    synthesis.flight_search_trends.push({
                        from_city: location,
                        to_city: 'Popular Destination',
                        trip_type: 'Round Trip',
                        start_date: new Date().toISOString().split('T')[0],
                        pax: { adult: 2, child: 0, infant: 0 },
                        is_refundable: true,
                        fare_type: 'Regular',
                        search_count: 5000,
                        booking_conversion: 0.35,
                    });
                }
            }
        }
    }
    async storeData(data, source = 'api') {
        const documents = [];
        if (data.flight_search_trends.length > 0) {
            documents.push(VectorDatabase_1.VectorDatabase.createTravelDocument(JSON.stringify({ flight_search_trends: data.flight_search_trends }), 'flight', { source, locations: this.extractLocationsFromFlights(data.flight_search_trends) }));
        }
        if (data.hotel_search_trends.length > 0) {
            documents.push(VectorDatabase_1.VectorDatabase.createTravelDocument(JSON.stringify({ hotel_search_trends: data.hotel_search_trends }), 'hotel', { source, locations: this.extractLocationsFromHotels(data.hotel_search_trends) }));
        }
        if (data.experience_search_trends.length > 0) {
            documents.push(VectorDatabase_1.VectorDatabase.createTravelDocument(JSON.stringify({ experience_search_trends: data.experience_search_trends }), 'experience', { source }));
        }
        documents.push(VectorDatabase_1.VectorDatabase.createTravelDocument(JSON.stringify({
            content_metadata: data.content_metadata,
            booking_conversion: data.booking_conversion,
            api_performance: data.api_performance,
        }), 'analytics', { source }));
        if (data.events.length > 0) {
            documents.push(VectorDatabase_1.VectorDatabase.createTravelDocument(JSON.stringify({ events: data.events }), 'event', { source }));
        }
        await this.vectorDb.storeDocuments(documents);
        console.log(`Stored ${documents.length} documents from ${source}`);
    }
    extractLocationsFromFlights(flights) {
        const locations = new Set();
        flights.forEach(flight => {
            locations.add(flight.from_city);
            locations.add(flight.to_city);
        });
        return Array.from(locations).join(', ');
    }
    extractLocationsFromHotels(hotels) {
        const locations = new Set();
        hotels.forEach(hotel => {
            locations.add(hotel.city);
        });
        return Array.from(locations).join(', ');
    }
    async getStats() {
        try {
            return await this.vectorDb.getStats();
        }
        catch (error) {
            console.error('Failed to get RAG stats:', error);
            return null;
        }
    }
}
exports.RAGEngine = RAGEngine;
//# sourceMappingURL=RAGEngine.js.map