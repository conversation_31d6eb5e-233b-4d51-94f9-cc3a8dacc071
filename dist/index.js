"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const app_1 = __importDefault(require("./app"));
const config_1 = require("./config");
const routes_1 = require("./api/routes");
const PORT = config_1.Config.PORT;
async function startServer() {
    try {
        console.log('🔧 Initializing Enhanced Travel Caching Strategist...');
        await (0, routes_1.initializeEnhancedStrategist)();
        console.log('✅ Enhanced strategist initialized successfully');
        const server = app_1.default.listen(PORT, () => {
            console.log(`🚀 Travel Caching Strategist API (RAG + MCP) is running on port ${PORT}`);
            console.log(`📊 Environment: ${config_1.Config.NODE_ENV}`);
            console.log(`🔗 Health check: http://localhost:${PORT}/api/v1/health`);
            console.log(`📖 API docs: http://localhost:${PORT}/`);
            console.log(`🤖 Natural Language Query: http://localhost:${PORT}/api/v1/query`);
            if (config_1.Config.isDevelopment()) {
                console.log(`🧪 Sample input: http://localhost:${PORT}/api/v1/sample-input`);
                console.log(`💬 Sample queries: http://localhost:${PORT}/api/v1/sample-queries`);
            }
        });
        setupGracefulShutdown(server);
    }
    catch (error) {
        console.error('❌ Failed to initialize enhanced strategist:', error);
        console.log('⚠️ Starting server without enhanced features...');
        const server = app_1.default.listen(PORT, () => {
            console.log(`🚀 Travel Caching Strategist API (Basic Mode) is running on port ${PORT}`);
            console.log(`📊 Environment: ${config_1.Config.NODE_ENV}`);
            console.log(`🔗 Health check: http://localhost:${PORT}/api/v1/health`);
            console.log(`📖 API docs: http://localhost:${PORT}/`);
        });
        setupGracefulShutdown(server);
    }
}
function setupGracefulShutdown(server) {
    process.on('SIGTERM', () => {
        console.log('SIGTERM received, shutting down gracefully');
        server.close(() => {
            console.log('Process terminated');
            process.exit(0);
        });
    });
    process.on('SIGINT', () => {
        console.log('SIGINT received, shutting down gracefully');
        server.close(() => {
            console.log('Process terminated');
            process.exit(0);
        });
    });
}
process.on('uncaughtException', (error) => {
    console.error('Uncaught Exception:', error);
    process.exit(1);
});
process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason);
    process.exit(1);
});
startServer();
exports.default = app_1.default;
//# sourceMappingURL=index.js.map