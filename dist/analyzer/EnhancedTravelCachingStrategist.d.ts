import { RAGContext } from '../rag/RAGEngine';
import { TravelAnalysisInput, CachingRecommendation } from '../types';
export interface EnhancedAnalysisResult {
    query: string;
    ragContext: RAGContext;
    mcpData: TravelAnalysisInput;
    recommendations: CachingRecommendation[];
    metadata: {
        processingTime: number;
        dataSourcesUsed: string[];
        confidenceScore: number;
        retrievedDocuments: number;
    };
}
export declare class EnhancedTravelCachingStrategist {
    private ragEngine;
    private mcpRouter;
    private baseAnalyzer;
    constructor();
    initialize(): Promise<void>;
    analyzeQuery(query: string): Promise<EnhancedAnalysisResult>;
    private fetchMCPData;
    private mergeDataSources;
    private deduplicateFlightTrends;
    private deduplicateHotelTrends;
    private deduplicateExperienceTrends;
    private getDataSourcesUsed;
    private calculateConfidenceScore;
    private calculateMCPCompleteness;
    getHealthStatus(): Promise<Record<string, boolean>>;
    refreshCaches(): Promise<void>;
}
//# sourceMappingURL=EnhancedTravelCachingStrategist.d.ts.map