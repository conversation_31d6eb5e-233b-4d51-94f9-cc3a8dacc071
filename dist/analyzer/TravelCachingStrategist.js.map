{"version": 3, "file": "TravelCachingStrategist.js", "sourceRoot": "", "sources": ["../../src/analyzer/TravelCachingStrategist.ts"], "names": [], "mappings": ";;;AAUA,oCAAoD;AACpD,sCAAmC;AAEnC,MAAa,uBAAuB;IAGlC,YAAY,OAAyB;QACnC,IAAI,CAAC,OAAO,GAAG,OAAO,IAAI,eAAM,CAAC,kBAAkB,EAAE,CAAC;IACxD,CAAC;IAKM,KAAK,CAAC,OAAO,CAAC,KAA0B;QAE7C,MAAM,oBAAoB,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;QAGxD,MAAM,eAAe,GAA4B,EAAE,CAAC;QAGpD,MAAM,qBAAqB,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,oBAAoB,CAAC,CAAC;QAC/E,eAAe,CAAC,IAAI,CAAC,GAAG,qBAAqB,CAAC,CAAC;QAG/C,MAAM,oBAAoB,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,oBAAoB,CAAC,CAAC;QAC7E,eAAe,CAAC,IAAI,CAAC,GAAG,oBAAoB,CAAC,CAAC;QAG9C,MAAM,yBAAyB,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,oBAAoB,CAAC,CAAC;QACvF,eAAe,CAAC,IAAI,CAAC,GAAG,yBAAyB,CAAC,CAAC;QAGnD,OAAO,IAAI,CAAC,yBAAyB,CAAC,eAAe,CAAC,CAAC;IACzD,CAAC;IAKO,cAAc,CAAC,KAA0B;QAC/C,MAAM,SAAS,GAAG,qBAAa,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;QAGxD,MAAM,YAAY,GAAG,KAAK,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;QACpG,MAAM,WAAW,GAAG,KAAK,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;QAClG,MAAM,gBAAgB,GAAG,KAAK,CAAC,wBAAwB,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;QAE5G,IAAI,aAA6D,CAAC;QAClE,IAAI,YAAY,GAAG,WAAW,IAAI,YAAY,GAAG,gBAAgB,EAAE,CAAC;YAClE,aAAa,GAAG,SAAS,CAAC;QAC5B,CAAC;aAAM,IAAI,WAAW,GAAG,gBAAgB,EAAE,CAAC;YAC1C,aAAa,GAAG,QAAQ,CAAC;QAC3B,CAAC;aAAM,IAAI,gBAAgB,GAAG,CAAC,EAAE,CAAC;YAChC,aAAa,GAAG,aAAa,CAAC;QAChC,CAAC;aAAM,CAAC;YACN,aAAa,GAAG,OAAO,CAAC;QAC1B,CAAC;QAGD,MAAM,QAAQ,GAAa;YACzB,GAAG,KAAK,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC;YACpD,GAAG,KAAK,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAS,CAAC;YAC3E,GAAG,KAAK,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,YAAY,CAAC;YACrD,GAAG,KAAK,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,aAAa,CAAC;YACtD,GAAG,KAAK,CAAC,wBAAwB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,YAAY,CAAC;YAC1D,GAAG,KAAK,CAAC,wBAAwB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,aAAa,CAAC;SAC5D,CAAC;QAEF,OAAO;YACL,cAAc,EAAE,aAAa;YAC7B,SAAS;YACT,UAAU,EAAE,iBAAS,CAAC,YAAY,CAAC,QAAQ,CAAC;YAC5C,MAAM,EAAE,KAAK,CAAC,MAAM;SACrB,CAAC;IACJ,CAAC;IAKO,cAAc,CAAC,KAA0B,EAAE,cAAoC;QACrF,MAAM,eAAe,GAA4B,EAAE,CAAC;QAEpD,KAAK,MAAM,KAAK,IAAI,KAAK,CAAC,oBAAoB,EAAE,CAAC;YAC/C,MAAM,QAAQ,GAAG,GAAG,KAAK,CAAC,SAAS,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;YACvD,MAAM,WAAW,GAAG,GAAG,KAAK,CAAC,SAAS,IAAI,KAAK,CAAC,OAAO,UAAU,CAAC;YAClE,MAAM,QAAQ,GAAG,KAAK,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;YAErD,IAAI,CAAC,QAAQ;gBAAE,SAAS;YAGxB,MAAM,eAAe,GAAG,IAAI,CAAC,yBAAyB,CAAC,KAAK,CAAC,CAAC;YAG9D,MAAM,aAAa,GAAG,qBAAa,CAAC,sBAAsB,CACxD,QAAQ,CAAC,gBAAgB,EACzB,eAAe,EACf,KAAK,CAAC,eAAe,CAAC,OAAO,CAAC,WAAW,CAC1C,CAAC;YAGF,MAAM,GAAG,GAAG,qBAAa,CAAC,YAAY,CACpC,IAAI,CAAC,OAAO,CAAC,iBAAiB,GAAG,GAAG,EACpC,aAAa,EACb,KAAK,CAAC,YAAY,EAClB,KAAK,CAAC,kBAAkB,EACxB,IAAI,CAAC,OAAO,CAAC,aAAa,EAC1B,IAAI,CAAC,OAAO,CAAC,aAAa,CAC3B,CAAC;YAGF,MAAM,SAAS,GAAG,qBAAa,CAAC,iBAAiB,CAC/C,cAAc,EACd,KAAK,CAAC,YAAY,EAClB,KAAK,CAAC,kBAAkB,EACxB,QAAQ,CAAC,gBAAgB,EACzB,cAAc,CAAC,MAAM,EACrB,KAAK,CAAC,eAAe,CAAC,OAAO,CAAC,WAAW,CAC1C,CAAC;YAEF,eAAe,CAAC,IAAI,CAAC;gBACnB,WAAW,EAAE,cAAc;gBAC3B,iBAAiB,EAAE,QAAQ;gBAC3B,SAAS;gBACT,mBAAmB,EAAE,GAAG;gBACxB,cAAc,EAAE,aAAa;aAC9B,CAAC,CAAC;QACL,CAAC;QAED,OAAO,eAAe,CAAC;IACzB,CAAC;IAKO,aAAa,CAAC,KAA0B,EAAE,cAAoC;QACpF,MAAM,eAAe,GAA4B,EAAE,CAAC;QAEpD,KAAK,MAAM,KAAK,IAAI,KAAK,CAAC,mBAAmB,EAAE,CAAC;YAE9C,MAAM,YAAY,GAAG;gBACnB,GAAG,KAAK,CAAC,IAAI,SAAS;gBACtB,GAAG,KAAK,CAAC,IAAI,gBAAgB;gBAC7B,GAAG,KAAK,CAAC,IAAI,WAAW;aACzB,CAAC;YAEF,IAAI,QAAQ,CAAC;YACb,IAAI,WAAW,GAAG,EAAE,CAAC;YACrB,KAAK,MAAM,GAAG,IAAI,YAAY,EAAE,CAAC;gBAC/B,IAAI,KAAK,CAAC,gBAAgB,CAAC,GAAG,CAAC,EAAE,CAAC;oBAChC,QAAQ,GAAG,KAAK,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;oBACvC,WAAW,GAAG,GAAG,CAAC;oBAClB,MAAM;gBACR,CAAC;YACH,CAAC;YAED,IAAI,CAAC,QAAQ;gBAAE,SAAS;YAGxB,MAAM,eAAe,GAAG,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC;YAG7D,MAAM,aAAa,GAAG,qBAAa,CAAC,sBAAsB,CACxD,QAAQ,CAAC,gBAAgB,EACzB,eAAe,EACf,KAAK,CAAC,eAAe,CAAC,MAAM,CAAC,WAAW,CACzC,CAAC;YAGF,MAAM,GAAG,GAAG,qBAAa,CAAC,YAAY,CACpC,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAC9B,aAAa,EACb,KAAK,CAAC,YAAY,EAClB,KAAK,CAAC,kBAAkB,EACxB,IAAI,CAAC,OAAO,CAAC,aAAa,EAC1B,IAAI,CAAC,OAAO,CAAC,aAAa,CAC3B,CAAC;YAGF,MAAM,SAAS,GAAG,qBAAa,CAAC,iBAAiB,CAC/C,eAAe,EACf,KAAK,CAAC,YAAY,EAClB,KAAK,CAAC,kBAAkB,EACxB,QAAQ,CAAC,gBAAgB,EACzB,cAAc,CAAC,MAAM,CACtB,CAAC;YAEF,eAAe,CAAC,IAAI,CAAC;gBACnB,WAAW,EAAE,eAAe;gBAC5B,iBAAiB,EAAE,WAAW;gBAC9B,SAAS;gBACT,mBAAmB,EAAE,GAAG;gBACxB,cAAc,EAAE,aAAa;aAC9B,CAAC,CAAC;QACL,CAAC;QAED,OAAO,eAAe,CAAC;IACzB,CAAC;IAKO,kBAAkB,CAAC,KAA0B,EAAE,cAAoC;QACzF,MAAM,eAAe,GAA4B,EAAE,CAAC;QAEpD,KAAK,MAAM,KAAK,IAAI,KAAK,CAAC,wBAAwB,EAAE,CAAC;YACnD,MAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,OAAO,IAAI,SAAS,CAAC;YAC1E,MAAM,UAAU,GAAG,GAAG,QAAQ,cAAc,CAAC;YAG7C,MAAM,cAAc,GAAG,KAAK,CAAC,kBAAkB,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC;YAGlE,MAAM,eAAe,GAAG,IAAI,CAAC,6BAA6B,CAAC,KAAK,CAAC,CAAC;YAGlE,MAAM,aAAa,GAAG,qBAAa,CAAC,sBAAsB,CACxD,WAAW,EACX,eAAe,EACf,KAAK,CAAC,eAAe,CAAC,WAAW,CAAC,WAAW,CAC9C,CAAC;YAGF,MAAM,GAAG,GAAG,qBAAa,CAAC,YAAY,CACpC,IAAI,CAAC,OAAO,CAAC,iBAAiB,GAAG,GAAG,EACpC,aAAa,EACb,KAAK,CAAC,YAAY,EAClB,cAAc,EACd,IAAI,CAAC,OAAO,CAAC,aAAa,EAC1B,IAAI,CAAC,OAAO,CAAC,aAAa,CAC3B,CAAC;YAGF,MAAM,SAAS,GAAG,qBAAa,CAAC,iBAAiB,CAC/C,kBAAkB,EAClB,KAAK,CAAC,YAAY,EAClB,cAAc,EACd,WAAW,EACX,cAAc,CAAC,MAAM,EACrB,KAAK,CAAC,eAAe,CAAC,WAAW,CAAC,WAAW,CAC9C,CAAC;YAEF,eAAe,CAAC,IAAI,CAAC;gBACnB,WAAW,EAAE,kBAAkB;gBAC/B,iBAAiB,EAAE,UAAU;gBAC7B,SAAS;gBACT,mBAAmB,EAAE,GAAG;gBACxB,cAAc,EAAE,aAAa;aAC9B,CAAC,CAAC;QACL,CAAC;QAED,OAAO,eAAe,CAAC;IACzB,CAAC;IAKO,yBAAyB,CAAC,eAAwC;QACxE,OAAO,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YAEnC,MAAM,OAAO,GAAG,IAAI,CAAC,gCAAgC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;YACnE,MAAM,OAAO,GAAG,IAAI,CAAC,gCAAgC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;YAGnE,IAAI,OAAO,KAAK,OAAO,EAAE,CAAC;gBACxB,OAAO,OAAO,GAAG,OAAO,CAAC;YAC3B,CAAC;YAED,MAAM,SAAS,GAAG,EAAE,MAAM,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC;YACvD,OAAO,SAAS,CAAC,CAAC,CAAC,cAAc,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;IACL,CAAC;IAKO,yBAAyB,CAAC,KAAwB;QACxD,IAAI,UAAU,GAAG,GAAG,CAAC;QAGrB,IAAI,KAAK,CAAC,SAAS,KAAK,SAAS;YAAE,UAAU,IAAI,GAAG,CAAC;QAGrD,IAAI,CAAC,KAAK,CAAC,aAAa;YAAE,UAAU,IAAI,GAAG,CAAC;QAG5C,IAAI,KAAK,CAAC,SAAS,KAAK,SAAS;YAAE,UAAU,IAAI,GAAG,CAAC;QAErD,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC;IACnC,CAAC;IAKO,wBAAwB,CAAC,KAAuB;QACtD,IAAI,UAAU,GAAG,GAAG,CAAC;QAGrB,MAAM,YAAY,GAAG,iBAAS,CAAC,SAAS,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,OAAO,EAAE;YACnD,iBAAS,CAAC,SAAS,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,OAAO,EAAE,CAAC;QACtE,MAAM,IAAI,GAAG,YAAY,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;QAElD,IAAI,IAAI,IAAI,CAAC;YAAE,UAAU,IAAI,GAAG,CAAC;QACjC,IAAI,IAAI,IAAI,CAAC;YAAE,UAAU,IAAI,GAAG,CAAC;QAGjC,IAAI,KAAK,CAAC,KAAK,GAAG,CAAC;YAAE,UAAU,IAAI,GAAG,CAAC;QAEvC,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC,CAAC;IAClD,CAAC;IAKO,6BAA6B,CAAC,KAA4B;QAChE,IAAI,UAAU,GAAG,IAAI,CAAC;QAGtB,MAAM,QAAQ,GAAG,iBAAS,CAAC,SAAS,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,OAAO,EAAE;YACnD,iBAAS,CAAC,SAAS,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,OAAO,EAAE,CAAC;QAClE,MAAM,IAAI,GAAG,QAAQ,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;QAE9C,IAAI,IAAI,KAAK,CAAC;YAAE,UAAU,IAAI,GAAG,CAAC;QAClC,IAAI,IAAI,IAAI,CAAC;YAAE,UAAU,IAAI,IAAI,CAAC;QAElC,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC,CAAC;IAClD,CAAC;IAKO,gCAAgC,CAAC,SAAiB;QACxD,MAAM,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAC;QACtD,IAAI,KAAK,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;YACtB,OAAO,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;QAClD,CAAC;QACD,OAAO,CAAC,CAAC;IACX,CAAC;CACF;AA9UD,0DA8UC"}