import { TravelAnalysisInput, CachingRecommendation, AnalysisContext } from '../types';
export declare class TravelCachingStrategist {
    private context;
    constructor(context?: AnalysisContext);
    analyze(input: TravelAnalysisInput): Promise<CachingRecommendation[]>;
    private classifyDomain;
    private analyzeFlights;
    private analyzeHotels;
    private analyzeExperiences;
    private prioritizeRecommendations;
    private calculateFlightVolatility;
    private calculateHotelVolatility;
    private calculateExperienceVolatility;
    private extractSearchVolumeFromReasoning;
}
//# sourceMappingURL=TravelCachingStrategist.d.ts.map