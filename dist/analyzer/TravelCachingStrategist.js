"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TravelCachingStrategist = void 0;
const utils_1 = require("../utils");
const config_1 = require("../config");
class TravelCachingStrategist {
    constructor(context) {
        this.context = context || config_1.Config.getAnalysisContext();
    }
    async analyze(input) {
        const domainClassification = this.classifyDomain(input);
        const recommendations = [];
        const flightRecommendations = this.analyzeFlights(input, domainClassification);
        recommendations.push(...flightRecommendations);
        const hotelRecommendations = this.analyzeHotels(input, domainClassification);
        recommendations.push(...hotelRecommendations);
        const experienceRecommendations = this.analyzeExperiences(input, domainClassification);
        recommendations.push(...experienceRecommendations);
        return this.prioritizeRecommendations(recommendations);
    }
    classifyDomain(input) {
        const locations = utils_1.AnalysisUtils.extractLocations(input);
        const flightVolume = input.flight_search_trends.reduce((sum, trend) => sum + trend.search_count, 0);
        const hotelVolume = input.hotel_search_trends.reduce((sum, trend) => sum + trend.search_count, 0);
        const experienceVolume = input.experience_search_trends.reduce((sum, trend) => sum + trend.search_count, 0);
        let primaryDomain;
        if (flightVolume > hotelVolume && flightVolume > experienceVolume) {
            primaryDomain = 'flights';
        }
        else if (hotelVolume > experienceVolume) {
            primaryDomain = 'hotels';
        }
        else if (experienceVolume > 0) {
            primaryDomain = 'experiences';
        }
        else {
            primaryDomain = 'mixed';
        }
        const allDates = [
            ...input.flight_search_trends.map(t => t.start_date),
            ...input.flight_search_trends.filter(t => t.end_date).map(t => t.end_date),
            ...input.hotel_search_trends.map(t => t.checkin_date),
            ...input.hotel_search_trends.map(t => t.checkout_date),
            ...input.experience_search_trends.map(t => t.checkin_date),
            ...input.experience_search_trends.map(t => t.checkout_date),
        ];
        return {
            primary_domain: primaryDomain,
            locations,
            date_range: utils_1.DateUtils.getDateRange(allDates),
            events: input.events,
        };
    }
    analyzeFlights(input, classification) {
        const recommendations = [];
        for (const trend of input.flight_search_trends) {
            const routeKey = `${trend.from_city}–${trend.to_city}`;
            const metadataKey = `${trend.from_city}–${trend.to_city} flights`;
            const metadata = input.content_metadata[metadataKey];
            if (!metadata)
                continue;
            const volatilityScore = this.calculateFlightVolatility(trend);
            const stalenessRisk = utils_1.AnalysisUtils.calculateStalenessRisk(metadata.inventory_status, volatilityScore, input.api_performance.flights.avg_latency);
            const ttl = utils_1.AnalysisUtils.calculateTTL(this.context.default_ttl_hours * 0.5, stalenessRisk, trend.search_count, trend.booking_conversion, this.context.min_ttl_hours, this.context.max_ttl_hours);
            const reasoning = utils_1.AnalysisUtils.generateReasoning('flight_route', trend.search_count, trend.booking_conversion, metadata.inventory_status, classification.events, input.api_performance.flights.avg_latency);
            recommendations.push({
                entity_type: 'flight_route',
                entity_id_or_name: routeKey,
                reasoning,
                suggested_ttl_hours: ttl,
                staleness_risk: stalenessRisk,
            });
        }
        return recommendations;
    }
    analyzeHotels(input, classification) {
        const recommendations = [];
        for (const trend of input.hotel_search_trends) {
            const possibleKeys = [
                `${trend.city} hotels`,
                `${trend.city} 3-star hotels`,
                `${trend.city} cottages`,
            ];
            let metadata;
            let metadataKey = '';
            for (const key of possibleKeys) {
                if (input.content_metadata[key]) {
                    metadata = input.content_metadata[key];
                    metadataKey = key;
                    break;
                }
            }
            if (!metadata)
                continue;
            const volatilityScore = this.calculateHotelVolatility(trend);
            const stalenessRisk = utils_1.AnalysisUtils.calculateStalenessRisk(metadata.inventory_status, volatilityScore, input.api_performance.hotels.avg_latency);
            const ttl = utils_1.AnalysisUtils.calculateTTL(this.context.default_ttl_hours, stalenessRisk, trend.search_count, trend.booking_conversion, this.context.min_ttl_hours, this.context.max_ttl_hours);
            const reasoning = utils_1.AnalysisUtils.generateReasoning('hotel_cluster', trend.search_count, trend.booking_conversion, metadata.inventory_status, classification.events);
            recommendations.push({
                entity_type: 'hotel_cluster',
                entity_id_or_name: metadataKey,
                reasoning,
                suggested_ttl_hours: ttl,
                staleness_risk: stalenessRisk,
            });
        }
        return recommendations;
    }
    analyzeExperiences(input, classification) {
        const recommendations = [];
        for (const trend of input.experience_search_trends) {
            const location = trend.city || trend.region || trend.country || 'Unknown';
            const entityName = `${location} experiences`;
            const conversionRate = input.booking_conversion[location] || 0.25;
            const volatilityScore = this.calculateExperienceVolatility(trend);
            const stalenessRisk = utils_1.AnalysisUtils.calculateStalenessRisk('Available', volatilityScore, input.api_performance.experiences.avg_latency);
            const ttl = utils_1.AnalysisUtils.calculateTTL(this.context.default_ttl_hours * 1.5, stalenessRisk, trend.search_count, conversionRate, this.context.min_ttl_hours, this.context.max_ttl_hours);
            const reasoning = utils_1.AnalysisUtils.generateReasoning('experience_group', trend.search_count, conversionRate, 'Available', classification.events, input.api_performance.experiences.avg_latency);
            recommendations.push({
                entity_type: 'experience_group',
                entity_id_or_name: entityName,
                reasoning,
                suggested_ttl_hours: ttl,
                staleness_risk: stalenessRisk,
            });
        }
        return recommendations;
    }
    prioritizeRecommendations(recommendations) {
        return recommendations.sort((a, b) => {
            const aVolume = this.extractSearchVolumeFromReasoning(a.reasoning);
            const bVolume = this.extractSearchVolumeFromReasoning(b.reasoning);
            if (aVolume !== bVolume) {
                return bVolume - aVolume;
            }
            const riskOrder = { 'High': 3, 'Medium': 2, 'Low': 1 };
            return riskOrder[b.staleness_risk] - riskOrder[a.staleness_risk];
        });
    }
    calculateFlightVolatility(trend) {
        let volatility = 0.3;
        if (trend.trip_type === 'One Way')
            volatility += 0.1;
        if (!trend.is_refundable)
            volatility += 0.2;
        if (trend.fare_type === 'Premium')
            volatility += 0.1;
        return Math.min(1.0, volatility);
    }
    calculateHotelVolatility(trend) {
        let volatility = 0.2;
        const stayDuration = utils_1.DateUtils.parseDate(trend.checkout_date).getTime() -
            utils_1.DateUtils.parseDate(trend.checkin_date).getTime();
        const days = stayDuration / (1000 * 60 * 60 * 24);
        if (days <= 2)
            volatility += 0.2;
        if (days >= 7)
            volatility -= 0.1;
        if (trend.rooms > 1)
            volatility += 0.1;
        return Math.min(1.0, Math.max(0.1, volatility));
    }
    calculateExperienceVolatility(trend) {
        let volatility = 0.15;
        const duration = utils_1.DateUtils.parseDate(trend.checkout_date).getTime() -
            utils_1.DateUtils.parseDate(trend.checkin_date).getTime();
        const days = duration / (1000 * 60 * 60 * 24);
        if (days === 0)
            volatility += 0.1;
        if (days >= 5)
            volatility -= 0.05;
        return Math.min(1.0, Math.max(0.1, volatility));
    }
    extractSearchVolumeFromReasoning(reasoning) {
        const match = reasoning.match(/(\d{1,3}(?:,\d{3})*)/);
        if (match && match[1]) {
            return parseInt(match[1].replace(/,/g, ''), 10);
        }
        return 0;
    }
}
exports.TravelCachingStrategist = TravelCachingStrategist;
//# sourceMappingURL=TravelCachingStrategist.js.map