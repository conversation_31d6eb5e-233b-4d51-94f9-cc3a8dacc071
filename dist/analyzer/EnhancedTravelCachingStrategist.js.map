{"version": 3, "file": "EnhancedTravelCachingStrategist.js", "sourceRoot": "", "sources": ["../../src/analyzer/EnhancedTravelCachingStrategist.ts"], "names": [], "mappings": ";;;AAAA,uEAAoE;AACpE,gDAAyD;AACzD,gDAA6C;AAgB7C,MAAa,+BAA+B;IAK1C;QACE,IAAI,CAAC,SAAS,GAAG,IAAI,qBAAS,EAAE,CAAC;QACjC,IAAI,CAAC,SAAS,GAAG,IAAI,qBAAS,EAAE,CAAC;QACjC,IAAI,CAAC,YAAY,GAAG,IAAI,iDAAuB,EAAE,CAAC;IACpD,CAAC;IAKD,KAAK,CAAC,UAAU;QACd,OAAO,CAAC,GAAG,CAAC,oDAAoD,CAAC,CAAC;QAElE,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,CAAC;YAClC,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;QAC1C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;YAC3D,MAAM,KAAK,CAAC;QACd,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,8CAA8C,CAAC,CAAC;IAC9D,CAAC;IAKD,KAAK,CAAC,YAAY,CAAC,KAAa;QAC9B,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,OAAO,CAAC,GAAG,CAAC,wBAAwB,KAAK,GAAG,CAAC,CAAC;QAE9C,IAAI,CAAC;YAEH,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;YACtD,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;YAG5D,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;YACtD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;YAGpD,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;YAC9C,MAAM,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC;YAG9E,OAAO,CAAC,GAAG,CAAC,0CAA0C,CAAC,CAAC;YACxD,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;YAGpE,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;YACtD,MAAM,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,UAAU,EAAE,mBAAmB,CAAC,CAAC;YAEhE,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAE9C,MAAM,MAAM,GAA2B;gBACrC,KAAK;gBACL,UAAU;gBACV,OAAO;gBACP,eAAe;gBACf,QAAQ,EAAE;oBACR,cAAc;oBACd,eAAe,EAAE,IAAI,CAAC,kBAAkB,CAAC,UAAU,EAAE,OAAO,CAAC;oBAC7D,eAAe,EAAE,IAAI,CAAC,wBAAwB,CAAC,UAAU,EAAE,OAAO,CAAC;oBACnE,kBAAkB,EAAE,UAAU,CAAC,kBAAkB,CAAC,MAAM;iBACzD;aACF,CAAC;YAEF,OAAO,CAAC,GAAG,CAAC,2BAA2B,cAAc,IAAI,CAAC,CAAC;YAC3D,OAAO,CAAC,GAAG,CAAC,gBAAgB,eAAe,CAAC,MAAM,kBAAkB,CAAC,CAAC;YAEtE,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;YACpD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,YAAY,CAAC,UAAsB;QAC/C,MAAM,EAAE,MAAM,EAAE,GAAG,UAAU,CAAC;QAE9B,IAAI,CAAC;YAEH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,sBAAsB,CACzD,MAAM,CAAC,SAAS,EAChB,MAAM,CAAC,SAAS,CACjB,CAAC;YAEF,OAAO,CAAC,GAAG,CAAC,2BAA2B,MAAM,CAAC,SAAS,CAAC,MAAM,YAAY,CAAC,CAAC;YAC5E,OAAO,OAAO,CAAC;QACjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CAAC,gDAAgD,EAAE,KAAK,CAAC,CAAC;YAGtE,OAAO;gBACL,oBAAoB,EAAE,EAAE;gBACxB,mBAAmB,EAAE,EAAE;gBACvB,wBAAwB,EAAE,EAAE;gBAC5B,gBAAgB,EAAE,EAAE;gBACpB,kBAAkB,EAAE,EAAE;gBACtB,eAAe,EAAE;oBACf,OAAO,EAAE,EAAE,WAAW,EAAE,GAAG,EAAE,iBAAiB,EAAE,CAAC,EAAE;oBACnD,MAAM,EAAE,EAAE,WAAW,EAAE,GAAG,EAAE,iBAAiB,EAAE,CAAC,EAAE;oBAClD,WAAW,EAAE,EAAE,WAAW,EAAE,GAAG,EAAE,iBAAiB,EAAE,CAAC,EAAE;iBACxD;gBACD,MAAM,EAAE,EAAE;aACX,CAAC;QACJ,CAAC;IACH,CAAC;IAKO,gBAAgB,CACtB,OAA4B,EAC5B,OAA4B;QAE5B,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;QAEtD,MAAM,MAAM,GAAwB;YAClC,oBAAoB,EAAE;gBACpB,GAAG,OAAO,CAAC,oBAAoB;gBAC/B,GAAG,OAAO,CAAC,oBAAoB;aAChC;YACD,mBAAmB,EAAE;gBACnB,GAAG,OAAO,CAAC,mBAAmB;gBAC9B,GAAG,OAAO,CAAC,mBAAmB;aAC/B;YACD,wBAAwB,EAAE;gBACxB,GAAG,OAAO,CAAC,wBAAwB;gBACnC,GAAG,OAAO,CAAC,wBAAwB;aACpC;YACD,gBAAgB,EAAE;gBAChB,GAAG,OAAO,CAAC,gBAAgB;gBAC3B,GAAG,OAAO,CAAC,gBAAgB;aAC5B;YACD,kBAAkB,EAAE;gBAClB,GAAG,OAAO,CAAC,kBAAkB;gBAC7B,GAAG,OAAO,CAAC,kBAAkB;aAC9B;YACD,eAAe,EAAE,OAAO,CAAC,eAAe;YACxC,MAAM,EAAE;gBACN,GAAG,IAAI,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;aACnD;SACF,CAAC;QAGF,MAAM,CAAC,oBAAoB,GAAG,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC;QACxF,MAAM,CAAC,mBAAmB,GAAG,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC;QACrF,MAAM,CAAC,wBAAwB,GAAG,IAAI,CAAC,2BAA2B,CAAC,MAAM,CAAC,wBAAwB,CAAC,CAAC;QAEpG,OAAO,CAAC,GAAG,CAAC,mBAAmB,MAAM,CAAC,oBAAoB,CAAC,MAAM,mBAAmB,MAAM,CAAC,mBAAmB,CAAC,MAAM,kBAAkB,MAAM,CAAC,wBAAwB,CAAC,MAAM,oBAAoB,CAAC,CAAC;QAEnM,OAAO,MAAM,CAAC;IAChB,CAAC;IAKO,uBAAuB,CAAC,MAAa;QAC3C,MAAM,IAAI,GAAG,IAAI,GAAG,EAAU,CAAC;QAC/B,OAAO,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;YAC3B,MAAM,GAAG,GAAG,GAAG,KAAK,CAAC,SAAS,IAAI,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,UAAU,EAAE,CAAC;YACtE,IAAI,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;gBAClB,OAAO,KAAK,CAAC;YACf,CAAC;YACD,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YACd,OAAO,IAAI,CAAC;QACd,CAAC,CAAC,CAAC;IACL,CAAC;IAKO,sBAAsB,CAAC,MAAa;QAC1C,MAAM,IAAI,GAAG,IAAI,GAAG,EAAU,CAAC;QAC/B,OAAO,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;YAC3B,MAAM,GAAG,GAAG,GAAG,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,YAAY,EAAE,CAAC;YAClD,IAAI,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;gBAClB,OAAO,KAAK,CAAC;YACf,CAAC;YACD,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YACd,OAAO,IAAI,CAAC;QACd,CAAC,CAAC,CAAC;IACL,CAAC;IAKO,2BAA2B,CAAC,MAAa;QAC/C,MAAM,IAAI,GAAG,IAAI,GAAG,EAAU,CAAC;QAC/B,OAAO,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;YAC3B,MAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,OAAO,IAAI,SAAS,CAAC;YAC1E,MAAM,GAAG,GAAG,GAAG,QAAQ,IAAI,KAAK,CAAC,YAAY,EAAE,CAAC;YAChD,IAAI,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;gBAClB,OAAO,KAAK,CAAC;YACf,CAAC;YACD,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YACd,OAAO,IAAI,CAAC;QACd,CAAC,CAAC,CAAC;IACL,CAAC;IAKO,kBAAkB,CAAC,UAAsB,EAAE,OAA4B;QAC7E,MAAM,OAAO,GAAG,IAAI,GAAG,EAAU,CAAC;QAGlC,UAAU,CAAC,kBAAkB,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YAC1C,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;QAGH,IAAI,OAAO,CAAC,oBAAoB,CAAC,MAAM,GAAG,CAAC;YAAE,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;QACvE,IAAI,OAAO,CAAC,mBAAmB,CAAC,MAAM,GAAG,CAAC;YAAE,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QACrE,IAAI,OAAO,CAAC,wBAAwB,CAAC,MAAM,GAAG,CAAC;YAAE,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;QAC/E,IAAI,OAAO,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC;YAAE,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;QAEzD,OAAO,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAC7B,CAAC;IAKO,wBAAwB,CAAC,UAAsB,EAAE,OAA4B;QACnF,IAAI,KAAK,GAAG,CAAC,CAAC;QAGd,KAAK,IAAI,UAAU,CAAC,MAAM,CAAC,UAAU,GAAG,GAAG,CAAC;QAG5C,KAAK,IAAI,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,kBAAkB,CAAC,MAAM,GAAG,EAAE,EAAE,GAAG,CAAC,CAAC;QAGlE,MAAM,eAAe,GAAG,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,CAAC;QAC/D,KAAK,IAAI,eAAe,GAAG,GAAG,CAAC;QAG/B,KAAK,IAAI,GAAG,CAAC;QAEb,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;IAC9B,CAAC;IAKO,wBAAwB,CAAC,OAA4B;QAC3D,IAAI,YAAY,GAAG,CAAC,CAAC;QAErB,IAAI,OAAO,CAAC,oBAAoB,CAAC,MAAM,GAAG,CAAC;YAAE,YAAY,IAAI,IAAI,CAAC;QAClE,IAAI,OAAO,CAAC,mBAAmB,CAAC,MAAM,GAAG,CAAC;YAAE,YAAY,IAAI,IAAI,CAAC;QACjE,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,MAAM,GAAG,CAAC;YAAE,YAAY,IAAI,IAAI,CAAC;QAC3E,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC,MAAM,GAAG,CAAC;YAAE,YAAY,IAAI,IAAI,CAAC;QAE7E,OAAO,YAAY,CAAC;IACtB,CAAC;IAKD,KAAK,CAAC,eAAe;QACnB,MAAM,MAAM,GAA4B,EAAE,CAAC;QAE3C,IAAI,CAAC;YAEH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;YACjD,MAAM,CAAC,UAAU,GAAG,QAAQ,KAAK,IAAI,CAAC;QACxC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,UAAU,GAAG,KAAK,CAAC;QAC5B,CAAC;QAED,IAAI,CAAC;YAEH,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,eAAe,EAAE,CAAC;YACzD,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;QACnC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,UAAU,GAAG,KAAK,CAAC;QAC5B,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAKD,KAAK,CAAC,aAAa;QACjB,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;QAE3C,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,CAAC;YACpC,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC;QACxC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;QAC1D,CAAC;IACH,CAAC;CACF;AA9SD,0EA8SC"}