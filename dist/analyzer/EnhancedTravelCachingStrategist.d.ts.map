{"version": 3, "file": "EnhancedTravelCachingStrategist.d.ts", "sourceRoot": "", "sources": ["../../src/analyzer/EnhancedTravelCachingStrategist.ts"], "names": [], "mappings": "AACA,OAAO,EAAa,UAAU,EAAE,MAAM,kBAAkB,CAAC;AAEzD,OAAO,EAAE,mBAAmB,EAAE,qBAAqB,EAAE,MAAM,UAAU,CAAC;AAEtE,MAAM,WAAW,sBAAsB;IACrC,KAAK,EAAE,MAAM,CAAC;IACd,UAAU,EAAE,UAAU,CAAC;IACvB,OAAO,EAAE,mBAAmB,CAAC;IAC7B,eAAe,EAAE,qBAAqB,EAAE,CAAC;IACzC,QAAQ,EAAE;QACR,cAAc,EAAE,MAAM,CAAC;QACvB,eAAe,EAAE,MAAM,EAAE,CAAC;QAC1B,eAAe,EAAE,MAAM,CAAC;QACxB,kBAAkB,EAAE,MAAM,CAAC;KAC5B,CAAC;CACH;AAED,qBAAa,+BAA+B;IAC1C,OAAO,CAAC,SAAS,CAAY;IAC7B,OAAO,CAAC,SAAS,CAAY;IAC7B,OAAO,CAAC,YAAY,CAA0B;;IAWxC,UAAU,IAAI,OAAO,CAAC,IAAI,CAAC;IAiB3B,YAAY,CAAC,KAAK,EAAE,MAAM,GAAG,OAAO,CAAC,sBAAsB,CAAC;YAqDpD,YAAY;IAmC1B,OAAO,CAAC,gBAAgB;IA8CxB,OAAO,CAAC,uBAAuB;IAe/B,OAAO,CAAC,sBAAsB;IAe9B,OAAO,CAAC,2BAA2B;IAgBnC,OAAO,CAAC,kBAAkB;IAoB1B,OAAO,CAAC,wBAAwB;IAsBhC,OAAO,CAAC,wBAAwB;IAc1B,eAAe,IAAI,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IAyBnD,aAAa,IAAI,OAAO,CAAC,IAAI,CAAC;CAUrC"}