"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EnhancedTravelCachingStrategist = void 0;
const TravelCachingStrategist_1 = require("./TravelCachingStrategist");
const RAGEngine_1 = require("../rag/RAGEngine");
const MCPRouter_1 = require("../mcp/MCPRouter");
class EnhancedTravelCachingStrategist {
    constructor() {
        this.ragEngine = new RAGEngine_1.RAGEngine();
        this.mcpRouter = new MCPRouter_1.MCPRouter();
        this.baseAnalyzer = new TravelCachingStrategist_1.TravelCachingStrategist();
    }
    async initialize() {
        console.log('Initializing Enhanced Travel Caching Strategist...');
        try {
            await this.ragEngine.initialize();
            console.log('✅ RAG Engine initialized');
        }
        catch (error) {
            console.error('❌ Failed to initialize RAG Engine:', error);
            throw error;
        }
        console.log('🚀 Enhanced Travel Caching Strategist ready!');
    }
    async analyzeQuery(query) {
        const startTime = Date.now();
        console.log(`🔍 Analyzing query: "${query}"`);
        try {
            console.log('📚 Processing query with RAG engine...');
            const ragContext = await this.ragEngine.processQuery(query);
            console.log('🌐 Fetching real-time data with MCP...');
            const mcpData = await this.fetchMCPData(ragContext);
            console.log('🔄 Merging RAG and MCP data...');
            const mergedData = this.mergeDataSources(ragContext.synthesizedData, mcpData);
            console.log('🎯 Generating caching recommendations...');
            const recommendations = await this.baseAnalyzer.analyze(mergedData);
            console.log('💾 Storing new data in RAG database...');
            await this.ragEngine.storeData(mergedData, 'enhanced_analysis');
            const processingTime = Date.now() - startTime;
            const result = {
                query,
                ragContext,
                mcpData,
                recommendations,
                metadata: {
                    processingTime,
                    dataSourcesUsed: this.getDataSourcesUsed(ragContext, mcpData),
                    confidenceScore: this.calculateConfidenceScore(ragContext, mcpData),
                    retrievedDocuments: ragContext.retrievedDocuments.length,
                },
            };
            console.log(`✅ Analysis completed in ${processingTime}ms`);
            console.log(`📊 Generated ${recommendations.length} recommendations`);
            return result;
        }
        catch (error) {
            console.error('❌ Enhanced analysis failed:', error);
            throw error;
        }
    }
    async fetchMCPData(ragContext) {
        const { intent } = ragContext;
        try {
            const mcpData = await this.mcpRouter.fetchComprehensiveData(intent.locations, intent.dateRange);
            console.log(`📡 MCP fetched data for ${intent.locations.length} locations`);
            return mcpData;
        }
        catch (error) {
            console.warn('⚠️ MCP data fetch failed, using fallback data:', error);
            return {
                flight_search_trends: [],
                hotel_search_trends: [],
                experience_search_trends: [],
                content_metadata: {},
                booking_conversion: {},
                api_performance: {
                    flights: { avg_latency: 2.0, failures_last_24h: 0 },
                    hotels: { avg_latency: 1.5, failures_last_24h: 0 },
                    experiences: { avg_latency: 2.2, failures_last_24h: 0 },
                },
                events: [],
            };
        }
    }
    mergeDataSources(ragData, mcpData) {
        console.log('🔀 Merging RAG and MCP data sources...');
        const merged = {
            flight_search_trends: [
                ...ragData.flight_search_trends,
                ...mcpData.flight_search_trends,
            ],
            hotel_search_trends: [
                ...ragData.hotel_search_trends,
                ...mcpData.hotel_search_trends,
            ],
            experience_search_trends: [
                ...ragData.experience_search_trends,
                ...mcpData.experience_search_trends,
            ],
            content_metadata: {
                ...ragData.content_metadata,
                ...mcpData.content_metadata,
            },
            booking_conversion: {
                ...ragData.booking_conversion,
                ...mcpData.booking_conversion,
            },
            api_performance: mcpData.api_performance,
            events: [
                ...new Set([...ragData.events, ...mcpData.events]),
            ],
        };
        merged.flight_search_trends = this.deduplicateFlightTrends(merged.flight_search_trends);
        merged.hotel_search_trends = this.deduplicateHotelTrends(merged.hotel_search_trends);
        merged.experience_search_trends = this.deduplicateExperienceTrends(merged.experience_search_trends);
        console.log(`📈 Merged data: ${merged.flight_search_trends.length} flight trends, ${merged.hotel_search_trends.length} hotel trends, ${merged.experience_search_trends.length} experience trends`);
        return merged;
    }
    deduplicateFlightTrends(trends) {
        const seen = new Set();
        return trends.filter(trend => {
            const key = `${trend.from_city}-${trend.to_city}-${trend.start_date}`;
            if (seen.has(key)) {
                return false;
            }
            seen.add(key);
            return true;
        });
    }
    deduplicateHotelTrends(trends) {
        const seen = new Set();
        return trends.filter(trend => {
            const key = `${trend.city}-${trend.checkin_date}`;
            if (seen.has(key)) {
                return false;
            }
            seen.add(key);
            return true;
        });
    }
    deduplicateExperienceTrends(trends) {
        const seen = new Set();
        return trends.filter(trend => {
            const location = trend.city || trend.region || trend.country || 'unknown';
            const key = `${location}-${trend.checkin_date}`;
            if (seen.has(key)) {
                return false;
            }
            seen.add(key);
            return true;
        });
    }
    getDataSourcesUsed(ragContext, mcpData) {
        const sources = new Set();
        ragContext.retrievedDocuments.forEach(doc => {
            sources.add(doc.document.metadata.source);
        });
        if (mcpData.flight_search_trends.length > 0)
            sources.add('flight_api');
        if (mcpData.hotel_search_trends.length > 0)
            sources.add('hotel_api');
        if (mcpData.experience_search_trends.length > 0)
            sources.add('experience_api');
        if (mcpData.events.length > 0)
            sources.add('events_api');
        return Array.from(sources);
    }
    calculateConfidenceScore(ragContext, mcpData) {
        let score = 0;
        score += ragContext.intent.confidence * 0.3;
        score += Math.min(ragContext.retrievedDocuments.length / 10, 0.2);
        const mcpCompleteness = this.calculateMCPCompleteness(mcpData);
        score += mcpCompleteness * 0.3;
        score += 0.2;
        return Math.min(1.0, score);
    }
    calculateMCPCompleteness(mcpData) {
        let completeness = 0;
        if (mcpData.flight_search_trends.length > 0)
            completeness += 0.25;
        if (mcpData.hotel_search_trends.length > 0)
            completeness += 0.25;
        if (Object.keys(mcpData.content_metadata).length > 0)
            completeness += 0.25;
        if (Object.keys(mcpData.booking_conversion).length > 0)
            completeness += 0.25;
        return completeness;
    }
    async getHealthStatus() {
        const health = {};
        try {
            const ragStats = await this.ragEngine.getStats();
            health.rag_engine = ragStats !== null;
        }
        catch (error) {
            health.rag_engine = false;
        }
        try {
            const mcpHealth = await this.mcpRouter.getHealthStatus();
            Object.assign(health, mcpHealth);
        }
        catch (error) {
            health.mcp_router = false;
        }
        return health;
    }
    async refreshCaches() {
        console.log('🔄 Refreshing all caches...');
        try {
            await this.mcpRouter.refreshCache();
            console.log('✅ MCP caches refreshed');
        }
        catch (error) {
            console.error('❌ Failed to refresh MCP caches:', error);
        }
    }
}
exports.EnhancedTravelCachingStrategist = EnhancedTravelCachingStrategist;
//# sourceMappingURL=EnhancedTravelCachingStrategist.js.map