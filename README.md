# Travel Caching Strategist (RAG + MCP Enhanced)

An AI-powered travel content pre-caching optimization service that uses **RAG (Retrieval-Augmented Generation)** and **MCP (Model Context Protocol)** to intelligently analyze travel data and generate optimal caching recommendations.

## 🚀 Enhanced Features

### Core AI Capabilities
- **Natural Language Query Processing**: Ask questions like "What should I cache for flights from Mumbai to Delhi?"
- **RAG (Retrieval-Augmented Generation)**: Intelligent context retrieval from historical travel data
- **MCP (Model Context Protocol)**: Real-time data integration from multiple travel APIs
- **Vector Database Storage**: Semantic search and intelligent data retrieval using Pinecone
- **Multi-domain Analysis**: Supports flights, hotels, and experiences

### Intelligence & Optimization
- **Intelligent TTL Calculation**: Dynamic cache time-to-live based on volatility and demand
- **Risk Assessment**: Evaluates staleness risk (Low/Medium/High) for each recommendation
- **Event-aware Analysis**: Considers holidays and special events in caching decisions
- **Performance Optimization**: Factors in API latency and failure rates
- **Confidence Scoring**: Provides confidence levels for each recommendation

### Integration & APIs
- **RESTful API**: Easy integration with existing travel platforms
- **Real-time Data Sources**: Connects to flight, hotel, events, and weather APIs
- **Legacy Support**: Maintains backward compatibility with JSON input format

## 🏗️ Architecture Overview

```
┌─────────────────┐    ┌──────────────┐    ┌─────────────────┐
│   User Query    │───▶│  RAG Engine  │───▶│ Caching AI      │
│ "Cache for NYC" │    │ + MCP Tools  │    │ Strategist      │
└─────────────────┘    └──────────────┘    └─────────────────┘
                              │
                              ▼
                    ┌──────────────────┐
                    │ External Sources │
                    │ • Flight APIs    │
                    │ • Hotel Systems  │
                    │ • Event Feeds    │
                    │ • Weather Data   │
                    └──────────────────┘
```

## 🚀 Quick Start

### Prerequisites

- Node.js 18+
- npm or yarn
- **API Keys** (for full functionality):
  - OpenAI API key (for embeddings and NLP)
  - Pinecone API key (for vector database)
  - Travel API keys (optional, uses synthetic data as fallback)

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd precaching-analyzer

# Install backend dependencies
npm install

# Install frontend dependencies
cd frontend
npm install
cd ..

# Copy and configure environment
cp .env.example .env
# Edit .env with your API keys (see Configuration section)

# Build both applications
npm run build
cd frontend && npm run build && cd ..
```

### Running the Application

#### Option 1: Development Mode (Recommended)

```bash
# Terminal 1: Start backend (RAG + MCP API)
npm run dev

# Terminal 2: Start frontend (Next.js Dashboard)
cd frontend
npm run dev
```

#### Option 2: Production Mode

```bash
# Start backend
npm start

# Start frontend (in another terminal)
cd frontend
npm start
```

### Access the Application

- **Frontend Dashboard**: http://localhost:3001
- **Backend API**: http://localhost:3000
- **API Documentation**: http://localhost:3000/
- **Health Check**: http://localhost:3000/api/v1/health

### Development

```bash
# Run in development mode with hot reload
npm run dev

# Run tests
npm test

# Run linting
npm run lint
```

## 🎯 Features Overview

### Frontend Dashboard
- **🤖 Natural Language Interface**: Chat-like query input with sample suggestions
- **📊 Interactive Analytics**: Real-time charts and visualizations
- **🎯 Smart Recommendations**: Prioritized caching recommendations with detailed reasoning
- **🏥 Health Monitoring**: Live system health status with component monitoring
- **📱 Responsive Design**: Mobile-friendly interface built with Tailwind CSS
- **⚡ Real-time Updates**: Live data refresh and error handling

### Backend API (RAG + MCP Enhanced)
- **🧠 Natural Language Processing**: OpenAI-powered query understanding
- **🔍 Vector Search**: Pinecone-based semantic search and retrieval
- **🌐 Real-time Data Integration**: MCP connectors for external APIs
- **📈 Intelligent Analysis**: Context-aware caching recommendations
- **🔄 Adaptive Learning**: Stores and learns from query patterns

## 🎮 Usage

### Frontend Dashboard

1. **Open the Dashboard**: Navigate to http://localhost:3001
2. **Ask Natural Language Questions**:
   - "What should I cache for flights from Mumbai to Delhi?"
   - "Cache recommendations for hotels in Goa during festivals"
   - "Analyze caching needs for Thailand experiences"
3. **View Results**: Interactive charts, recommendations, and system health
4. **Explore Analytics**: TTL distribution, risk assessment, and confidence scores

### API Usage

#### Natural Language Query (NEW)

```bash
curl -X POST http://localhost:3000/api/v1/query \
  -H "Content-Type: application/json" \
  -d '{"query": "What should I cache for flights from Mumbai to Delhi?"}'
```

#### Health Check

```bash
curl http://localhost:3000/api/v1/health
```

#### Legacy JSON Analysis

```bash
curl -X POST http://localhost:3000/api/v1/analyze \
  -H "Content-Type: application/json" \
  -d @sampleInput.json
```

#### Get Sample Queries

```bash
curl http://localhost:3000/api/v1/sample-queries
```

## 🛠️ Technology Stack

### Backend (TypeScript/Node.js)
- **Framework**: Express.js with TypeScript
- **AI/ML**: OpenAI API for embeddings and NLP
- **Vector Database**: Pinecone for semantic search
- **HTTP Client**: Axios for external API calls
- **Validation**: Zod for type-safe data validation
- **Testing**: Jest with TypeScript support

### Frontend (Next.js/React)
- **Framework**: Next.js 14 with App Router
- **Language**: TypeScript for type safety
- **Styling**: Tailwind CSS for responsive design
- **State Management**: TanStack Query (React Query)
- **Charts**: Recharts for data visualization
- **Icons**: Lucide React for consistent iconography
- **Animations**: Framer Motion for smooth interactions

### External Integrations
- **Vector Database**: Pinecone (managed vector search)
- **AI/NLP**: OpenAI (embeddings, chat completions)
- **Travel APIs**: Amadeus, Booking.com, Eventbrite (via MCP)
- **Weather**: OpenWeatherMap API
- **Analytics**: Custom analytics connectors

## Input Data Format

The API expects a JSON object with the following structure:

```typescript
{
  "flight_search_trends": [
    {
      "from_city": "Mumbai",
      "to_city": "Delhi", 
      "trip_type": "One Way",
      "start_date": "2025-08-14",
      "pax": { "adult": 1, "child": 1, "infant": 0 },
      "is_refundable": true,
      "fare_type": "Regular",
      "search_count": 13200,
      "booking_conversion": 0.42
    }
  ],
  "hotel_search_trends": [...],
  "experience_search_trends": [...],
  "content_metadata": {...},
  "booking_conversion": {...},
  "api_performance": {...},
  "events": [...]
}
```

## Output Format

The API returns caching recommendations in this format:

```json
[
  {
    "entity_type": "flight_route",
    "entity_id_or_name": "Mumbai–Delhi",
    "reasoning": "High search volume (13,200) with excellent conversion rate (42%). Independence Day weekend driving demand.",
    "suggested_ttl_hours": 6,
    "staleness_risk": "Medium"
  }
]
```

## Configuration

Environment variables can be configured in `.env`:

```bash
# Server Configuration
PORT=3000
NODE_ENV=development

# Caching Configuration  
DEFAULT_TTL_HOURS=12
MAX_TTL_HOURS=72
MIN_TTL_HOURS=1

# Performance Thresholds
HIGH_SEARCH_VOLUME_THRESHOLD=10000
HIGH_CONVERSION_THRESHOLD=0.4
HIGH_LATENCY_THRESHOLD=2.0
```

## Architecture

```
src/
├── analyzer/           # Core analysis logic
│   └── TravelCachingStrategist.ts
├── api/               # REST API routes
│   └── routes.ts
├── config/            # Configuration management
│   └── index.ts
├── types/             # TypeScript type definitions
│   └── index.ts
├── utils/             # Utility functions
│   └── index.ts
├── examples/          # Usage examples
│   └── basic-usage.ts
├── __tests__/         # Test files
│   └── TravelCachingStrategist.test.ts
├── app.ts            # Express app setup
└── index.ts          # Server entry point
```

## Algorithm Overview

The caching strategist follows a three-step process:

### Step 1: Domain Understanding
- Classifies queries by travel domain (flights/hotels/experiences)
- Identifies locations and date ranges
- Detects relevant events and holidays

### Step 2: Context Analysis  
- Prioritizes based on search volume and conversion rates
- Considers content metadata (pricing, ratings, inventory)
- Factors in system performance (API latency, failures)
- Calculates volatility scores for different content types

### Step 3: Recommendation Generation
- Generates entity-specific TTL recommendations
- Assigns staleness risk levels
- Provides detailed reasoning for each recommendation
- Prioritizes results by business impact

## Testing

Run the test suite:

```bash
npm test
```

Run the basic usage example:

```bash
npm run dev
# In another terminal:
npx ts-node src/examples/basic-usage.ts
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run the test suite
6. Submit a pull request

## License

MIT License - see LICENSE file for details.
