import {
  TravelAnalysisInput,
  CachingRecommendation,
  DomainClassification,
  AnalysisContext,
  StalenessRisk,
  FlightSearchTrend,
  HotelSearchTrend,
  ExperienceSearchTrend,
} from '../types';
import { AnalysisUtils, DateUtils } from '../utils';
import { Config } from '../config';

export class TravelCachingStrategist {
  private context: AnalysisContext;

  constructor(context?: AnalysisContext) {
    this.context = context || Config.getAnalysisContext();
  }

  /**
   * Main analysis method that processes input and generates caching recommendations
   */
  public async analyze(input: TravelAnalysisInput): Promise<CachingRecommendation[]> {
    // Step 1: Understand the Domain
    const domainClassification = this.classifyDomain(input);
    
    // Step 2: Analyze the Context
    const recommendations: CachingRecommendation[] = [];
    
    // Analyze flights
    const flightRecommendations = this.analyzeFlights(input, domainClassification);
    recommendations.push(...flightRecommendations);
    
    // Analyze hotels
    const hotelRecommendations = this.analyzeHotels(input, domainClassification);
    recommendations.push(...hotelRecommendations);
    
    // Analyze experiences
    const experienceRecommendations = this.analyzeExperiences(input, domainClassification);
    recommendations.push(...experienceRecommendations);
    
    // Step 3: Sort by priority (high search volume and conversion first)
    return this.prioritizeRecommendations(recommendations);
  }

  /**
   * Step 1: Classify domain and extract key information
   */
  private classifyDomain(input: TravelAnalysisInput): DomainClassification {
    const locations = AnalysisUtils.extractLocations(input);
    
    // Determine primary domain based on search volumes
    const flightVolume = input.flight_search_trends.reduce((sum, trend) => sum + trend.search_count, 0);
    const hotelVolume = input.hotel_search_trends.reduce((sum, trend) => sum + trend.search_count, 0);
    const experienceVolume = input.experience_search_trends.reduce((sum, trend) => sum + trend.search_count, 0);
    
    let primaryDomain: 'flights' | 'hotels' | 'experiences' | 'mixed';
    if (flightVolume > hotelVolume && flightVolume > experienceVolume) {
      primaryDomain = 'flights';
    } else if (hotelVolume > experienceVolume) {
      primaryDomain = 'hotels';
    } else if (experienceVolume > 0) {
      primaryDomain = 'experiences';
    } else {
      primaryDomain = 'mixed';
    }

    // Extract date range
    const allDates: string[] = [
      ...input.flight_search_trends.map(t => t.start_date),
      ...input.flight_search_trends.filter(t => t.end_date).map(t => t.end_date!),
      ...input.hotel_search_trends.map(t => t.checkin_date),
      ...input.hotel_search_trends.map(t => t.checkout_date),
      ...input.experience_search_trends.map(t => t.checkin_date),
      ...input.experience_search_trends.map(t => t.checkout_date),
    ];

    return {
      primary_domain: primaryDomain,
      locations,
      date_range: DateUtils.getDateRange(allDates),
      events: input.events,
    };
  }

  /**
   * Analyze flight search trends and generate caching recommendations
   */
  private analyzeFlights(input: TravelAnalysisInput, classification: DomainClassification): CachingRecommendation[] {
    const recommendations: CachingRecommendation[] = [];

    for (const trend of input.flight_search_trends) {
      const routeKey = `${trend.from_city}–${trend.to_city}`;
      const metadataKey = `${trend.from_city}–${trend.to_city} flights`;
      const metadata = input.content_metadata[metadataKey];

      if (!metadata) continue;

      // Calculate volatility based on trip type and refundability
      const volatilityScore = this.calculateFlightVolatility(trend);

      // Calculate staleness risk
      const stalenessRisk = AnalysisUtils.calculateStalenessRisk(
        metadata.inventory_status,
        volatilityScore,
        input.api_performance.flights.avg_latency
      );

      // Calculate TTL
      const ttl = AnalysisUtils.calculateTTL(
        this.context.default_ttl_hours * 0.5, // Flights typically need shorter TTL
        stalenessRisk,
        trend.search_count,
        trend.booking_conversion,
        this.context.min_ttl_hours,
        this.context.max_ttl_hours
      );

      // Generate reasoning
      const reasoning = AnalysisUtils.generateReasoning(
        'flight_route',
        trend.search_count,
        trend.booking_conversion,
        metadata.inventory_status,
        classification.events,
        input.api_performance.flights.avg_latency
      );

      recommendations.push({
        entity_type: 'flight_route',
        entity_id_or_name: routeKey,
        reasoning,
        suggested_ttl_hours: ttl,
        staleness_risk: stalenessRisk,
      });
    }

    return recommendations;
  }

  /**
   * Analyze hotel search trends and generate caching recommendations
   */
  private analyzeHotels(input: TravelAnalysisInput, classification: DomainClassification): CachingRecommendation[] {
    const recommendations: CachingRecommendation[] = [];

    for (const trend of input.hotel_search_trends) {
      // Find matching metadata
      const possibleKeys = [
        `${trend.city} hotels`,
        `${trend.city} 3-star hotels`,
        `${trend.city} cottages`,
      ];

      let metadata;
      let metadataKey = '';
      for (const key of possibleKeys) {
        if (input.content_metadata[key]) {
          metadata = input.content_metadata[key];
          metadataKey = key;
          break;
        }
      }

      if (!metadata) continue;

      // Calculate volatility based on stay duration and room count
      const volatilityScore = this.calculateHotelVolatility(trend);

      // Calculate staleness risk
      const stalenessRisk = AnalysisUtils.calculateStalenessRisk(
        metadata.inventory_status,
        volatilityScore,
        input.api_performance.hotels.avg_latency
      );

      // Calculate TTL (hotels can have longer TTL than flights)
      const ttl = AnalysisUtils.calculateTTL(
        this.context.default_ttl_hours,
        stalenessRisk,
        trend.search_count,
        trend.booking_conversion,
        this.context.min_ttl_hours,
        this.context.max_ttl_hours
      );

      // Generate reasoning
      const reasoning = AnalysisUtils.generateReasoning(
        'hotel_cluster',
        trend.search_count,
        trend.booking_conversion,
        metadata.inventory_status,
        classification.events
      );

      recommendations.push({
        entity_type: 'hotel_cluster',
        entity_id_or_name: metadataKey,
        reasoning,
        suggested_ttl_hours: ttl,
        staleness_risk: stalenessRisk,
      });
    }

    return recommendations;
  }

  /**
   * Analyze experience search trends and generate caching recommendations
   */
  private analyzeExperiences(input: TravelAnalysisInput, classification: DomainClassification): CachingRecommendation[] {
    const recommendations: CachingRecommendation[] = [];

    for (const trend of input.experience_search_trends) {
      const location = trend.city || trend.region || trend.country || 'Unknown';
      const entityName = `${location} experiences`;

      // Get conversion rate from booking_conversion data
      const conversionRate = input.booking_conversion[location] || 0.25; // Default fallback

      // Calculate volatility (experiences typically have lower volatility)
      const volatilityScore = this.calculateExperienceVolatility(trend);

      // Calculate staleness risk (assume available inventory for experiences)
      const stalenessRisk = AnalysisUtils.calculateStalenessRisk(
        'Available',
        volatilityScore,
        input.api_performance.experiences.avg_latency
      );

      // Calculate TTL (experiences can have longer TTL)
      const ttl = AnalysisUtils.calculateTTL(
        this.context.default_ttl_hours * 1.5, // Experiences can be cached longer
        stalenessRisk,
        trend.search_count,
        conversionRate,
        this.context.min_ttl_hours,
        this.context.max_ttl_hours
      );

      // Generate reasoning
      const reasoning = AnalysisUtils.generateReasoning(
        'experience_group',
        trend.search_count,
        conversionRate,
        'Available',
        classification.events,
        input.api_performance.experiences.avg_latency
      );

      recommendations.push({
        entity_type: 'experience_group',
        entity_id_or_name: entityName,
        reasoning,
        suggested_ttl_hours: ttl,
        staleness_risk: stalenessRisk,
      });
    }

    return recommendations;
  }

  /**
   * Prioritize recommendations based on search volume and conversion rates
   */
  private prioritizeRecommendations(recommendations: CachingRecommendation[]): CachingRecommendation[] {
    return recommendations.sort((a, b) => {
      // Extract search volume from reasoning (simple heuristic)
      const aVolume = this.extractSearchVolumeFromReasoning(a.reasoning);
      const bVolume = this.extractSearchVolumeFromReasoning(b.reasoning);

      // Sort by search volume descending, then by staleness risk (High first)
      if (aVolume !== bVolume) {
        return bVolume - aVolume;
      }

      const riskOrder = { 'High': 3, 'Medium': 2, 'Low': 1 };
      return riskOrder[b.staleness_risk] - riskOrder[a.staleness_risk];
    });
  }

  /**
   * Calculate flight volatility score based on trip characteristics
   */
  private calculateFlightVolatility(trend: FlightSearchTrend): number {
    let volatility = 0.3; // Base volatility for flights

    // Trip type impact
    if (trend.trip_type === 'One Way') volatility += 0.1;

    // Refundability impact
    if (!trend.is_refundable) volatility += 0.2;

    // Fare type impact
    if (trend.fare_type === 'Premium') volatility += 0.1;

    return Math.min(1.0, volatility);
  }

  /**
   * Calculate hotel volatility score based on stay characteristics
   */
  private calculateHotelVolatility(trend: HotelSearchTrend): number {
    let volatility = 0.2; // Base volatility for hotels

    // Stay duration impact
    const stayDuration = DateUtils.parseDate(trend.checkout_date).getTime() -
                        DateUtils.parseDate(trend.checkin_date).getTime();
    const days = stayDuration / (1000 * 60 * 60 * 24);

    if (days <= 2) volatility += 0.2; // Short stays more volatile
    if (days >= 7) volatility -= 0.1; // Long stays less volatile

    // Multiple rooms impact
    if (trend.rooms > 1) volatility += 0.1;

    return Math.min(1.0, Math.max(0.1, volatility));
  }

  /**
   * Calculate experience volatility score
   */
  private calculateExperienceVolatility(trend: ExperienceSearchTrend): number {
    let volatility = 0.15; // Base volatility for experiences (typically lower)

    // Duration impact
    const duration = DateUtils.parseDate(trend.checkout_date).getTime() -
                    DateUtils.parseDate(trend.checkin_date).getTime();
    const days = duration / (1000 * 60 * 60 * 24);

    if (days === 0) volatility += 0.1; // Same-day experiences
    if (days >= 5) volatility -= 0.05; // Multi-day experiences

    return Math.min(1.0, Math.max(0.1, volatility));
  }

  /**
   * Extract search volume from reasoning text (simple regex)
   */
  private extractSearchVolumeFromReasoning(reasoning: string): number {
    const match = reasoning.match(/(\d{1,3}(?:,\d{3})*)/);
    if (match && match[1]) {
      return parseInt(match[1].replace(/,/g, ''), 10);
    }
    return 0;
  }
}
