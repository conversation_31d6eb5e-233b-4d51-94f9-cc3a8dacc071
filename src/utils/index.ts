import { StalenessRisk, InventoryStatus } from '../types';

export class DateUtils {
  /**
   * Parse date string and return Date object
   */
  public static parseDate(dateString: string): Date {
    return new Date(dateString);
  }

  /**
   * Check if date is within a certain number of days from now
   */
  public static isWithinDays(dateString: string, days: number): boolean {
    const targetDate = this.parseDate(dateString);
    const now = new Date();
    const diffTime = targetDate.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays <= days && diffDays >= 0;
  }

  /**
   * Get date range from array of date strings
   */
  public static getDateRange(dates: string[]): { start: string; end: string } {
    if (dates.length === 0) {
      const now = new Date().toISOString().split('T')[0]!;
      return { start: now, end: now };
    }

    const sortedDates = dates.sort();
    return {
      start: sortedDates[0]!,
      end: sortedDates[sortedDates.length - 1]!,
    };
  }
}

export class AnalysisUtils {
  /**
   * Calculate staleness risk based on various factors
   */
  public static calculateStalenessRisk(
    inventoryStatus: InventoryStatus,
    volatilityScore: number,
    apiLatency: number
  ): StalenessRisk {
    let riskScore = 0;

    // Inventory status impact
    switch (inventoryStatus) {
      case 'Limited':
        riskScore += 3;
        break;
      case 'Unavailable':
        riskScore += 5;
        break;
      case 'Available':
        riskScore += 1;
        break;
    }

    // Volatility impact (0-1 scale)
    riskScore += volatilityScore * 3;

    // API latency impact
    if (apiLatency > 2.5) riskScore += 2;
    else if (apiLatency > 2.0) riskScore += 1;

    // Convert to risk categories
    if (riskScore <= 2) return 'Low';
    if (riskScore <= 4) return 'Medium';
    return 'High';
  }

  /**
   * Calculate TTL based on risk, demand, and conversion
   */
  public static calculateTTL(
    baseHours: number,
    stalenessRisk: StalenessRisk,
    searchVolume: number,
    conversionRate: number,
    minHours: number,
    maxHours: number
  ): number {
    let ttl = baseHours;

    // Adjust based on staleness risk
    switch (stalenessRisk) {
      case 'High':
        ttl *= 0.3;
        break;
      case 'Medium':
        ttl *= 0.7;
        break;
      case 'Low':
        ttl *= 1.5;
        break;
    }

    // Adjust based on demand and conversion
    const demandMultiplier = searchVolume > 10000 ? 0.8 : 1.2;
    const conversionMultiplier = conversionRate > 0.4 ? 0.9 : 1.1;
    
    ttl *= demandMultiplier * conversionMultiplier;

    // Ensure within bounds
    return Math.max(minHours, Math.min(maxHours, Math.round(ttl)));
  }

  /**
   * Extract locations from search trends
   */
  public static extractLocations(data: any): string[] {
    const locations = new Set<string>();

    // From flight trends
    if (data.flight_search_trends) {
      data.flight_search_trends.forEach((trend: any) => {
        locations.add(trend.from_city);
        locations.add(trend.to_city);
      });
    }

    // From hotel trends
    if (data.hotel_search_trends) {
      data.hotel_search_trends.forEach((trend: any) => {
        locations.add(trend.city);
      });
    }

    // From experience trends
    if (data.experience_search_trends) {
      data.experience_search_trends.forEach((trend: any) => {
        if (trend.city) locations.add(trend.city);
        if (trend.region) locations.add(trend.region);
        if (trend.country) locations.add(trend.country);
      });
    }

    return Array.from(locations);
  }

  /**
   * Generate reasoning text for caching recommendation
   */
  public static generateReasoning(
    entityType: string,
    searchVolume: number,
    conversionRate: number,
    inventoryStatus: InventoryStatus,
    events: string[],
    apiLatency?: number
  ): string {
    const reasons: string[] = [];

    // Search volume analysis
    if (searchVolume > 10000) {
      reasons.push(`High search volume (${searchVolume.toLocaleString()})`);
    } else if (searchVolume > 5000) {
      reasons.push(`Moderate search volume (${searchVolume.toLocaleString()})`);
    }

    // Conversion rate analysis
    if (conversionRate > 0.4) {
      reasons.push(`excellent conversion rate (${(conversionRate * 100).toFixed(0)}%)`);
    } else if (conversionRate > 0.3) {
      reasons.push(`solid conversion rate (${(conversionRate * 100).toFixed(0)}%)`);
    } else if (conversionRate > 0.2) {
      reasons.push(`decent conversion rate (${(conversionRate * 100).toFixed(0)}%)`);
    }

    // Inventory status
    if (inventoryStatus === 'Limited') {
      reasons.push('limited inventory requires frequent updates');
    } else if (inventoryStatus === 'Available') {
      reasons.push('good inventory availability');
    }

    // Events impact
    if (events.length > 0) {
      const eventList = events.slice(0, 2).join(' and ');
      reasons.push(`${eventList} driving demand`);
    }

    // API performance
    if (apiLatency && apiLatency > 2.0) {
      reasons.push(`higher API latency (${apiLatency}s) requires caching`);
    }

    return reasons.join(' with ') + '.';
  }
}
