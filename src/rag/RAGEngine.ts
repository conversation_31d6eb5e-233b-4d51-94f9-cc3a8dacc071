import { VectorDatabase, TravelDocument, SearchResult } from './VectorDatabase';
import { QueryProcessor } from './QueryProcessor';
import { TravelAnalysisInput } from '../types';

export interface RAGContext {
  query: string;
  intent: {
    type: 'flight' | 'hotel' | 'experience' | 'general';
    locations: string[];
    dateRange?: {
      start: string;
      end: string;
    } | undefined;
    entities: string[];
    confidence: number;
  };
  retrievedDocuments: SearchResult[];
  synthesizedData: TravelAnalysisInput;
}

export class RAGEngine {
  private vectorDb: VectorDatabase;
  private queryProcessor: QueryProcessor;

  constructor() {
    this.vectorDb = new VectorDatabase();
    this.queryProcessor = new QueryProcessor();
  }

  /**
   * Initialize the RAG engine
   */
  async initialize(): Promise<void> {
    await this.vectorDb.initialize();
    console.log('RAG Engine initialized');
  }

  /**
   * Process a natural language query and retrieve relevant context
   */
  async processQuery(query: string): Promise<RAGContext> {
    try {
      console.log(`Processing query: "${query}"`);

      // Step 1: Parse and understand the query
      const intent = await this.queryProcessor.parseIntent(query);
      console.log('Parsed intent:', intent);

      // Step 2: Retrieve relevant documents from vector database
      const retrievedDocuments = await this.retrieveRelevantContext(query, intent);
      console.log(`Retrieved ${retrievedDocuments.length} relevant documents`);

      // Step 3: Synthesize the retrieved data into structured format
      const synthesizedData = await this.synthesizeData(retrievedDocuments, intent);

      return {
        query,
        intent,
        retrievedDocuments,
        synthesizedData,
      };
    } catch (error) {
      console.error('Failed to process query:', error);
      throw error;
    }
  }

  /**
   * Retrieve relevant context based on query and intent
   */
  private async retrieveRelevantContext(
    query: string,
    intent: any
  ): Promise<SearchResult[]> {
    const searchResults: SearchResult[] = [];

    // Search for general context
    const generalResults = await this.vectorDb.searchSimilar(query, 5);
    searchResults.push(...generalResults);

    // Search by intent type
    if (intent.type !== 'general') {
      const typeFilter = { type: intent.type };
      const typeResults = await this.vectorDb.searchSimilar(query, 3, typeFilter);
      searchResults.push(...typeResults);
    }

    // Search by locations
    for (const location of intent.locations) {
      const locationResults = await this.vectorDb.searchSimilar(
        `${location} ${intent.type} trends`,
        2,
        { location }
      );
      searchResults.push(...locationResults);
    }

    // Remove duplicates and sort by relevance
    const uniqueResults = this.deduplicateResults(searchResults);
    return uniqueResults.slice(0, 15); // Limit to top 15 results
  }

  /**
   * Remove duplicate search results
   */
  private deduplicateResults(results: SearchResult[]): SearchResult[] {
    const seen = new Set<string>();
    return results.filter(result => {
      if (seen.has(result.document.id)) {
        return false;
      }
      seen.add(result.document.id);
      return true;
    }).sort((a, b) => b.score - a.score);
  }

  /**
   * Synthesize retrieved documents into structured travel analysis input
   */
  private async synthesizeData(
    documents: SearchResult[],
    intent: any
  ): Promise<TravelAnalysisInput> {
    // Initialize empty structure
    const synthesizedData: TravelAnalysisInput = {
      flight_search_trends: [],
      hotel_search_trends: [],
      experience_search_trends: [],
      content_metadata: {},
      booking_conversion: {},
      api_performance: {
        flights: { avg_latency: 2.0, failures_last_24h: 0 },
        hotels: { avg_latency: 1.5, failures_last_24h: 0 },
        experiences: { avg_latency: 2.2, failures_last_24h: 0 },
      },
      events: [],
    };

    // Process each document and extract relevant data
    for (const result of documents) {
      const doc = result.document;
      
      try {
        // Parse document content as JSON if possible
        const parsedContent = this.tryParseJSON(doc.content);
        
        if (parsedContent) {
          this.mergeDataIntoSynthesis(synthesizedData, parsedContent, doc.metadata.type);
        } else {
          // Handle text-based documents
          this.extractDataFromText(synthesizedData, doc);
        }
      } catch (error) {
        console.warn(`Failed to process document ${doc.id}:`, error);
      }
    }

    // Fill in default values if no data was found
    this.ensureMinimumData(synthesizedData, intent);

    return synthesizedData;
  }

  /**
   * Try to parse content as JSON
   */
  private tryParseJSON(content: string): any {
    try {
      return JSON.parse(content);
    } catch {
      return null;
    }
  }

  /**
   * Merge parsed data into synthesis based on type
   */
  private mergeDataIntoSynthesis(
    synthesis: TravelAnalysisInput,
    data: any,
    type: string
  ): void {
    switch (type) {
      case 'flight':
        if (data.flight_search_trends) {
          synthesis.flight_search_trends.push(...data.flight_search_trends);
        }
        break;
      case 'hotel':
        if (data.hotel_search_trends) {
          synthesis.hotel_search_trends.push(...data.hotel_search_trends);
        }
        break;
      case 'experience':
        if (data.experience_search_trends) {
          synthesis.experience_search_trends.push(...data.experience_search_trends);
        }
        break;
      case 'event':
        if (data.events) {
          synthesis.events.push(...data.events);
        }
        break;
      case 'analytics':
        if (data.content_metadata) {
          Object.assign(synthesis.content_metadata, data.content_metadata);
        }
        if (data.booking_conversion) {
          Object.assign(synthesis.booking_conversion, data.booking_conversion);
        }
        if (data.api_performance) {
          Object.assign(synthesis.api_performance, data.api_performance);
        }
        break;
    }
  }

  /**
   * Extract data from text-based documents
   */
  private extractDataFromText(synthesis: TravelAnalysisInput, doc: TravelDocument): void {
    const content = doc.content.toLowerCase();
    const metadata = doc.metadata;

    // Extract events from text
    if (content.includes('event') || content.includes('holiday') || content.includes('festival')) {
      const eventMatch = doc.content.match(/([A-Z][^.!?]*(?:event|holiday|festival|celebration)[^.!?]*)/i);
      if (eventMatch && eventMatch[1]) {
        synthesis.events.push(eventMatch[1].trim());
      }
    }

    // Extract location-based insights
    if (metadata.location) {
      const location = metadata.location;
      
      // Create synthetic data based on text content
      if (content.includes('flight') || content.includes('airline')) {
        // Add synthetic flight trend
        synthesis.flight_search_trends.push({
          from_city: location,
          to_city: 'Popular Destination',
          trip_type: 'Round Trip',
          start_date: new Date().toISOString().split('T')[0]!,
          pax: { adult: 2, child: 0, infant: 0 },
          is_refundable: true,
          fare_type: 'Regular',
          search_count: Math.floor(Math.random() * 10000) + 1000,
          booking_conversion: Math.random() * 0.5 + 0.2,
        });
      }
    }
  }

  /**
   * Ensure minimum data is present for analysis
   */
  private ensureMinimumData(synthesis: TravelAnalysisInput, intent: any): void {
    // If no data was found, create synthetic data based on intent
    if (
      synthesis.flight_search_trends.length === 0 &&
      synthesis.hotel_search_trends.length === 0 &&
      synthesis.experience_search_trends.length === 0
    ) {
      console.log('No specific data found, generating synthetic data based on intent');
      
      for (const location of intent.locations) {
        if (intent.type === 'flight' || intent.type === 'general') {
          synthesis.flight_search_trends.push({
            from_city: location,
            to_city: 'Popular Destination',
            trip_type: 'Round Trip',
            start_date: new Date().toISOString().split('T')[0]!,
            pax: { adult: 2, child: 0, infant: 0 },
            is_refundable: true,
            fare_type: 'Regular',
            search_count: 5000,
            booking_conversion: 0.35,
          });
        }
      }
    }
  }

  /**
   * Store new travel data in the vector database
   */
  async storeData(data: TravelAnalysisInput, source: string = 'api'): Promise<void> {
    const documents: TravelDocument[] = [];

    // Store flight data
    if (data.flight_search_trends.length > 0) {
      documents.push(
        VectorDatabase.createTravelDocument(
          JSON.stringify({ flight_search_trends: data.flight_search_trends }),
          'flight',
          { source, locations: this.extractLocationsFromFlights(data.flight_search_trends) }
        )
      );
    }

    // Store hotel data
    if (data.hotel_search_trends.length > 0) {
      documents.push(
        VectorDatabase.createTravelDocument(
          JSON.stringify({ hotel_search_trends: data.hotel_search_trends }),
          'hotel',
          { source, locations: this.extractLocationsFromHotels(data.hotel_search_trends) }
        )
      );
    }

    // Store experience data
    if (data.experience_search_trends.length > 0) {
      documents.push(
        VectorDatabase.createTravelDocument(
          JSON.stringify({ experience_search_trends: data.experience_search_trends }),
          'experience',
          { source }
        )
      );
    }

    // Store analytics data
    documents.push(
      VectorDatabase.createTravelDocument(
        JSON.stringify({
          content_metadata: data.content_metadata,
          booking_conversion: data.booking_conversion,
          api_performance: data.api_performance,
        }),
        'analytics',
        { source }
      )
    );

    // Store events
    if (data.events.length > 0) {
      documents.push(
        VectorDatabase.createTravelDocument(
          JSON.stringify({ events: data.events }),
          'event',
          { source }
        )
      );
    }

    await this.vectorDb.storeDocuments(documents);
    console.log(`Stored ${documents.length} documents from ${source}`);
  }

  private extractLocationsFromFlights(flights: any[]): string {
    const locations = new Set<string>();
    flights.forEach(flight => {
      locations.add(flight.from_city);
      locations.add(flight.to_city);
    });
    return Array.from(locations).join(', ');
  }

  private extractLocationsFromHotels(hotels: any[]): string {
    const locations = new Set<string>();
    hotels.forEach(hotel => {
      locations.add(hotel.city);
    });
    return Array.from(locations).join(', ');
  }

  /**
   * Get RAG engine statistics
   */
  async getStats(): Promise<any> {
    try {
      return await this.vectorDb.getStats();
    } catch (error) {
      console.error('Failed to get RAG stats:', error);
      return null;
    }
  }
}
