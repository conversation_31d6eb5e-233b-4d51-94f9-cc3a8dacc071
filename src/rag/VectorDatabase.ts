import { Pinecone } from '@pinecone-database/pinecone';
import OpenAI from 'openai';
import { v4 as uuidv4 } from 'uuid';

export interface TravelDocument {
  id: string;
  content: string;
  metadata: {
    type: 'flight' | 'hotel' | 'experience' | 'event' | 'analytics';
    location?: string;
    date?: string;
    source: string;
    timestamp: number;
    [key: string]: any;
  };
}

export interface SearchResult {
  document: TravelDocument;
  score: number;
}

export class VectorDatabase {
  private pinecone: Pinecone;
  private openai: OpenAI;
  private indexName: string;

  constructor() {
    this.pinecone = new Pinecone({
      apiKey: process.env.PINECONE_API_KEY || '',
    });
    
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY || '',
    });
    
    this.indexName = process.env.PINECONE_INDEX_NAME || 'travel-cache-strategist';
  }

  /**
   * Initialize the vector database index
   */
  async initialize(): Promise<void> {
    try {
      // Check if index exists
      const indexList = await this.pinecone.listIndexes();
      const indexExists = indexList.indexes?.some(index => index.name === this.indexName);

      if (!indexExists) {
        console.log(`Creating Pinecone index: ${this.indexName}`);
        await this.pinecone.createIndex({
          name: this.indexName,
          dimension: 1536, // OpenAI embedding dimension
          metric: 'cosine',
          spec: {
            serverless: {
              cloud: 'aws',
              region: 'us-east-1'
            }
          }
        });
        
        // Wait for index to be ready
        await this.waitForIndexReady();
      }
      
      console.log(`Vector database initialized with index: ${this.indexName}`);
    } catch (error) {
      console.error('Failed to initialize vector database:', error);
      throw error;
    }
  }

  /**
   * Wait for index to be ready
   */
  private async waitForIndexReady(): Promise<void> {
    let isReady = false;
    let attempts = 0;
    const maxAttempts = 30;

    while (!isReady && attempts < maxAttempts) {
      try {
        const indexStats = await this.pinecone.index(this.indexName).describeIndexStats();
        isReady = true;
        console.log('Index is ready:', indexStats);
      } catch (error) {
        attempts++;
        console.log(`Waiting for index to be ready... (${attempts}/${maxAttempts})`);
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }

    if (!isReady) {
      throw new Error('Index failed to become ready within timeout');
    }
  }

  /**
   * Generate embeddings for text using OpenAI
   */
  async generateEmbedding(text: string): Promise<number[]> {
    try {
      const response = await this.openai.embeddings.create({
        model: 'text-embedding-ada-002',
        input: text,
      });

      return response.data[0]?.embedding || [];
    } catch (error) {
      console.error('Failed to generate embedding:', error);
      throw error;
    }
  }

  /**
   * Store a travel document in the vector database
   */
  async storeDocument(document: TravelDocument): Promise<void> {
    try {
      const embedding = await this.generateEmbedding(document.content);
      
      const index = this.pinecone.index(this.indexName);
      await index.upsert([
        {
          id: document.id,
          values: embedding,
          metadata: {
            content: document.content,
            ...document.metadata,
          },
        },
      ]);

      console.log(`Stored document: ${document.id}`);
    } catch (error) {
      console.error('Failed to store document:', error);
      throw error;
    }
  }

  /**
   * Store multiple documents in batch
   */
  async storeDocuments(documents: TravelDocument[]): Promise<void> {
    try {
      const vectors = await Promise.all(
        documents.map(async (doc) => {
          const embedding = await this.generateEmbedding(doc.content);
          return {
            id: doc.id,
            values: embedding,
            metadata: {
              content: doc.content,
              ...doc.metadata,
            },
          };
        })
      );

      const index = this.pinecone.index(this.indexName);
      await index.upsert(vectors);

      console.log(`Stored ${documents.length} documents in batch`);
    } catch (error) {
      console.error('Failed to store documents:', error);
      throw error;
    }
  }

  /**
   * Search for similar documents using semantic search
   */
  async searchSimilar(
    query: string,
    topK: number = 10,
    filter?: Record<string, any>
  ): Promise<SearchResult[]> {
    try {
      const queryEmbedding = await this.generateEmbedding(query);
      
      const index = this.pinecone.index(this.indexName);
      const queryOptions: any = {
        vector: queryEmbedding,
        topK,
        includeMetadata: true,
      };

      if (filter) {
        queryOptions.filter = filter;
      }

      const searchResponse = await index.query(queryOptions);

      return searchResponse.matches?.map(match => ({
        document: {
          id: match.id || '',
          content: match.metadata?.content as string || '',
          metadata: {
            type: match.metadata?.type as any || 'analytics',
            location: match.metadata?.location as string,
            date: match.metadata?.date as string,
            source: match.metadata?.source as string || 'unknown',
            timestamp: match.metadata?.timestamp as number || Date.now(),
            ...match.metadata,
          },
        },
        score: match.score || 0,
      })) || [];
    } catch (error) {
      console.error('Failed to search documents:', error);
      throw error;
    }
  }

  /**
   * Delete documents by filter
   */
  async deleteDocuments(filter: Record<string, any>): Promise<void> {
    try {
      const index = this.pinecone.index(this.indexName);
      await index.deleteMany(filter);
      console.log('Deleted documents with filter:', filter);
    } catch (error) {
      console.error('Failed to delete documents:', error);
      throw error;
    }
  }

  /**
   * Get index statistics
   */
  async getStats(): Promise<any> {
    try {
      const index = this.pinecone.index(this.indexName);
      return await index.describeIndexStats();
    } catch (error) {
      console.error('Failed to get index stats:', error);
      throw error;
    }
  }

  /**
   * Create a travel document from raw data
   */
  static createTravelDocument(
    content: string,
    type: 'flight' | 'hotel' | 'experience' | 'event' | 'analytics',
    metadata: Partial<TravelDocument['metadata']> = {}
  ): TravelDocument {
    return {
      id: uuidv4(),
      content,
      metadata: {
        type,
        source: 'api',
        timestamp: Date.now(),
        ...metadata,
      },
    };
  }
}
