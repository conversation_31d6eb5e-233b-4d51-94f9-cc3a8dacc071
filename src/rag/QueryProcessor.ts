import OpenAI from 'openai';

export interface QueryIntent {
  type: 'flight' | 'hotel' | 'experience' | 'general';
  locations: string[];
  dateRange?: {
    start: string;
    end: string;
  } | undefined;
  entities: string[];
  confidence: number;
}

export class QueryProcessor {
  private openai: OpenAI;

  constructor() {
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY || '',
    });
  }

  /**
   * Parse natural language query to extract intent and entities
   */
  async parseIntent(query: string): Promise<QueryIntent> {
    try {
      const prompt = this.buildIntentExtractionPrompt(query);
      
      const response = await this.openai.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: 'You are an expert at parsing travel-related queries and extracting structured information.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.1,
        max_tokens: 500,
      });

      const result = response.choices[0]?.message?.content;
      if (!result) {
        throw new Error('No response from OpenAI');
      }

      return this.parseIntentResponse(result, query);
    } catch (error) {
      console.error('Failed to parse intent with OpenAI, falling back to rule-based parsing:', error);
      return this.fallbackIntentParsing(query);
    }
  }

  /**
   * Build prompt for intent extraction
   */
  private buildIntentExtractionPrompt(query: string): string {
    return `
Analyze this travel caching query and extract structured information:

Query: "${query}"

Extract the following information and respond in JSON format:
{
  "type": "flight|hotel|experience|general",
  "locations": ["city1", "city2", ...],
  "dateRange": {
    "start": "YYYY-MM-DD",
    "end": "YYYY-MM-DD"
  },
  "entities": ["entity1", "entity2", ...],
  "confidence": 0.0-1.0
}

Rules:
- type: Determine if the query is primarily about flights, hotels, experiences, or general travel
- locations: Extract all mentioned cities, countries, or regions
- dateRange: Extract any mentioned dates or date ranges (use current year if not specified)
- entities: Extract important travel-related entities (airlines, hotel chains, attractions, etc.)
- confidence: Rate how confident you are in the classification (0.0-1.0)

If no specific dates are mentioned, omit the dateRange field.
If the query is ambiguous, use "general" as the type.
`;
  }

  /**
   * Parse the OpenAI response into QueryIntent
   */
  private parseIntentResponse(response: string, originalQuery: string): QueryIntent {
    try {
      // Extract JSON from response
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in response');
      }

      const parsed = JSON.parse(jsonMatch[0]);
      
      return {
        type: parsed.type || 'general',
        locations: parsed.locations || [],
        dateRange: parsed.dateRange,
        entities: parsed.entities || [],
        confidence: parsed.confidence || 0.5,
      };
    } catch (error) {
      console.error('Failed to parse OpenAI response:', error);
      return this.fallbackIntentParsing(originalQuery);
    }
  }

  /**
   * Fallback rule-based intent parsing when OpenAI fails
   */
  private fallbackIntentParsing(query: string): QueryIntent {
    const lowerQuery = query.toLowerCase();
    
    // Determine type based on keywords
    let type: 'flight' | 'hotel' | 'experience' | 'general' = 'general';
    if (this.containsFlightKeywords(lowerQuery)) {
      type = 'flight';
    } else if (this.containsHotelKeywords(lowerQuery)) {
      type = 'hotel';
    } else if (this.containsExperienceKeywords(lowerQuery)) {
      type = 'experience';
    }

    // Extract locations using simple patterns
    const locations = this.extractLocations(query);
    
    // Extract entities
    const entities = this.extractEntities(query);

    // Extract date range
    const dateRange = this.extractDateRange(query);

    return {
      type,
      locations,
      dateRange,
      entities,
      confidence: 0.7, // Medium confidence for rule-based parsing
    };
  }

  /**
   * Check if query contains flight-related keywords
   */
  private containsFlightKeywords(query: string): boolean {
    const flightKeywords = [
      'flight', 'flights', 'airline', 'airlines', 'plane', 'aircraft',
      'departure', 'arrival', 'takeoff', 'landing', 'airport', 'terminal',
      'boarding', 'ticket', 'fare', 'route', 'connection'
    ];
    return flightKeywords.some(keyword => query.includes(keyword));
  }

  /**
   * Check if query contains hotel-related keywords
   */
  private containsHotelKeywords(query: string): boolean {
    const hotelKeywords = [
      'hotel', 'hotels', 'accommodation', 'stay', 'room', 'rooms',
      'booking', 'reservation', 'check-in', 'check-out', 'resort',
      'lodge', 'inn', 'motel', 'suite', 'cottage'
    ];
    return hotelKeywords.some(keyword => query.includes(keyword));
  }

  /**
   * Check if query contains experience-related keywords
   */
  private containsExperienceKeywords(query: string): boolean {
    const experienceKeywords = [
      'experience', 'experiences', 'activity', 'activities', 'tour', 'tours',
      'attraction', 'attractions', 'sightseeing', 'adventure', 'excursion',
      'museum', 'park', 'beach', 'restaurant', 'entertainment'
    ];
    return experienceKeywords.some(keyword => query.includes(keyword));
  }

  /**
   * Extract locations from query using simple patterns
   */
  private extractLocations(query: string): string[] {
    const locations: string[] = [];
    
    // Common city patterns
    const cityPatterns = [
      /\b(New York|NYC|Los Angeles|LA|San Francisco|Chicago|Miami|Boston|Seattle|Denver|Las Vegas|Orlando|Phoenix|San Diego|Dallas|Houston|Atlanta|Washington DC|DC)\b/gi,
      /\b(London|Paris|Rome|Barcelona|Amsterdam|Berlin|Vienna|Prague|Budapest|Istanbul|Athens|Lisbon|Madrid|Dublin|Edinburgh)\b/gi,
      /\b(Tokyo|Seoul|Bangkok|Singapore|Hong Kong|Kuala Lumpur|Jakarta|Manila|Ho Chi Minh|Hanoi|Mumbai|Delhi|Bangalore|Chennai)\b/gi,
      /\b(Sydney|Melbourne|Auckland|Brisbane|Perth|Adelaide|Wellington|Christchurch)\b/gi,
      /\b(Toronto|Vancouver|Montreal|Calgary|Ottawa|Quebec City)\b/gi,
      /\b(Mexico City|Cancun|Guadalajara|Monterrey|Tijuana)\b/gi,
      /\b(São Paulo|Rio de Janeiro|Buenos Aires|Lima|Bogotá|Santiago|Caracas)\b/gi,
      /\b(Cairo|Dubai|Doha|Riyadh|Kuwait City|Abu Dhabi|Muscat)\b/gi,
      /\b(Cape Town|Johannesburg|Lagos|Nairobi|Casablanca|Tunis)\b/gi,
      /\b(Mumbai|Delhi|Bangalore|Chennai|Kolkata|Hyderabad|Pune|Ahmedabad|Goa|Manali|Shimla|Jaipur|Udaipur)\b/gi
    ];

    cityPatterns.forEach(pattern => {
      const matches = query.match(pattern);
      if (matches) {
        locations.push(...matches.map(match => match.trim()));
      }
    });

    // Remove duplicates and return
    return [...new Set(locations)];
  }

  /**
   * Extract travel entities from query
   */
  private extractEntities(query: string): string[] {
    const entities: string[] = [];
    
    // Airlines
    const airlinePattern = /\b(American Airlines|Delta|United|Southwest|JetBlue|Alaska Airlines|Spirit|Frontier|IndiGo|Air India|SpiceJet|Vistara|Emirates|Qatar Airways|Singapore Airlines|Lufthansa|British Airways|Air France|KLM)\b/gi;
    const airlineMatches = query.match(airlinePattern);
    if (airlineMatches) {
      entities.push(...airlineMatches);
    }

    // Hotel chains
    const hotelPattern = /\b(Marriott|Hilton|Hyatt|InterContinental|Sheraton|Westin|Radisson|Holiday Inn|Best Western|Courtyard|Hampton Inn|Fairfield Inn)\b/gi;
    const hotelMatches = query.match(hotelPattern);
    if (hotelMatches) {
      entities.push(...hotelMatches);
    }

    // Events and holidays
    const eventPattern = /\b(Christmas|New Year|Easter|Thanksgiving|Independence Day|Memorial Day|Labor Day|Diwali|Holi|Eid|Chinese New Year|Valentine's Day|Halloween)\b/gi;
    const eventMatches = query.match(eventPattern);
    if (eventMatches) {
      entities.push(...eventMatches);
    }

    return [...new Set(entities)];
  }

  /**
   * Extract date range from query
   */
  private extractDateRange(query: string): { start: string; end: string } | undefined {
    const currentYear = new Date().getFullYear();
    
    // Look for date patterns
    const datePatterns = [
      /(\d{4}-\d{2}-\d{2})/g, // YYYY-MM-DD
      /(\d{1,2}\/\d{1,2}\/\d{4})/g, // MM/DD/YYYY
      /(\d{1,2}-\d{1,2}-\d{4})/g, // MM-DD-YYYY
    ];

    const dates: string[] = [];
    datePatterns.forEach(pattern => {
      const matches = query.match(pattern);
      if (matches) {
        dates.push(...matches);
      }
    });

    // Look for month names
    const monthPattern = /\b(January|February|March|April|May|June|July|August|September|October|November|December)\s+(\d{1,2}),?\s*(\d{4})?\b/gi;
    const monthMatches = query.match(monthPattern);
    if (monthMatches) {
      monthMatches.forEach(match => {
        const parts = match.split(/\s+/);
        const month = parts[0];
        const day = parts[1]?.replace(',', '');
        const year = parts[2] || currentYear.toString();
        
        const monthNum = new Date(`${month} 1, 2000`).getMonth() + 1;
        const formattedDate = `${year}-${monthNum.toString().padStart(2, '0')}-${day?.padStart(2, '0')}`;
        dates.push(formattedDate);
      });
    }

    if (dates.length >= 2) {
      const sortedDates = dates.sort();
      return {
        start: sortedDates[0]!,
        end: sortedDates[sortedDates.length - 1]!,
      };
    } else if (dates.length === 1) {
      return {
        start: dates[0]!,
        end: dates[0]!,
      };
    }

    return undefined;
  }
}
