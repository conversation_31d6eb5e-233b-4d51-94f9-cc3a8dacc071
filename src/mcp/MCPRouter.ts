import { FlightAPIConnector } from './connectors/FlightAPIConnector';
import { HotelAPIConnector } from './connectors/HotelAPIConnector';
import { EventsAPIConnector } from './connectors/EventsAPIConnector';
import { WeatherAPIConnector } from './connectors/WeatherAPIConnector';
import { AnalyticsConnector } from './connectors/AnalyticsConnector';
import { TravelAnalysisInput } from '../types';

export interface MCPRequest {
  type: 'flight' | 'hotel' | 'experience' | 'event' | 'weather' | 'analytics';
  locations: string[];
  dateRange?: {
    start: string;
    end: string;
  } | undefined;
  parameters?: Record<string, any>;
}

export interface MCPResponse {
  success: boolean;
  data: any;
  source: string;
  timestamp: number;
  error?: string;
}

export class MCPRouter {
  private flightConnector: FlightAPIConnector;
  private hotelConnector: HotelAPIConnector;
  private eventsConnector: EventsAPIConnector;
  private weatherConnector: WeatherAPIConnector;
  private analyticsConnector: AnalyticsConnector;

  constructor() {
    this.flightConnector = new FlightAPIConnector();
    this.hotelConnector = new HotelAPIConnector();
    this.eventsConnector = new EventsAPIConnector();
    this.weatherConnector = new WeatherAPIConnector();
    this.analyticsConnector = new AnalyticsConnector();
  }

  /**
   * Route MCP request to appropriate connector
   */
  async routeRequest(request: MCPRequest): Promise<MCPResponse> {
    try {
      console.log(`Routing MCP request: ${request.type} for locations: ${request.locations.join(', ')}`);

      switch (request.type) {
        case 'flight':
          return await this.flightConnector.fetchData(request);
        case 'hotel':
          return await this.hotelConnector.fetchData(request);
        case 'experience':
          return await this.hotelConnector.fetchData(request); // Hotels often include experiences
        case 'event':
          return await this.eventsConnector.fetchData(request);
        case 'weather':
          return await this.weatherConnector.fetchData(request);
        case 'analytics':
          return await this.analyticsConnector.fetchData(request);
        default:
          throw new Error(`Unknown MCP request type: ${request.type}`);
      }
    } catch (error) {
      console.error(`MCP routing error for ${request.type}:`, error);
      return {
        success: false,
        data: null,
        source: `${request.type}_api`,
        timestamp: Date.now(),
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Fetch comprehensive travel data for multiple request types
   */
  async fetchComprehensiveData(
    locations: string[],
    dateRange?: { start: string; end: string }
  ): Promise<TravelAnalysisInput> {
    console.log('Fetching comprehensive travel data...');

    const requests: MCPRequest[] = [
      { type: 'flight', locations, dateRange: dateRange || undefined },
      { type: 'hotel', locations, dateRange: dateRange || undefined },
      { type: 'event', locations, dateRange: dateRange || undefined },
      { type: 'analytics', locations, dateRange: dateRange || undefined },
    ];

    // Execute all requests in parallel
    const responses = await Promise.allSettled(
      requests.map(request => this.routeRequest(request))
    );

    // Aggregate successful responses
    const aggregatedData: TravelAnalysisInput = {
      flight_search_trends: [],
      hotel_search_trends: [],
      experience_search_trends: [],
      content_metadata: {},
      booking_conversion: {},
      api_performance: {
        flights: { avg_latency: 2.0, failures_last_24h: 0 },
        hotels: { avg_latency: 1.5, failures_last_24h: 0 },
        experiences: { avg_latency: 2.2, failures_last_24h: 0 },
      },
      events: [],
    };

    responses.forEach((result, index) => {
      if (result.status === 'fulfilled' && result.value.success) {
        const response = result.value;
        const requestType = requests[index]?.type;

        this.mergeResponseData(aggregatedData, response, requestType);
      } else {
        console.warn(`Failed to fetch ${requests[index]?.type} data:`, 
          result.status === 'rejected' ? result.reason : result.value.error);
      }
    });

    return aggregatedData;
  }

  /**
   * Merge response data into aggregated travel analysis input
   */
  private mergeResponseData(
    aggregated: TravelAnalysisInput,
    response: MCPResponse,
    requestType?: string
  ): void {
    const data = response.data;

    switch (requestType) {
      case 'flight':
        if (data.flight_search_trends) {
          aggregated.flight_search_trends.push(...data.flight_search_trends);
        }
        if (data.content_metadata) {
          Object.assign(aggregated.content_metadata, data.content_metadata);
        }
        break;

      case 'hotel':
        if (data.hotel_search_trends) {
          aggregated.hotel_search_trends.push(...data.hotel_search_trends);
        }
        if (data.experience_search_trends) {
          aggregated.experience_search_trends.push(...data.experience_search_trends);
        }
        if (data.content_metadata) {
          Object.assign(aggregated.content_metadata, data.content_metadata);
        }
        break;

      case 'event':
        if (data.events) {
          aggregated.events.push(...data.events);
        }
        break;

      case 'analytics':
        if (data.booking_conversion) {
          Object.assign(aggregated.booking_conversion, data.booking_conversion);
        }
        if (data.api_performance) {
          Object.assign(aggregated.api_performance, data.api_performance);
        }
        if (data.content_metadata) {
          Object.assign(aggregated.content_metadata, data.content_metadata);
        }
        break;
    }
  }

  /**
   * Get health status of all connectors
   */
  async getHealthStatus(): Promise<Record<string, boolean>> {
    const connectors = {
      flight: this.flightConnector,
      hotel: this.hotelConnector,
      events: this.eventsConnector,
      weather: this.weatherConnector,
      analytics: this.analyticsConnector,
    };

    const healthStatus: Record<string, boolean> = {};

    for (const [name, connector] of Object.entries(connectors)) {
      try {
        healthStatus[name] = await connector.healthCheck();
      } catch (error) {
        console.error(`Health check failed for ${name}:`, error);
        healthStatus[name] = false;
      }
    }

    return healthStatus;
  }

  /**
   * Refresh cached data for all connectors
   */
  async refreshCache(): Promise<void> {
    console.log('Refreshing MCP connector caches...');

    const connectors = [
      this.flightConnector,
      this.hotelConnector,
      this.eventsConnector,
      this.weatherConnector,
      this.analyticsConnector,
    ];

    await Promise.allSettled(
      connectors.map(connector => {
        if ('refreshCache' in connector && typeof connector.refreshCache === 'function') {
          return connector.refreshCache();
        }
        return Promise.resolve();
      })
    );

    console.log('MCP connector cache refresh completed');
  }
}
