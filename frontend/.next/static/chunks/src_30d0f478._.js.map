{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Sherwyn/Project%20Repos/AI%20Projects%20/PrecatchingAnalyzer/frontend/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\nimport { CachingRecommendation, TTLChartData, RiskDistributionData } from '@/types';\n\n/**\n * Utility function to merge Tailwind CSS classes\n */\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\n/**\n * Format processing time in a human-readable format\n */\nexport function formatProcessingTime(ms: number): string {\n  if (ms < 1000) {\n    return `${ms}ms`;\n  }\n  return `${(ms / 1000).toFixed(1)}s`;\n}\n\n/**\n * Format confidence score as percentage\n */\nexport function formatConfidence(score: number): string {\n  return `${Math.round(score * 100)}%`;\n}\n\n/**\n * Get color for staleness risk level\n */\nexport function getRiskColor(risk: 'Low' | 'Medium' | 'High'): string {\n  switch (risk) {\n    case 'Low':\n      return 'text-green-600 bg-green-50 border-green-200';\n    case 'Medium':\n      return 'text-yellow-600 bg-yellow-50 border-yellow-200';\n    case 'High':\n      return 'text-red-600 bg-red-50 border-red-200';\n    default:\n      return 'text-gray-600 bg-gray-50 border-gray-200';\n  }\n}\n\n/**\n * Get icon for entity type\n */\nexport function getEntityIcon(entityType: string): string {\n  switch (entityType.toLowerCase()) {\n    case 'flight_route':\n    case 'flight':\n      return '✈️';\n    case 'hotel_cluster':\n    case 'hotel':\n      return '🏨';\n    case 'experience_group':\n    case 'experience':\n      return '🎯';\n    default:\n      return '📊';\n  }\n}\n\n/**\n * Format TTL hours in a human-readable format\n */\nexport function formatTTL(hours: number): string {\n  if (hours < 1) {\n    return `${Math.round(hours * 60)}min`;\n  }\n  if (hours < 24) {\n    return `${hours}h`;\n  }\n  const days = Math.floor(hours / 24);\n  const remainingHours = hours % 24;\n  if (remainingHours === 0) {\n    return `${days}d`;\n  }\n  return `${days}d ${remainingHours}h`;\n}\n\n/**\n * Convert recommendations to TTL chart data\n */\nexport function prepareTTLChartData(recommendations: CachingRecommendation[]): TTLChartData[] {\n  return recommendations.map((rec, index) => ({\n    name: rec.entity_id_or_name.length > 20 \n      ? rec.entity_id_or_name.substring(0, 20) + '...' \n      : rec.entity_id_or_name,\n    ttl: rec.suggested_ttl_hours,\n    risk: rec.staleness_risk,\n    type: rec.entity_type,\n  }));\n}\n\n/**\n * Calculate risk distribution data for pie chart\n */\nexport function prepareRiskDistributionData(recommendations: CachingRecommendation[]): RiskDistributionData[] {\n  const riskCounts = recommendations.reduce((acc, rec) => {\n    acc[rec.staleness_risk] = (acc[rec.staleness_risk] || 0) + 1;\n    return acc;\n  }, {} as Record<string, number>);\n\n  return [\n    { name: 'Low Risk', value: riskCounts.Low || 0, color: '#10b981' },\n    { name: 'Medium Risk', value: riskCounts.Medium || 0, color: '#f59e0b' },\n    { name: 'High Risk', value: riskCounts.High || 0, color: '#ef4444' },\n  ].filter(item => item.value > 0);\n}\n\n/**\n * Truncate text to specified length\n */\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text;\n  return text.substring(0, maxLength) + '...';\n}\n\n/**\n * Format data sources list\n */\nexport function formatDataSources(sources: string[]): string {\n  if (sources.length === 0) return 'No sources';\n  if (sources.length === 1) return sources[0]!;\n  if (sources.length === 2) return sources.join(' and ');\n  return `${sources.slice(0, -1).join(', ')}, and ${sources[sources.length - 1]}`;\n}\n\n/**\n * Get relative time string\n */\nexport function getRelativeTime(timestamp: string): string {\n  const now = new Date();\n  const time = new Date(timestamp);\n  const diffMs = now.getTime() - time.getTime();\n  const diffSeconds = Math.floor(diffMs / 1000);\n  const diffMinutes = Math.floor(diffSeconds / 60);\n  const diffHours = Math.floor(diffMinutes / 60);\n\n  if (diffSeconds < 60) {\n    return 'just now';\n  } else if (diffMinutes < 60) {\n    return `${diffMinutes}m ago`;\n  } else if (diffHours < 24) {\n    return `${diffHours}h ago`;\n  } else {\n    return time.toLocaleDateString();\n  }\n}\n\n/**\n * Validate query input\n */\nexport function validateQuery(query: string): { isValid: boolean; error?: string } {\n  if (!query.trim()) {\n    return { isValid: false, error: 'Query cannot be empty' };\n  }\n  \n  if (query.length < 5) {\n    return { isValid: false, error: 'Query is too short (minimum 5 characters)' };\n  }\n  \n  if (query.length > 500) {\n    return { isValid: false, error: 'Query is too long (maximum 500 characters)' };\n  }\n  \n  return { isValid: true };\n}\n\n/**\n * Generate sample queries based on context\n */\nexport function generateSampleQueries(): string[] {\n  return [\n    \"What should I cache for flights from Mumbai to Delhi next week?\",\n    \"Cache recommendations for hotels in Goa during Independence Day weekend\",\n    \"Analyze caching needs for Thailand experiences in August\",\n    \"What travel content should I pre-cache for the upcoming festival season?\",\n    \"Cache strategy for Paris attractions and hotels for summer vacation\",\n    \"Pre-cache recommendations for Manali cottages with limited inventory\"\n  ];\n}\n\n/**\n * Sort recommendations by priority\n */\nexport function sortRecommendationsByPriority(recommendations: CachingRecommendation[]): CachingRecommendation[] {\n  return [...recommendations].sort((a, b) => {\n    // First sort by risk (High > Medium > Low)\n    const riskOrder = { 'High': 3, 'Medium': 2, 'Low': 1 };\n    const riskDiff = riskOrder[b.staleness_risk] - riskOrder[a.staleness_risk];\n    if (riskDiff !== 0) return riskDiff;\n    \n    // Then sort by TTL (shorter TTL = higher priority)\n    return a.suggested_ttl_hours - b.suggested_ttl_hours;\n  });\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,QAAQ,KAAK;AACtB;AAKO,SAAS,qBAAqB,EAAU;IAC7C,IAAI,KAAK,MAAM;QACb,OAAO,GAAG,GAAG,EAAE,CAAC;IAClB;IACA,OAAO,GAAG,CAAC,KAAK,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;AACrC;AAKO,SAAS,iBAAiB,KAAa;IAC5C,OAAO,GAAG,KAAK,KAAK,CAAC,QAAQ,KAAK,CAAC,CAAC;AACtC;AAKO,SAAS,aAAa,IAA+B;IAC1D,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAKO,SAAS,cAAc,UAAkB;IAC9C,OAAQ,WAAW,WAAW;QAC5B,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAKO,SAAS,UAAU,KAAa;IACrC,IAAI,QAAQ,GAAG;QACb,OAAO,GAAG,KAAK,KAAK,CAAC,QAAQ,IAAI,GAAG,CAAC;IACvC;IACA,IAAI,QAAQ,IAAI;QACd,OAAO,GAAG,MAAM,CAAC,CAAC;IACpB;IACA,MAAM,OAAO,KAAK,KAAK,CAAC,QAAQ;IAChC,MAAM,iBAAiB,QAAQ;IAC/B,IAAI,mBAAmB,GAAG;QACxB,OAAO,GAAG,KAAK,CAAC,CAAC;IACnB;IACA,OAAO,GAAG,KAAK,EAAE,EAAE,eAAe,CAAC,CAAC;AACtC;AAKO,SAAS,oBAAoB,eAAwC;IAC1E,OAAO,gBAAgB,GAAG,CAAC,CAAC,KAAK,QAAU,CAAC;YAC1C,MAAM,IAAI,iBAAiB,CAAC,MAAM,GAAG,KACjC,IAAI,iBAAiB,CAAC,SAAS,CAAC,GAAG,MAAM,QACzC,IAAI,iBAAiB;YACzB,KAAK,IAAI,mBAAmB;YAC5B,MAAM,IAAI,cAAc;YACxB,MAAM,IAAI,WAAW;QACvB,CAAC;AACH;AAKO,SAAS,4BAA4B,eAAwC;IAClF,MAAM,aAAa,gBAAgB,MAAM,CAAC,CAAC,KAAK;QAC9C,GAAG,CAAC,IAAI,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,cAAc,CAAC,IAAI,CAAC,IAAI;QAC3D,OAAO;IACT,GAAG,CAAC;IAEJ,OAAO;QACL;YAAE,MAAM;YAAY,OAAO,WAAW,GAAG,IAAI;YAAG,OAAO;QAAU;QACjE;YAAE,MAAM;YAAe,OAAO,WAAW,MAAM,IAAI;YAAG,OAAO;QAAU;QACvE;YAAE,MAAM;YAAa,OAAO,WAAW,IAAI,IAAI;YAAG,OAAO;QAAU;KACpE,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,KAAK,GAAG;AAChC;AAKO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,SAAS,CAAC,GAAG,aAAa;AACxC;AAKO,SAAS,kBAAkB,OAAiB;IACjD,IAAI,QAAQ,MAAM,KAAK,GAAG,OAAO;IACjC,IAAI,QAAQ,MAAM,KAAK,GAAG,OAAO,OAAO,CAAC,EAAE;IAC3C,IAAI,QAAQ,MAAM,KAAK,GAAG,OAAO,QAAQ,IAAI,CAAC;IAC9C,OAAO,GAAG,QAAQ,KAAK,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,MAAM,MAAM,EAAE,OAAO,CAAC,QAAQ,MAAM,GAAG,EAAE,EAAE;AACjF;AAKO,SAAS,gBAAgB,SAAiB;IAC/C,MAAM,MAAM,IAAI;IAChB,MAAM,OAAO,IAAI,KAAK;IACtB,MAAM,SAAS,IAAI,OAAO,KAAK,KAAK,OAAO;IAC3C,MAAM,cAAc,KAAK,KAAK,CAAC,SAAS;IACxC,MAAM,cAAc,KAAK,KAAK,CAAC,cAAc;IAC7C,MAAM,YAAY,KAAK,KAAK,CAAC,cAAc;IAE3C,IAAI,cAAc,IAAI;QACpB,OAAO;IACT,OAAO,IAAI,cAAc,IAAI;QAC3B,OAAO,GAAG,YAAY,KAAK,CAAC;IAC9B,OAAO,IAAI,YAAY,IAAI;QACzB,OAAO,GAAG,UAAU,KAAK,CAAC;IAC5B,OAAO;QACL,OAAO,KAAK,kBAAkB;IAChC;AACF;AAKO,SAAS,cAAc,KAAa;IACzC,IAAI,CAAC,MAAM,IAAI,IAAI;QACjB,OAAO;YAAE,SAAS;YAAO,OAAO;QAAwB;IAC1D;IAEA,IAAI,MAAM,MAAM,GAAG,GAAG;QACpB,OAAO;YAAE,SAAS;YAAO,OAAO;QAA4C;IAC9E;IAEA,IAAI,MAAM,MAAM,GAAG,KAAK;QACtB,OAAO;YAAE,SAAS;YAAO,OAAO;QAA6C;IAC/E;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAKO,SAAS;IACd,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;KACD;AACH;AAKO,SAAS,8BAA8B,eAAwC;IACpF,OAAO;WAAI;KAAgB,CAAC,IAAI,CAAC,CAAC,GAAG;QACnC,2CAA2C;QAC3C,MAAM,YAAY;YAAE,QAAQ;YAAG,UAAU;YAAG,OAAO;QAAE;QACrD,MAAM,WAAW,SAAS,CAAC,EAAE,cAAc,CAAC,GAAG,SAAS,CAAC,EAAE,cAAc,CAAC;QAC1E,IAAI,aAAa,GAAG,OAAO;QAE3B,mDAAmD;QACnD,OAAO,EAAE,mBAAmB,GAAG,EAAE,mBAAmB;IACtD;AACF", "debugId": null}}, {"offset": {"line": 204, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Sherwyn/Project%20Repos/AI%20Projects%20/PrecatchingAnalyzer/frontend/src/components/QueryInterface.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { Send, Loader2, <PERSON>bulb, Sparkles } from 'lucide-react';\nimport { cn, validateQuery, generateSampleQueries } from '@/lib/utils';\n\ninterface QueryInterfaceProps {\n  onQuerySubmit: (query: string) => void;\n  isLoading: boolean;\n}\n\nexport function QueryInterface({ onQuerySubmit, isLoading }: QueryInterfaceProps) {\n  const [query, setQuery] = useState('');\n  const [error, setError] = useState<string | null>(null);\n  const [showSuggestions, setShowSuggestions] = useState(false);\n\n  const sampleQueries = generateSampleQueries();\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    const validation = validateQuery(query);\n    if (!validation.isValid) {\n      setError(validation.error || 'Invalid query');\n      return;\n    }\n\n    setError(null);\n    onQuerySubmit(query);\n  };\n\n  const handleSampleQuery = (sampleQuery: string) => {\n    setQuery(sampleQuery);\n    setShowSuggestions(false);\n    setError(null);\n  };\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {\n    setQuery(e.target.value);\n    if (error) setError(null);\n  };\n\n  return (\n    <div className=\"w-full max-w-4xl mx-auto\">\n      {/* Header */}\n      <motion.div\n        initial={{ opacity: 0, y: -20 }}\n        animate={{ opacity: 1, y: 0 }}\n        className=\"text-center mb-8\"\n      >\n        <div className=\"flex items-center justify-center space-x-2 mb-4\">\n          <Sparkles className=\"w-8 h-8 text-blue-600\" />\n          <h1 className=\"text-3xl font-bold text-gray-900\">\n            Travel Caching Strategist\n          </h1>\n        </div>\n        <p className=\"text-lg text-gray-600 max-w-2xl mx-auto\">\n          Ask me anything about travel content caching strategies. I'll analyze your query using \n          RAG + MCP and provide intelligent recommendations.\n        </p>\n      </motion.div>\n\n      {/* Query Form */}\n      <motion.form\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ delay: 0.1 }}\n        onSubmit={handleSubmit}\n        className=\"space-y-4\"\n      >\n        <div className=\"relative\">\n          <textarea\n            value={query}\n            onChange={handleInputChange}\n            placeholder=\"What should I cache for flights from Mumbai to Delhi next week?\"\n            className={cn(\n              'w-full p-4 pr-12 border rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors',\n              'min-h-[120px] text-lg',\n              error ? 'border-red-300 bg-red-50' : 'border-gray-300 bg-white'\n            )}\n            disabled={isLoading}\n          />\n          \n          {/* Submit Button */}\n          <button\n            type=\"submit\"\n            disabled={isLoading || !query.trim()}\n            className={cn(\n              'absolute bottom-4 right-4 p-2 rounded-lg transition-colors',\n              'disabled:opacity-50 disabled:cursor-not-allowed',\n              query.trim() && !isLoading\n                ? 'bg-blue-600 text-white hover:bg-blue-700'\n                : 'bg-gray-200 text-gray-400'\n            )}\n          >\n            {isLoading ? (\n              <Loader2 className=\"w-5 h-5 animate-spin\" />\n            ) : (\n              <Send className=\"w-5 h-5\" />\n            )}\n          </button>\n        </div>\n\n        {/* Error Message */}\n        {error && (\n          <motion.div\n            initial={{ opacity: 0, height: 0 }}\n            animate={{ opacity: 1, height: 'auto' }}\n            className=\"text-red-600 text-sm bg-red-50 p-3 rounded-lg border border-red-200\"\n          >\n            {error}\n          </motion.div>\n        )}\n\n        {/* Character Count */}\n        <div className=\"flex justify-between items-center text-sm text-gray-500\">\n          <span>{query.length}/500 characters</span>\n          <button\n            type=\"button\"\n            onClick={() => setShowSuggestions(!showSuggestions)}\n            className=\"flex items-center space-x-1 text-blue-600 hover:text-blue-700 transition-colors\"\n          >\n            <Lightbulb className=\"w-4 h-4\" />\n            <span>Need inspiration?</span>\n          </button>\n        </div>\n      </motion.form>\n\n      {/* Sample Queries */}\n      {showSuggestions && (\n        <motion.div\n          initial={{ opacity: 0, height: 0 }}\n          animate={{ opacity: 1, height: 'auto' }}\n          exit={{ opacity: 0, height: 0 }}\n          className=\"mt-6 space-y-3\"\n        >\n          <h3 className=\"text-sm font-medium text-gray-700 flex items-center space-x-2\">\n            <Lightbulb className=\"w-4 h-4\" />\n            <span>Try these sample queries:</span>\n          </h3>\n          \n          <div className=\"grid gap-2\">\n            {sampleQueries.map((sampleQuery, index) => (\n              <motion.button\n                key={index}\n                initial={{ opacity: 0, x: -20 }}\n                animate={{ opacity: 1, x: 0 }}\n                transition={{ delay: index * 0.05 }}\n                onClick={() => handleSampleQuery(sampleQuery)}\n                className=\"text-left p-3 bg-gray-50 hover:bg-gray-100 rounded-lg border border-gray-200 transition-colors text-sm\"\n                disabled={isLoading}\n              >\n                <span className=\"text-gray-700\">{sampleQuery}</span>\n              </motion.button>\n            ))}\n          </div>\n        </motion.div>\n      )}\n\n      {/* Loading State */}\n      {isLoading && (\n        <motion.div\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          className=\"mt-8 text-center\"\n        >\n          <div className=\"inline-flex items-center space-x-3 text-blue-600\">\n            <Loader2 className=\"w-6 h-6 animate-spin\" />\n            <span className=\"text-lg font-medium\">Analyzing your query...</span>\n          </div>\n          <p className=\"text-gray-500 mt-2\">\n            Using RAG + MCP to gather context and generate recommendations\n          </p>\n        </motion.div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;;;;;;;;;AAGA;;;AALA;;;;;AAYO,SAAS,eAAe,EAAE,aAAa,EAAE,SAAS,EAAuB;;IAC9E,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,MAAM,gBAAgB,CAAA,GAAA,sHAAA,CAAA,wBAAqB,AAAD;IAE1C,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAEhB,MAAM,aAAa,CAAA,GAAA,sHAAA,CAAA,gBAAa,AAAD,EAAE;QACjC,IAAI,CAAC,WAAW,OAAO,EAAE;YACvB,SAAS,WAAW,KAAK,IAAI;YAC7B;QACF;QAEA,SAAS;QACT,cAAc;IAChB;IAEA,MAAM,oBAAoB,CAAC;QACzB,SAAS;QACT,mBAAmB;QACnB,SAAS;IACX;IAEA,MAAM,oBAAoB,CAAC;QACzB,SAAS,EAAE,MAAM,CAAC,KAAK;QACvB,IAAI,OAAO,SAAS;IACtB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,OAAO,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG,CAAC;gBAAG;gBAC9B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,WAAU;;kCAEV,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAS,WAAU;;;;;;0CACpB,6LAAC;gCAAG,WAAU;0CAAmC;;;;;;;;;;;;kCAInD,6LAAC;wBAAE,WAAU;kCAA0C;;;;;;;;;;;;0BAOzD,6LAAC,OAAO,IAAI;gBACV,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,OAAO;gBAAI;gBACzB,UAAU;gBACV,WAAU;;kCAEV,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,OAAO;gCACP,UAAU;gCACV,aAAY;gCACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8HACA,yBACA,QAAQ,6BAA6B;gCAEvC,UAAU;;;;;;0CAIZ,6LAAC;gCACC,MAAK;gCACL,UAAU,aAAa,CAAC,MAAM,IAAI;gCAClC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8DACA,mDACA,MAAM,IAAI,MAAM,CAAC,YACb,6CACA;0CAGL,0BACC,6LAAC;oCAAQ,WAAU;;;;;yDAEnB,6LAAC;oCAAK,WAAU;;;;;;;;;;;;;;;;;oBAMrB,uBACC,6LAAC,OAAO,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,QAAQ;wBAAE;wBACjC,SAAS;4BAAE,SAAS;4BAAG,QAAQ;wBAAO;wBACtC,WAAU;kCAET;;;;;;kCAKL,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;oCAAM,MAAM,MAAM;oCAAC;;;;;;;0CACpB,6LAAC;gCACC,MAAK;gCACL,SAAS,IAAM,mBAAmB,CAAC;gCACnC,WAAU;;kDAEV,6LAAC;wCAAU,WAAU;;;;;;kDACrB,6LAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;YAMX,iCACC,6LAAC,OAAO,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,QAAQ;gBAAE;gBACjC,SAAS;oBAAE,SAAS;oBAAG,QAAQ;gBAAO;gBACtC,MAAM;oBAAE,SAAS;oBAAG,QAAQ;gBAAE;gBAC9B,WAAU;;kCAEV,6LAAC;wBAAG,WAAU;;0CACZ,6LAAC;gCAAU,WAAU;;;;;;0CACrB,6LAAC;0CAAK;;;;;;;;;;;;kCAGR,6LAAC;wBAAI,WAAU;kCACZ,cAAc,GAAG,CAAC,CAAC,aAAa,sBAC/B,6LAAC,OAAO,MAAM;gCAEZ,SAAS;oCAAE,SAAS;oCAAG,GAAG,CAAC;gCAAG;gCAC9B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,OAAO,QAAQ;gCAAK;gCAClC,SAAS,IAAM,kBAAkB;gCACjC,WAAU;gCACV,UAAU;0CAEV,cAAA,6LAAC;oCAAK,WAAU;8CAAiB;;;;;;+BAR5B;;;;;;;;;;;;;;;;YAgBd,2BACC,6LAAC,OAAO,GAAG;gBACT,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,WAAU;;kCAEV,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAQ,WAAU;;;;;;0CACnB,6LAAC;gCAAK,WAAU;0CAAsB;;;;;;;;;;;;kCAExC,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;;;;;;;AAO5C;GAtKgB;KAAA", "debugId": null}}, {"offset": {"line": 573, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Sherwyn/Project%20Repos/AI%20Projects%20/PrecatchingAnalyzer/frontend/src/components/RecommendationCard.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { Clock, AlertTriangle, TrendingUp, Info } from 'lucide-react';\nimport { CachingRecommendation } from '@/types';\nimport { cn, getRiskColor, getEntityIcon, formatTTL, truncateText } from '@/lib/utils';\n\ninterface RecommendationCardProps {\n  recommendation: CachingRecommendation;\n  index: number;\n}\n\nexport function RecommendationCard({ recommendation, index }: RecommendationCardProps) {\n  const riskColorClass = getRiskColor(recommendation.staleness_risk);\n  const entityIcon = getEntityIcon(recommendation.entity_type);\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ delay: index * 0.1, duration: 0.3 }}\n      className=\"bg-white rounded-lg border border-gray-200 p-6 shadow-sm hover:shadow-md transition-shadow duration-200\"\n    >\n      {/* Header */}\n      <div className=\"flex items-start justify-between mb-4\">\n        <div className=\"flex items-center space-x-3\">\n          <div className=\"text-2xl\">{entityIcon}</div>\n          <div>\n            <h3 className=\"font-semibold text-gray-900 text-lg\">\n              {truncateText(recommendation.entity_id_or_name, 30)}\n            </h3>\n            <p className=\"text-sm text-gray-500 capitalize\">\n              {recommendation.entity_type.replace('_', ' ')}\n            </p>\n          </div>\n        </div>\n        \n        {/* Risk Badge */}\n        <div className={cn(\n          'px-3 py-1 rounded-full text-xs font-medium border',\n          riskColorClass\n        )}>\n          <div className=\"flex items-center space-x-1\">\n            <AlertTriangle className=\"w-3 h-3\" />\n            <span>{recommendation.staleness_risk} Risk</span>\n          </div>\n        </div>\n      </div>\n\n      {/* TTL Information */}\n      <div className=\"flex items-center space-x-4 mb-4\">\n        <div className=\"flex items-center space-x-2 text-blue-600\">\n          <Clock className=\"w-4 h-4\" />\n          <span className=\"font-medium\">\n            TTL: {formatTTL(recommendation.suggested_ttl_hours)}\n          </span>\n        </div>\n        \n        <div className=\"flex items-center space-x-2 text-green-600\">\n          <TrendingUp className=\"w-4 h-4\" />\n          <span className=\"text-sm\">\n            {recommendation.suggested_ttl_hours}h cache\n          </span>\n        </div>\n      </div>\n\n      {/* Reasoning */}\n      <div className=\"bg-gray-50 rounded-lg p-4\">\n        <div className=\"flex items-start space-x-2\">\n          <Info className=\"w-4 h-4 text-gray-400 mt-0.5 flex-shrink-0\" />\n          <div>\n            <h4 className=\"text-sm font-medium text-gray-700 mb-1\">Reasoning</h4>\n            <p className=\"text-sm text-gray-600 leading-relaxed\">\n              {recommendation.reasoning}\n            </p>\n          </div>\n        </div>\n      </div>\n\n      {/* Priority Indicator */}\n      <div className=\"mt-4 flex items-center justify-between\">\n        <div className=\"flex items-center space-x-2\">\n          <div className={cn(\n            'w-2 h-2 rounded-full',\n            recommendation.staleness_risk === 'High' ? 'bg-red-500' :\n            recommendation.staleness_risk === 'Medium' ? 'bg-yellow-500' : 'bg-green-500'\n          )} />\n          <span className=\"text-xs text-gray-500\">\n            Priority: {recommendation.staleness_risk}\n          </span>\n        </div>\n        \n        <div className=\"text-xs text-gray-400\">\n          #{index + 1}\n        </div>\n      </div>\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAKA;AALA;;;;;AAYO,SAAS,mBAAmB,EAAE,cAAc,EAAE,KAAK,EAA2B;IACnF,MAAM,iBAAiB,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,eAAe,cAAc;IACjE,MAAM,aAAa,CAAA,GAAA,sHAAA,CAAA,gBAAa,AAAD,EAAE,eAAe,WAAW;IAE3D,qBACE,6LAAC,OAAO,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,OAAO,QAAQ;YAAK,UAAU;QAAI;QAChD,WAAU;;0BAGV,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAAY;;;;;;0CAC3B,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDACX,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,eAAe,iBAAiB,EAAE;;;;;;kDAElD,6LAAC;wCAAE,WAAU;kDACV,eAAe,WAAW,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;;;;;;;;kCAM/C,6LAAC;wBAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,qDACA;kCAEA,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAc,WAAU;;;;;;8CACzB,6LAAC;;wCAAM,eAAe,cAAc;wCAAC;;;;;;;;;;;;;;;;;;;;;;;;0BAM3C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAM,WAAU;;;;;;0CACjB,6LAAC;gCAAK,WAAU;;oCAAc;oCACtB,CAAA,GAAA,sHAAA,CAAA,YAAS,AAAD,EAAE,eAAe,mBAAmB;;;;;;;;;;;;;kCAItD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAW,WAAU;;;;;;0CACtB,6LAAC;gCAAK,WAAU;;oCACb,eAAe,mBAAmB;oCAAC;;;;;;;;;;;;;;;;;;;0BAM1C,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAK,WAAU;;;;;;sCAChB,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,6LAAC;oCAAE,WAAU;8CACV,eAAe,SAAS;;;;;;;;;;;;;;;;;;;;;;;0BAOjC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,wBACA,eAAe,cAAc,KAAK,SAAS,eAC3C,eAAe,cAAc,KAAK,WAAW,kBAAkB;;;;;;0CAEjE,6LAAC;gCAAK,WAAU;;oCAAwB;oCAC3B,eAAe,cAAc;;;;;;;;;;;;;kCAI5C,6LAAC;wBAAI,WAAU;;4BAAwB;4BACnC,QAAQ;;;;;;;;;;;;;;;;;;;AAKpB;KAtFgB", "debugId": null}}, {"offset": {"line": 870, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Sherwyn/Project%20Repos/AI%20Projects%20/PrecatchingAnalyzer/frontend/src/components/charts/ChartContainer.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { cn } from '@/lib/utils';\n\ninterface ChartContainerProps {\n  title: string;\n  children: React.ReactNode;\n  className?: string;\n  description?: string;\n}\n\nexport function ChartContainer({ title, children, className, description }: ChartContainerProps) {\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      className={cn(\n        'bg-white rounded-lg border border-gray-200 p-6 shadow-sm',\n        className\n      )}\n    >\n      <div className=\"mb-4\">\n        <h3 className=\"text-lg font-semibold text-gray-900\">{title}</h3>\n        {description && (\n          <p className=\"text-sm text-gray-600 mt-1\">{description}</p>\n        )}\n      </div>\n      <div className=\"w-full\">\n        {children}\n      </div>\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;AAGA;AAHA;;;;AAYO,SAAS,eAAe,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAuB;IAC7F,qBACE,6LAAC,OAAO,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4DACA;;0BAGF,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAuC;;;;;;oBACpD,6BACC,6LAAC;wBAAE,WAAU;kCAA8B;;;;;;;;;;;;0BAG/C,6LAAC;gBAAI,WAAU;0BACZ;;;;;;;;;;;;AAIT;KArBgB", "debugId": null}}, {"offset": {"line": 948, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Sherwyn/Project%20Repos/AI%20Projects%20/PrecatchingAnalyzer/frontend/src/components/charts/TTLChart.tsx"], "sourcesContent": ["'use client';\n\nimport { <PERSON><PERSON><PERSON>, Bar, <PERSON>Axis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Cell } from 'recharts';\nimport { TTLChartData } from '@/types';\nimport { formatTTL } from '@/lib/utils';\n\ninterface TTLChartProps {\n  data: TTLChartData[];\n}\n\nconst getRiskColor = (risk: string) => {\n  switch (risk) {\n    case 'High': return '#ef4444';\n    case 'Medium': return '#f59e0b';\n    case 'Low': return '#10b981';\n    default: return '#6b7280';\n  }\n};\n\nconst CustomTooltip = ({ active, payload, label }: any) => {\n  if (active && payload && payload.length) {\n    const data = payload[0].payload;\n    return (\n      <div className=\"bg-white p-3 border border-gray-200 rounded-lg shadow-lg\">\n        <p className=\"font-medium text-gray-900\">{label}</p>\n        <p className=\"text-sm text-gray-600 capitalize\">Type: {data.type.replace('_', ' ')}</p>\n        <p className=\"text-sm text-blue-600\">TTL: {formatTTL(data.ttl)}</p>\n        <p className=\"text-sm\" style={{ color: getRiskColor(data.risk) }}>\n          Risk: {data.risk}\n        </p>\n      </div>\n    );\n  }\n  return null;\n};\n\nexport function TTLChart({ data }: TTLChartProps) {\n  if (!data || data.length === 0) {\n    return (\n      <div className=\"h-64 flex items-center justify-center text-gray-500\">\n        No TTL data available\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"h-64\">\n      <ResponsiveContainer width=\"100%\" height=\"100%\">\n        <BarChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>\n          <CartesianGrid strokeDasharray=\"3 3\" stroke=\"#f3f4f6\" />\n          <XAxis \n            dataKey=\"name\" \n            tick={{ fontSize: 12 }}\n            stroke=\"#6b7280\"\n          />\n          <YAxis \n            tick={{ fontSize: 12 }}\n            stroke=\"#6b7280\"\n            label={{ value: 'Hours', angle: -90, position: 'insideLeft' }}\n          />\n          <Tooltip content={<CustomTooltip />} />\n          <Bar dataKey=\"ttl\" radius={[4, 4, 0, 0]}>\n            {data.map((entry, index) => (\n              <Cell key={`cell-${index}`} fill={getRiskColor(entry.risk)} />\n            ))}\n          </Bar>\n        </BarChart>\n      </ResponsiveContainer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;AAIA;AAJA;;;;AAUA,MAAM,eAAe,CAAC;IACpB,OAAQ;QACN,KAAK;YAAQ,OAAO;QACpB,KAAK;YAAU,OAAO;QACtB,KAAK;YAAO,OAAO;QACnB;YAAS,OAAO;IAClB;AACF;AAEA,MAAM,gBAAgB,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAO;IACpD,IAAI,UAAU,WAAW,QAAQ,MAAM,EAAE;QACvC,MAAM,OAAO,OAAO,CAAC,EAAE,CAAC,OAAO;QAC/B,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAE,WAAU;8BAA6B;;;;;;8BAC1C,6LAAC;oBAAE,WAAU;;wBAAmC;wBAAO,KAAK,IAAI,CAAC,OAAO,CAAC,KAAK;;;;;;;8BAC9E,6LAAC;oBAAE,WAAU;;wBAAwB;wBAAM,CAAA,GAAA,sHAAA,CAAA,YAAS,AAAD,EAAE,KAAK,GAAG;;;;;;;8BAC7D,6LAAC;oBAAE,WAAU;oBAAU,OAAO;wBAAE,OAAO,aAAa,KAAK,IAAI;oBAAE;;wBAAG;wBACzD,KAAK,IAAI;;;;;;;;;;;;;IAIxB;IACA,OAAO;AACT;KAfM;AAiBC,SAAS,SAAS,EAAE,IAAI,EAAiB;IAC9C,IAAI,CAAC,QAAQ,KAAK,MAAM,KAAK,GAAG;QAC9B,qBACE,6LAAC;YAAI,WAAU;sBAAsD;;;;;;IAIzE;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAoB,OAAM;YAAO,QAAO;sBACvC,cAAA,6LAAC;gBAAS,MAAM;gBAAM,QAAQ;oBAAE,KAAK;oBAAI,OAAO;oBAAI,MAAM;oBAAI,QAAQ;gBAAE;;kCACtE,6LAAC;wBAAc,iBAAgB;wBAAM,QAAO;;;;;;kCAC5C,6LAAC;wBACC,SAAQ;wBACR,MAAM;4BAAE,UAAU;wBAAG;wBACrB,QAAO;;;;;;kCAET,6LAAC;wBACC,MAAM;4BAAE,UAAU;wBAAG;wBACrB,QAAO;wBACP,OAAO;4BAAE,OAAO;4BAAS,OAAO,CAAC;4BAAI,UAAU;wBAAa;;;;;;kCAE9D,6LAAC;wBAAQ,uBAAS,6LAAC;;;;;;;;;;kCACnB,6LAAC;wBAAI,SAAQ;wBAAM,QAAQ;4BAAC;4BAAG;4BAAG;4BAAG;yBAAE;kCACpC,KAAK,GAAG,CAAC,CAAC,OAAO,sBAChB,6LAAC;gCAA2B,MAAM,aAAa,MAAM,IAAI;+BAA9C,CAAC,KAAK,EAAE,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;AAOxC;MAlCgB", "debugId": null}}, {"offset": {"line": 1154, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Sherwyn/Project%20Repos/AI%20Projects%20/PrecatchingAnalyzer/frontend/src/components/charts/RiskDistributionChart.tsx"], "sourcesContent": ["'use client';\n\nimport { <PERSON><PERSON><PERSON>, <PERSON>, Cell, Responsive<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Legend } from 'recharts';\nimport { RiskDistributionData } from '@/types';\n\ninterface RiskDistributionChartProps {\n  data: RiskDistributionData[];\n}\n\nconst CustomTooltip = ({ active, payload }: any) => {\n  if (active && payload && payload.length) {\n    const data = payload[0].payload;\n    return (\n      <div className=\"bg-white p-3 border border-gray-200 rounded-lg shadow-lg\">\n        <p className=\"font-medium text-gray-900\">{data.name}</p>\n        <p className=\"text-sm text-gray-600\">Count: {data.value}</p>\n        <p className=\"text-sm text-gray-600\">\n          Percentage: {((data.value / payload[0].payload.total) * 100).toFixed(1)}%\n        </p>\n      </div>\n    );\n  }\n  return null;\n};\n\nconst CustomLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent }: any) => {\n  if (percent < 0.05) return null; // Don't show labels for slices smaller than 5%\n  \n  const RADIAN = Math.PI / 180;\n  const radius = innerRadius + (outerRadius - innerRadius) * 0.5;\n  const x = cx + radius * Math.cos(-midAngle * RADIAN);\n  const y = cy + radius * Math.sin(-midAngle * RADIAN);\n\n  return (\n    <text \n      x={x} \n      y={y} \n      fill=\"white\" \n      textAnchor={x > cx ? 'start' : 'end'} \n      dominantBaseline=\"central\"\n      fontSize={12}\n      fontWeight=\"medium\"\n    >\n      {`${(percent * 100).toFixed(0)}%`}\n    </text>\n  );\n};\n\nexport function RiskDistributionChart({ data }: RiskDistributionChartProps) {\n  if (!data || data.length === 0) {\n    return (\n      <div className=\"h-64 flex items-center justify-center text-gray-500\">\n        No risk distribution data available\n      </div>\n    );\n  }\n\n  // Calculate total for percentage calculation\n  const total = data.reduce((sum, item) => sum + item.value, 0);\n  const dataWithTotal = data.map(item => ({ ...item, total }));\n\n  return (\n    <div className=\"h-64\">\n      <ResponsiveContainer width=\"100%\" height=\"100%\">\n        <PieChart>\n          <Pie\n            data={dataWithTotal}\n            cx=\"50%\"\n            cy=\"50%\"\n            labelLine={false}\n            label={CustomLabel}\n            outerRadius={80}\n            fill=\"#8884d8\"\n            dataKey=\"value\"\n          >\n            {dataWithTotal.map((entry, index) => (\n              <Cell key={`cell-${index}`} fill={entry.color} />\n            ))}\n          </Pie>\n          <Tooltip content={<CustomTooltip />} />\n          <Legend \n            verticalAlign=\"bottom\" \n            height={36}\n            formatter={(value, entry) => (\n              <span style={{ color: entry.color }}>{value}</span>\n            )}\n          />\n        </PieChart>\n      </ResponsiveContainer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;;AASA,MAAM,gBAAgB,CAAC,EAAE,MAAM,EAAE,OAAO,EAAO;IAC7C,IAAI,UAAU,WAAW,QAAQ,MAAM,EAAE;QACvC,MAAM,OAAO,OAAO,CAAC,EAAE,CAAC,OAAO;QAC/B,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAE,WAAU;8BAA6B,KAAK,IAAI;;;;;;8BACnD,6LAAC;oBAAE,WAAU;;wBAAwB;wBAAQ,KAAK,KAAK;;;;;;;8BACvD,6LAAC;oBAAE,WAAU;;wBAAwB;wBACtB,CAAC,AAAC,KAAK,KAAK,GAAG,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,KAAK,GAAI,GAAG,EAAE,OAAO,CAAC;wBAAG;;;;;;;;;;;;;IAIhF;IACA,OAAO;AACT;KAdM;AAgBN,MAAM,cAAc,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,QAAQ,EAAE,WAAW,EAAE,WAAW,EAAE,OAAO,EAAO;IAC/E,IAAI,UAAU,MAAM,OAAO,MAAM,+CAA+C;IAEhF,MAAM,SAAS,KAAK,EAAE,GAAG;IACzB,MAAM,SAAS,cAAc,CAAC,cAAc,WAAW,IAAI;IAC3D,MAAM,IAAI,KAAK,SAAS,KAAK,GAAG,CAAC,CAAC,WAAW;IAC7C,MAAM,IAAI,KAAK,SAAS,KAAK,GAAG,CAAC,CAAC,WAAW;IAE7C,qBACE,6LAAC;QACC,GAAG;QACH,GAAG;QACH,MAAK;QACL,YAAY,IAAI,KAAK,UAAU;QAC/B,kBAAiB;QACjB,UAAU;QACV,YAAW;kBAEV,GAAG,CAAC,UAAU,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;;;;;;AAGvC;MArBM;AAuBC,SAAS,sBAAsB,EAAE,IAAI,EAA8B;IACxE,IAAI,CAAC,QAAQ,KAAK,MAAM,KAAK,GAAG;QAC9B,qBACE,6LAAC;YAAI,WAAU;sBAAsD;;;;;;IAIzE;IAEA,6CAA6C;IAC7C,MAAM,QAAQ,KAAK,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,KAAK,EAAE;IAC3D,MAAM,gBAAgB,KAAK,GAAG,CAAC,CAAA,OAAQ,CAAC;YAAE,GAAG,IAAI;YAAE;QAAM,CAAC;IAE1D,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAoB,OAAM;YAAO,QAAO;sBACvC,cAAA,6LAAC;;kCACC,6LAAC;wBACC,MAAM;wBACN,IAAG;wBACH,IAAG;wBACH,WAAW;wBACX,OAAO;wBACP,aAAa;wBACb,MAAK;wBACL,SAAQ;kCAEP,cAAc,GAAG,CAAC,CAAC,OAAO,sBACzB,6LAAC;gCAA2B,MAAM,MAAM,KAAK;+BAAlC,CAAC,KAAK,EAAE,OAAO;;;;;;;;;;kCAG9B,6LAAC;wBAAQ,uBAAS,6LAAC;;;;;;;;;;kCACnB,6LAAC;wBACC,eAAc;wBACd,QAAQ;wBACR,WAAW,CAAC,OAAO,sBACjB,6LAAC;gCAAK,OAAO;oCAAE,OAAO,MAAM,KAAK;gCAAC;0CAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpD;MA3CgB", "debugId": null}}, {"offset": {"line": 1340, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Sherwyn/Project%20Repos/AI%20Projects%20/PrecatchingAnalyzer/frontend/src/components/HealthStatus.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { CheckCircle, XCircle, AlertCircle, Activity } from 'lucide-react';\nimport { HealthResponse } from '@/types';\nimport { cn, getRelativeTime } from '@/lib/utils';\n\ninterface HealthStatusProps {\n  healthData: HealthResponse | null;\n  isLoading: boolean;\n  error: string | null;\n}\n\nconst getStatusIcon = (status: boolean) => {\n  return status ? (\n    <CheckCircle className=\"w-4 h-4 text-green-500\" />\n  ) : (\n    <XCircle className=\"w-4 h-4 text-red-500\" />\n  );\n};\n\nconst getStatusColor = (status: boolean) => {\n  return status \n    ? 'text-green-700 bg-green-50 border-green-200' \n    : 'text-red-700 bg-red-50 border-red-200';\n};\n\nexport function HealthStatus({ healthData, isLoading, error }: HealthStatusProps) {\n  if (isLoading) {\n    return (\n      <motion.div\n        initial={{ opacity: 0 }}\n        animate={{ opacity: 1 }}\n        className=\"bg-white rounded-lg border border-gray-200 p-6 shadow-sm\"\n      >\n        <div className=\"flex items-center space-x-2 mb-4\">\n          <Activity className=\"w-5 h-5 text-blue-600 animate-pulse\" />\n          <h3 className=\"text-lg font-semibold text-gray-900\">System Health</h3>\n        </div>\n        <div className=\"text-gray-500\">Checking system health...</div>\n      </motion.div>\n    );\n  }\n\n  if (error) {\n    return (\n      <motion.div\n        initial={{ opacity: 0 }}\n        animate={{ opacity: 1 }}\n        className=\"bg-white rounded-lg border border-red-200 p-6 shadow-sm\"\n      >\n        <div className=\"flex items-center space-x-2 mb-4\">\n          <AlertCircle className=\"w-5 h-5 text-red-600\" />\n          <h3 className=\"text-lg font-semibold text-gray-900\">System Health</h3>\n        </div>\n        <div className=\"text-red-600 text-sm\">\n          Failed to fetch health status: {error}\n        </div>\n      </motion.div>\n    );\n  }\n\n  if (!healthData) {\n    return null;\n  }\n\n  const components = healthData.components || {};\n  const componentEntries = Object.entries(components);\n  const healthyCount = componentEntries.filter(([, status]) => status).length;\n  const totalCount = componentEntries.length;\n  const overallHealthy = healthyCount === totalCount;\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      className=\"bg-white rounded-lg border border-gray-200 p-6 shadow-sm\"\n    >\n      {/* Header */}\n      <div className=\"flex items-center justify-between mb-4\">\n        <div className=\"flex items-center space-x-2\">\n          <Activity className=\"w-5 h-5 text-blue-600\" />\n          <h3 className=\"text-lg font-semibold text-gray-900\">System Health</h3>\n        </div>\n        \n        <div className={cn(\n          'px-3 py-1 rounded-full text-xs font-medium border',\n          overallHealthy \n            ? 'text-green-700 bg-green-50 border-green-200'\n            : 'text-red-700 bg-red-50 border-red-200'\n        )}>\n          {overallHealthy ? 'All Systems Operational' : 'Issues Detected'}\n        </div>\n      </div>\n\n      {/* Overall Status */}\n      <div className=\"mb-4 p-3 bg-gray-50 rounded-lg\">\n        <div className=\"flex items-center justify-between\">\n          <span className=\"text-sm font-medium text-gray-700\">\n            Components Status\n          </span>\n          <span className=\"text-sm text-gray-600\">\n            {healthyCount}/{totalCount} healthy\n          </span>\n        </div>\n        \n        <div className=\"mt-2 w-full bg-gray-200 rounded-full h-2\">\n          <div \n            className={cn(\n              'h-2 rounded-full transition-all duration-300',\n              overallHealthy ? 'bg-green-500' : 'bg-red-500'\n            )}\n            style={{ width: `${(healthyCount / totalCount) * 100}%` }}\n          />\n        </div>\n      </div>\n\n      {/* Component Details */}\n      <div className=\"space-y-2\">\n        {componentEntries.map(([component, status], index) => (\n          <motion.div\n            key={component}\n            initial={{ opacity: 0, x: -20 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ delay: index * 0.05 }}\n            className={cn(\n              'flex items-center justify-between p-3 rounded-lg border',\n              getStatusColor(status)\n            )}\n          >\n            <div className=\"flex items-center space-x-3\">\n              {getStatusIcon(status)}\n              <span className=\"font-medium capitalize\">\n                {component.replace(/_/g, ' ')}\n              </span>\n            </div>\n            \n            <span className=\"text-xs font-medium\">\n              {status ? 'Healthy' : 'Unhealthy'}\n            </span>\n          </motion.div>\n        ))}\n      </div>\n\n      {/* Last Updated */}\n      <div className=\"mt-4 pt-4 border-t border-gray-200\">\n        <div className=\"flex items-center justify-between text-xs text-gray-500\">\n          <span>Last updated: {getRelativeTime(healthData.timestamp)}</span>\n          <span>{healthData.service}</span>\n        </div>\n      </div>\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAKA;AALA;;;;;AAaA,MAAM,gBAAgB,CAAC;IACrB,OAAO,uBACL,6LAAC;QAAY,WAAU;;;;;6BAEvB,6LAAC;QAAQ,WAAU;;;;;;AAEvB;AAEA,MAAM,iBAAiB,CAAC;IACtB,OAAO,SACH,gDACA;AACN;AAEO,SAAS,aAAa,EAAE,UAAU,EAAE,SAAS,EAAE,KAAK,EAAqB;IAC9E,IAAI,WAAW;QACb,qBACE,6LAAC,OAAO,GAAG;YACT,SAAS;gBAAE,SAAS;YAAE;YACtB,SAAS;gBAAE,SAAS;YAAE;YACtB,WAAU;;8BAEV,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAS,WAAU;;;;;;sCACpB,6LAAC;4BAAG,WAAU;sCAAsC;;;;;;;;;;;;8BAEtD,6LAAC;oBAAI,WAAU;8BAAgB;;;;;;;;;;;;IAGrC;IAEA,IAAI,OAAO;QACT,qBACE,6LAAC,OAAO,GAAG;YACT,SAAS;gBAAE,SAAS;YAAE;YACtB,SAAS;gBAAE,SAAS;YAAE;YACtB,WAAU;;8BAEV,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAY,WAAU;;;;;;sCACvB,6LAAC;4BAAG,WAAU;sCAAsC;;;;;;;;;;;;8BAEtD,6LAAC;oBAAI,WAAU;;wBAAuB;wBACJ;;;;;;;;;;;;;IAIxC;IAEA,IAAI,CAAC,YAAY;QACf,OAAO;IACT;IAEA,MAAM,aAAa,WAAW,UAAU,IAAI,CAAC;IAC7C,MAAM,mBAAmB,OAAO,OAAO,CAAC;IACxC,MAAM,eAAe,iBAAiB,MAAM,CAAC,CAAC,GAAG,OAAO,GAAK,QAAQ,MAAM;IAC3E,MAAM,aAAa,iBAAiB,MAAM;IAC1C,MAAM,iBAAiB,iBAAiB;IAExC,qBACE,6LAAC,OAAO,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,WAAU;;0BAGV,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAS,WAAU;;;;;;0CACpB,6LAAC;gCAAG,WAAU;0CAAsC;;;;;;;;;;;;kCAGtD,6LAAC;wBAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,qDACA,iBACI,gDACA;kCAEH,iBAAiB,4BAA4B;;;;;;;;;;;;0BAKlD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CAAoC;;;;;;0CAGpD,6LAAC;gCAAK,WAAU;;oCACb;oCAAa;oCAAE;oCAAW;;;;;;;;;;;;;kCAI/B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gDACA,iBAAiB,iBAAiB;4BAEpC,OAAO;gCAAE,OAAO,GAAG,AAAC,eAAe,aAAc,IAAI,CAAC,CAAC;4BAAC;;;;;;;;;;;;;;;;;0BAM9D,6LAAC;gBAAI,WAAU;0BACZ,iBAAiB,GAAG,CAAC,CAAC,CAAC,WAAW,OAAO,EAAE,sBAC1C,6LAAC,OAAO,GAAG;wBAET,SAAS;4BAAE,SAAS;4BAAG,GAAG,CAAC;wBAAG;wBAC9B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO,QAAQ;wBAAK;wBAClC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2DACA,eAAe;;0CAGjB,6LAAC;gCAAI,WAAU;;oCACZ,cAAc;kDACf,6LAAC;wCAAK,WAAU;kDACb,UAAU,OAAO,CAAC,MAAM;;;;;;;;;;;;0CAI7B,6LAAC;gCAAK,WAAU;0CACb,SAAS,YAAY;;;;;;;uBAjBnB;;;;;;;;;;0BAwBX,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;gCAAK;gCAAe,CAAA,GAAA,sHAAA,CAAA,kBAAe,AAAD,EAAE,WAAW,SAAS;;;;;;;sCACzD,6LAAC;sCAAM,WAAW,OAAO;;;;;;;;;;;;;;;;;;;;;;;AAKnC;KA9HgB", "debugId": null}}, {"offset": {"line": 1701, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Sherwyn/Project%20Repos/AI%20Projects%20/PrecatchingAnalyzer/frontend/src/components/Dashboard.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { Bar<PERSON>hart3, Pie<PERSON>hart, TrendingUp, Clock, Users, Database } from 'lucide-react';\nimport { QueryResponse, HealthResponse } from '@/types';\nimport { RecommendationCard } from './RecommendationCard';\nimport { ChartContainer } from './charts/ChartContainer';\nimport { TTLChart } from './charts/TTLChart';\nimport { RiskDistributionChart } from './charts/RiskDistributionChart';\nimport { HealthStatus } from './HealthStatus';\nimport { \n  prepareTTLChartData, \n  prepareRiskDistributionData, \n  formatProcessingTime, \n  formatConfidence,\n  formatDataSources,\n  sortRecommendationsByPriority\n} from '@/lib/utils';\n\ninterface DashboardProps {\n  queryResponse: QueryResponse | null;\n  healthData: HealthResponse | null;\n  healthLoading: boolean;\n  healthError: string | null;\n}\n\nexport function Dashboard({ queryResponse, healthData, healthLoading, healthError }: DashboardProps) {\n  if (!queryResponse) {\n    return (\n      <div className=\"text-center py-12\">\n        <div className=\"text-gray-400 mb-4\">\n          <BarChart3 className=\"w-16 h-16 mx-auto\" />\n        </div>\n        <h3 className=\"text-lg font-medium text-gray-900 mb-2\">\n          No Analysis Yet\n        </h3>\n        <p className=\"text-gray-600\">\n          Submit a query above to see caching recommendations and analytics\n        </p>\n      </div>\n    );\n  }\n\n  const { recommendations, intent, metadata, summary } = queryResponse;\n  const sortedRecommendations = sortRecommendationsByPriority(recommendations);\n  const ttlChartData = prepareTTLChartData(recommendations);\n  const riskDistributionData = prepareRiskDistributionData(recommendations);\n\n  return (\n    <div className=\"space-y-8\">\n      {/* Query Summary */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        className=\"bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 border border-blue-200\"\n      >\n        <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">Analysis Summary</h2>\n        \n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n          <div className=\"bg-white rounded-lg p-4 border border-blue-100\">\n            <div className=\"flex items-center space-x-2 mb-2\">\n              <TrendingUp className=\"w-5 h-5 text-blue-600\" />\n              <span className=\"text-sm font-medium text-gray-700\">Recommendations</span>\n            </div>\n            <div className=\"text-2xl font-bold text-gray-900\">{summary.total_recommendations}</div>\n          </div>\n          \n          <div className=\"bg-white rounded-lg p-4 border border-blue-100\">\n            <div className=\"flex items-center space-x-2 mb-2\">\n              <Clock className=\"w-5 h-5 text-green-600\" />\n              <span className=\"text-sm font-medium text-gray-700\">Avg TTL</span>\n            </div>\n            <div className=\"text-2xl font-bold text-gray-900\">{summary.avg_ttl_hours}h</div>\n          </div>\n          \n          <div className=\"bg-white rounded-lg p-4 border border-blue-100\">\n            <div className=\"flex items-center space-x-2 mb-2\">\n              <Users className=\"w-5 h-5 text-purple-600\" />\n              <span className=\"text-sm font-medium text-gray-700\">Confidence</span>\n            </div>\n            <div className=\"text-2xl font-bold text-gray-900\">\n              {formatConfidence(summary.confidence_score)}\n            </div>\n          </div>\n          \n          <div className=\"bg-white rounded-lg p-4 border border-blue-100\">\n            <div className=\"flex items-center space-x-2 mb-2\">\n              <Database className=\"w-5 h-5 text-orange-600\" />\n              <span className=\"text-sm font-medium text-gray-700\">Processing</span>\n            </div>\n            <div className=\"text-2xl font-bold text-gray-900\">\n              {formatProcessingTime(summary.processing_time_ms)}\n            </div>\n          </div>\n        </div>\n\n        {/* Intent Analysis */}\n        <div className=\"mt-4 p-4 bg-white rounded-lg border border-blue-100\">\n          <h3 className=\"font-medium text-gray-900 mb-2\">Query Analysis</h3>\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm\">\n            <div>\n              <span className=\"text-gray-600\">Type:</span>\n              <span className=\"ml-2 font-medium capitalize\">{intent.type}</span>\n            </div>\n            <div>\n              <span className=\"text-gray-600\">Locations:</span>\n              <span className=\"ml-2 font-medium\">{intent.locations.join(', ') || 'None'}</span>\n            </div>\n            <div>\n              <span className=\"text-gray-600\">Data Sources:</span>\n              <span className=\"ml-2 font-medium\">{formatDataSources(summary.data_sources)}</span>\n            </div>\n          </div>\n        </div>\n      </motion.div>\n\n      {/* Charts Section */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        <ChartContainer \n          title=\"TTL Distribution\" \n          description=\"Cache time-to-live recommendations by entity\"\n        >\n          <TTLChart data={ttlChartData} />\n        </ChartContainer>\n        \n        <ChartContainer \n          title=\"Risk Assessment\" \n          description=\"Distribution of staleness risk levels\"\n        >\n          <RiskDistributionChart data={riskDistributionData} />\n        </ChartContainer>\n      </div>\n\n      {/* Health Status and Recommendations */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n        {/* Health Status */}\n        <div className=\"lg:col-span-1\">\n          <HealthStatus \n            healthData={healthData}\n            isLoading={healthLoading}\n            error={healthError}\n          />\n        </div>\n\n        {/* Risk Summary */}\n        <div className=\"lg:col-span-2\">\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            className=\"bg-white rounded-lg border border-gray-200 p-6 shadow-sm\"\n          >\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Risk Summary</h3>\n            \n            <div className=\"grid grid-cols-3 gap-4\">\n              <div className=\"text-center p-4 bg-red-50 rounded-lg border border-red-200\">\n                <div className=\"text-2xl font-bold text-red-600\">{summary.high_risk_count}</div>\n                <div className=\"text-sm text-red-700\">High Risk</div>\n              </div>\n              \n              <div className=\"text-center p-4 bg-yellow-50 rounded-lg border border-yellow-200\">\n                <div className=\"text-2xl font-bold text-yellow-600\">{summary.medium_risk_count}</div>\n                <div className=\"text-sm text-yellow-700\">Medium Risk</div>\n              </div>\n              \n              <div className=\"text-center p-4 bg-green-50 rounded-lg border border-green-200\">\n                <div className=\"text-2xl font-bold text-green-600\">{summary.low_risk_count}</div>\n                <div className=\"text-sm text-green-700\">Low Risk</div>\n              </div>\n            </div>\n          </motion.div>\n        </div>\n      </div>\n\n      {/* Recommendations List */}\n      <div>\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          className=\"mb-6\"\n        >\n          <h2 className=\"text-xl font-semibold text-gray-900 mb-2\">\n            Caching Recommendations\n          </h2>\n          <p className=\"text-gray-600\">\n            {recommendations.length} recommendations sorted by priority\n          </p>\n        </motion.div>\n\n        <div className=\"grid gap-6\">\n          {sortedRecommendations.map((recommendation, index) => (\n            <RecommendationCard\n              key={`${recommendation.entity_type}-${recommendation.entity_id_or_name}-${index}`}\n              recommendation={recommendation}\n              index={index}\n            />\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAKA;AACA;AACA;AACA;AACA;AACA;AAVA;;;;;;;;;;AA0BO,SAAS,UAAU,EAAE,aAAa,EAAE,UAAU,EAAE,aAAa,EAAE,WAAW,EAAkB;IACjG,IAAI,CAAC,eAAe;QAClB,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAU,WAAU;;;;;;;;;;;8BAEvB,6LAAC;oBAAG,WAAU;8BAAyC;;;;;;8BAGvD,6LAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;;IAKnC;IAEA,MAAM,EAAE,eAAe,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG;IACvD,MAAM,wBAAwB,CAAA,GAAA,sHAAA,CAAA,gCAA6B,AAAD,EAAE;IAC5D,MAAM,eAAe,CAAA,GAAA,sHAAA,CAAA,sBAAmB,AAAD,EAAE;IACzC,MAAM,uBAAuB,CAAA,GAAA,sHAAA,CAAA,8BAA2B,AAAD,EAAE;IAEzD,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,OAAO,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,WAAU;;kCAEV,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCAEzD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAW,WAAU;;;;;;0DACtB,6LAAC;gDAAK,WAAU;0DAAoC;;;;;;;;;;;;kDAEtD,6LAAC;wCAAI,WAAU;kDAAoC,QAAQ,qBAAqB;;;;;;;;;;;;0CAGlF,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,WAAU;;;;;;0DACjB,6LAAC;gDAAK,WAAU;0DAAoC;;;;;;;;;;;;kDAEtD,6LAAC;wCAAI,WAAU;;4CAAoC,QAAQ,aAAa;4CAAC;;;;;;;;;;;;;0CAG3E,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,WAAU;;;;;;0DACjB,6LAAC;gDAAK,WAAU;0DAAoC;;;;;;;;;;;;kDAEtD,6LAAC;wCAAI,WAAU;kDACZ,CAAA,GAAA,sHAAA,CAAA,mBAAgB,AAAD,EAAE,QAAQ,gBAAgB;;;;;;;;;;;;0CAI9C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAS,WAAU;;;;;;0DACpB,6LAAC;gDAAK,WAAU;0DAAoC;;;;;;;;;;;;kDAEtD,6LAAC;wCAAI,WAAU;kDACZ,CAAA,GAAA,sHAAA,CAAA,uBAAoB,AAAD,EAAE,QAAQ,kBAAkB;;;;;;;;;;;;;;;;;;kCAMtD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAiC;;;;;;0CAC/C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,6LAAC;gDAAK,WAAU;0DAA+B,OAAO,IAAI;;;;;;;;;;;;kDAE5D,6LAAC;;0DACC,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,6LAAC;gDAAK,WAAU;0DAAoB,OAAO,SAAS,CAAC,IAAI,CAAC,SAAS;;;;;;;;;;;;kDAErE,6LAAC;;0DACC,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,6LAAC;gDAAK,WAAU;0DAAoB,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,QAAQ,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOlF,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,iJAAA,CAAA,iBAAc;wBACb,OAAM;wBACN,aAAY;kCAEZ,cAAA,6LAAC,2IAAA,CAAA,WAAQ;4BAAC,MAAM;;;;;;;;;;;kCAGlB,6LAAC,iJAAA,CAAA,iBAAc;wBACb,OAAM;wBACN,aAAY;kCAEZ,cAAA,6LAAC,wJAAA,CAAA,wBAAqB;4BAAC,MAAM;;;;;;;;;;;;;;;;;0BAKjC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,qIAAA,CAAA,eAAY;4BACX,YAAY;4BACZ,WAAW;4BACX,OAAO;;;;;;;;;;;kCAKX,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,OAAO,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,WAAU;;8CAEV,6LAAC;oCAAG,WAAU;8CAA2C;;;;;;8CAEzD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAmC,QAAQ,eAAe;;;;;;8DACzE,6LAAC;oDAAI,WAAU;8DAAuB;;;;;;;;;;;;sDAGxC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAsC,QAAQ,iBAAiB;;;;;;8DAC9E,6LAAC;oDAAI,WAAU;8DAA0B;;;;;;;;;;;;sDAG3C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAqC,QAAQ,cAAc;;;;;;8DAC1E,6LAAC;oDAAI,WAAU;8DAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQlD,6LAAC;;kCACC,6LAAC,OAAO,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,WAAU;;0CAEV,6LAAC;gCAAG,WAAU;0CAA2C;;;;;;0CAGzD,6LAAC;gCAAE,WAAU;;oCACV,gBAAgB,MAAM;oCAAC;;;;;;;;;;;;;kCAI5B,6LAAC;wBAAI,WAAU;kCACZ,sBAAsB,GAAG,CAAC,CAAC,gBAAgB,sBAC1C,6LAAC,2IAAA,CAAA,qBAAkB;gCAEjB,gBAAgB;gCAChB,OAAO;+BAFF,GAAG,eAAe,WAAW,CAAC,CAAC,EAAE,eAAe,iBAAiB,CAAC,CAAC,EAAE,OAAO;;;;;;;;;;;;;;;;;;;;;;AAS/F;KA9KgB", "debugId": null}}, {"offset": {"line": 2337, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Sher<PERSON>/Project%20Repos/AI%20Projects%20/PrecatchingAnalyzer/frontend/src/lib/api.ts"], "sourcesContent": ["import { QueryResponse, HealthResponse, SampleQueriesResponse } from '@/types';\n\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000';\n\nclass ApiClient {\n  private baseUrl: string;\n\n  constructor(baseUrl: string = API_BASE_URL) {\n    this.baseUrl = baseUrl;\n  }\n\n  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {\n    const url = `${this.baseUrl}${endpoint}`;\n    \n    const config: RequestInit = {\n      headers: {\n        'Content-Type': 'application/json',\n        ...options.headers,\n      },\n      ...options,\n    };\n\n    try {\n      const response = await fetch(url, config);\n      \n      if (!response.ok) {\n        const errorData = await response.json().catch(() => ({}));\n        throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);\n      }\n\n      return await response.json();\n    } catch (error) {\n      if (error instanceof Error) {\n        throw error;\n      }\n      throw new Error('An unexpected error occurred');\n    }\n  }\n\n  /**\n   * Submit a natural language query for caching analysis\n   */\n  async query(query: string): Promise<QueryResponse> {\n    return this.request<QueryResponse>('/api/v1/query', {\n      method: 'POST',\n      body: JSON.stringify({ query }),\n    });\n  }\n\n  /**\n   * Get system health status\n   */\n  async health(): Promise<HealthResponse> {\n    return this.request<HealthResponse>('/api/v1/health');\n  }\n\n  /**\n   * Get sample queries for testing\n   */\n  async sampleQueries(): Promise<SampleQueriesResponse> {\n    return this.request<SampleQueriesResponse>('/api/v1/sample-queries');\n  }\n\n  /**\n   * Legacy analyze endpoint (for JSON input)\n   */\n  async analyze(data: any): Promise<any> {\n    return this.request('/api/v1/analyze', {\n      method: 'POST',\n      body: JSON.stringify(data),\n    });\n  }\n\n  /**\n   * Get sample input data\n   */\n  async sampleInput(): Promise<any> {\n    return this.request('/api/v1/sample-input');\n  }\n}\n\n// Create singleton instance\nexport const apiClient = new ApiClient();\n\n// Export for testing or custom instances\nexport { ApiClient };\n"], "names": [], "mappings": ";;;;AAEqB;AAArB,MAAM,eAAe,6DAAmC;AAExD,MAAM;IACI,QAAgB;IAExB,YAAY,UAAkB,YAAY,CAAE;QAC1C,IAAI,CAAC,OAAO,GAAG;IACjB;IAEA,MAAc,QAAW,QAAgB,EAAE,UAAuB,CAAC,CAAC,EAAc;QAChF,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG,UAAU;QAExC,MAAM,SAAsB;YAC1B,SAAS;gBACP,gBAAgB;gBAChB,GAAG,QAAQ,OAAO;YACpB;YACA,GAAG,OAAO;QACZ;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,KAAK;YAElC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;gBACvD,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,EAAE;YACxF;YAEA,OAAO,MAAM,SAAS,IAAI;QAC5B,EAAE,OAAO,OAAO;YACd,IAAI,iBAAiB,OAAO;gBAC1B,MAAM;YACR;YACA,MAAM,IAAI,MAAM;QAClB;IACF;IAEA;;GAEC,GACD,MAAM,MAAM,KAAa,EAA0B;QACjD,OAAO,IAAI,CAAC,OAAO,CAAgB,iBAAiB;YAClD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;gBAAE;YAAM;QAC/B;IACF;IAEA;;GAEC,GACD,MAAM,SAAkC;QACtC,OAAO,IAAI,CAAC,OAAO,CAAiB;IACtC;IAEA;;GAEC,GACD,MAAM,gBAAgD;QACpD,OAAO,IAAI,CAAC,OAAO,CAAwB;IAC7C;IAEA;;GAEC,GACD,MAAM,QAAQ,IAAS,EAAgB;QACrC,OAAO,IAAI,CAAC,OAAO,CAAC,mBAAmB;YACrC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA;;GAEC,GACD,MAAM,cAA4B;QAChC,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB;AACF;AAGO,MAAM,YAAY,IAAI", "debugId": null}}, {"offset": {"line": 2416, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Sherwyn/Project%20Repos/AI%20Projects%20/PrecatchingAnalyzer/frontend/src/hooks/useApi.ts"], "sourcesContent": ["'use client';\n\nimport { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';\nimport { apiClient } from '@/lib/api';\nimport { QueryResponse, HealthResponse, SampleQueriesResponse } from '@/types';\n\n// Query Keys\nexport const queryKeys = {\n  health: ['health'] as const,\n  sampleQueries: ['sample-queries'] as const,\n  query: (query: string) => ['query', query] as const,\n};\n\n/**\n * Hook for submitting natural language queries\n */\nexport function useSubmitQuery() {\n  const queryClient = useQueryClient();\n\n  return useMutation({\n    mutationFn: (query: string): Promise<QueryResponse> => apiClient.query(query),\n    onSuccess: (data, query) => {\n      // Cache the successful query result\n      queryClient.setQueryData(queryKeys.query(query), data);\n    },\n    onError: (error) => {\n      console.error('Query submission failed:', error);\n    },\n  });\n}\n\n/**\n * Hook for fetching system health status\n */\nexport function useHealthStatus() {\n  return useQuery({\n    queryKey: queryKeys.health,\n    queryFn: (): Promise<HealthResponse> => apiClient.health(),\n    refetchInterval: 30000, // Refetch every 30 seconds\n    staleTime: 10000, // Consider stale after 10 seconds\n  });\n}\n\n/**\n * Hook for fetching sample queries\n */\nexport function useSampleQueries() {\n  return useQuery({\n    queryKey: queryKeys.sampleQueries,\n    queryFn: (): Promise<SampleQueriesResponse> => apiClient.sampleQueries(),\n    staleTime: 60 * 60 * 1000, // 1 hour - sample queries don't change often\n  });\n}\n\n/**\n * Hook for getting a cached query result\n */\nexport function useCachedQuery(query: string) {\n  return useQuery({\n    queryKey: queryKeys.query(query),\n    queryFn: (): Promise<QueryResponse> => apiClient.query(query),\n    enabled: false, // Don't auto-fetch, only use cached data\n    staleTime: 5 * 60 * 1000, // 5 minutes\n  });\n}\n\n/**\n * Hook for prefetching health status\n */\nexport function usePrefetchHealth() {\n  const queryClient = useQueryClient();\n\n  return () => {\n    queryClient.prefetchQuery({\n      queryKey: queryKeys.health,\n      queryFn: () => apiClient.health(),\n      staleTime: 10000,\n    });\n  };\n}\n\n/**\n * Hook for invalidating all queries (useful for refresh)\n */\nexport function useRefreshAll() {\n  const queryClient = useQueryClient();\n\n  return () => {\n    queryClient.invalidateQueries();\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAGA;;AAHA;;;AAOO,MAAM,YAAY;IACvB,QAAQ;QAAC;KAAS;IAClB,eAAe;QAAC;KAAiB;IACjC,OAAO,CAAC,QAAkB;YAAC;YAAS;SAAM;AAC5C;AAKO,SAAS;;IACd,MAAM,cAAc;IAEpB,OAAO,YAAY;QACjB,UAAU;0CAAE,CAAC,QAA0C,oHAAA,CAAA,YAAS,CAAC,KAAK,CAAC;;QACvE,SAAS;0CAAE,CAAC,MAAM;gBAChB,oCAAoC;gBACpC,YAAY,YAAY,CAAC,UAAU,KAAK,CAAC,QAAQ;YACnD;;QACA,OAAO;0CAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,4BAA4B;YAC5C;;IACF;AACF;GAbgB;;QACM;QAEb;;;AAeF,SAAS;;IACd,OAAO,SAAS;QACd,UAAU,UAAU,MAAM;QAC1B,OAAO;wCAAE,IAA+B,oHAAA,CAAA,YAAS,CAAC,MAAM;;QACxD,iBAAiB;QACjB,WAAW;IACb;AACF;IAPgB;;QACP;;;AAWF,SAAS;;IACd,OAAO,SAAS;QACd,UAAU,UAAU,aAAa;QACjC,OAAO;yCAAE,IAAsC,oHAAA,CAAA,YAAS,CAAC,aAAa;;QACtE,WAAW,KAAK,KAAK;IACvB;AACF;IANgB;;QACP;;;AAUF,SAAS,eAAe,KAAa;;IAC1C,OAAO,SAAS;QACd,UAAU,UAAU,KAAK,CAAC;QAC1B,OAAO;uCAAE,IAA8B,oHAAA,CAAA,YAAS,CAAC,KAAK,CAAC;;QACvD,SAAS;QACT,WAAW,IAAI,KAAK;IACtB;AACF;IAPgB;;QACP;;;AAWF,SAAS;;IACd,MAAM,cAAc;IAEpB,OAAO;QACL,YAAY,aAAa,CAAC;YACxB,UAAU,UAAU,MAAM;YAC1B,SAAS,IAAM,oHAAA,CAAA,YAAS,CAAC,MAAM;YAC/B,WAAW;QACb;IACF;AACF;IAVgB;;QACM;;;AAcf,SAAS;;IACd,MAAM,cAAc;IAEpB,OAAO;QACL,YAAY,iBAAiB;IAC/B;AACF;IANgB;;QACM", "debugId": null}}, {"offset": {"line": 2557, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Sherwyn/Project%20Repos/AI%20Projects%20/PrecatchingAnalyzer/frontend/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { QueryInterface } from '@/components/QueryInterface';\nimport { Dashboard } from '@/components/Dashboard';\nimport { useSubmitQuery, useHealthStatus } from '@/hooks/useApi';\nimport { QueryResponse } from '@/types';\n\nexport default function Home() {\n  const [currentQuery, setCurrentQuery] = useState<string>('');\n  const [queryResponse, setQueryResponse] = useState<QueryResponse | null>(null);\n\n  const submitQueryMutation = useSubmitQuery();\n  const { data: healthData, isLoading: healthLoading, error: healthError } = useHealthStatus();\n\n  const handleQuerySubmit = async (query: string) => {\n    setCurrentQuery(query);\n    try {\n      const response = await submitQueryMutation.mutateAsync(query);\n      setQueryResponse(response);\n    } catch (error) {\n      console.error('Query failed:', error);\n      // Error is handled by the mutation's onError callback\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <div className=\"container mx-auto px-4 py-8\">\n        {/* Query Interface */}\n        <div className=\"mb-12\">\n          <QueryInterface\n            onQuerySubmit={handleQuerySubmit}\n            isLoading={submitQueryMutation.isPending}\n          />\n        </div>\n\n        {/* Error Display */}\n        {submitQueryMutation.error && (\n          <div className=\"mb-8 max-w-4xl mx-auto\">\n            <div className=\"bg-red-50 border border-red-200 rounded-lg p-4\">\n              <h3 className=\"text-red-800 font-medium mb-2\">Query Failed</h3>\n              <p className=\"text-red-700 text-sm\">\n                {submitQueryMutation.error instanceof Error\n                  ? submitQueryMutation.error.message\n                  : 'An unexpected error occurred'}\n              </p>\n            </div>\n          </div>\n        )}\n\n        {/* Dashboard */}\n        <div className=\"max-w-7xl mx-auto\">\n          <Dashboard\n            queryResponse={queryResponse}\n            healthData={healthData || null}\n            healthLoading={healthLoading}\n            healthError={healthError instanceof Error ? healthError.message : null}\n          />\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAQe,SAAS;;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACzD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB;IAEzE,MAAM,sBAAsB,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD;IACzC,MAAM,EAAE,MAAM,UAAU,EAAE,WAAW,aAAa,EAAE,OAAO,WAAW,EAAE,GAAG,CAAA,GAAA,yHAAA,CAAA,kBAAe,AAAD;IAEzF,MAAM,oBAAoB,OAAO;QAC/B,gBAAgB;QAChB,IAAI;YACF,MAAM,WAAW,MAAM,oBAAoB,WAAW,CAAC;YACvD,iBAAiB;QACnB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QAC/B,sDAAsD;QACxD;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,uIAAA,CAAA,iBAAc;wBACb,eAAe;wBACf,WAAW,oBAAoB,SAAS;;;;;;;;;;;gBAK3C,oBAAoB,KAAK,kBACxB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAgC;;;;;;0CAC9C,6LAAC;gCAAE,WAAU;0CACV,oBAAoB,KAAK,YAAY,QAClC,oBAAoB,KAAK,CAAC,OAAO,GACjC;;;;;;;;;;;;;;;;;8BAOZ,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,kIAAA,CAAA,YAAS;wBACR,eAAe;wBACf,YAAY,cAAc;wBAC1B,eAAe;wBACf,aAAa,uBAAuB,QAAQ,YAAY,OAAO,GAAG;;;;;;;;;;;;;;;;;;;;;;AAM9E;GAvDwB;;QAIM,yHAAA,CAAA,iBAAc;QACiC,yHAAA,CAAA,kBAAe;;;KALpE", "debugId": null}}]}