'use client';

import { motion } from 'framer-motion';
import { Clock, AlertTriangle, TrendingUp, Info } from 'lucide-react';
import { CachingRecommendation } from '@/types';
import { cn, getRiskColor, getEntityIcon, formatTTL, truncateText } from '@/lib/utils';

interface RecommendationCardProps {
  recommendation: CachingRecommendation;
  index: number;
}

export function RecommendationCard({ recommendation, index }: RecommendationCardProps) {
  const riskColorClass = getRiskColor(recommendation.staleness_risk);
  const entityIcon = getEntityIcon(recommendation.entity_type);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: index * 0.1, duration: 0.3 }}
      className="bg-white rounded-lg border border-gray-200 p-6 shadow-sm hover:shadow-md transition-shadow duration-200"
    >
      {/* Header */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className="text-2xl">{entityIcon}</div>
          <div>
            <h3 className="font-semibold text-gray-900 text-lg">
              {truncateText(recommendation.entity_id_or_name, 30)}
            </h3>
            <p className="text-sm text-gray-500 capitalize">
              {recommendation.entity_type.replace('_', ' ')}
            </p>
          </div>
        </div>
        
        {/* Risk Badge */}
        <div className={cn(
          'px-3 py-1 rounded-full text-xs font-medium border',
          riskColorClass
        )}>
          <div className="flex items-center space-x-1">
            <AlertTriangle className="w-3 h-3" />
            <span>{recommendation.staleness_risk} Risk</span>
          </div>
        </div>
      </div>

      {/* TTL Information */}
      <div className="flex items-center space-x-4 mb-4">
        <div className="flex items-center space-x-2 text-blue-600">
          <Clock className="w-4 h-4" />
          <span className="font-medium">
            TTL: {formatTTL(recommendation.suggested_ttl_hours)}
          </span>
        </div>
        
        <div className="flex items-center space-x-2 text-green-600">
          <TrendingUp className="w-4 h-4" />
          <span className="text-sm">
            {recommendation.suggested_ttl_hours}h cache
          </span>
        </div>
      </div>

      {/* Reasoning */}
      <div className="bg-gray-50 rounded-lg p-4">
        <div className="flex items-start space-x-2">
          <Info className="w-4 h-4 text-gray-400 mt-0.5 flex-shrink-0" />
          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-1">Reasoning</h4>
            <p className="text-sm text-gray-600 leading-relaxed">
              {recommendation.reasoning}
            </p>
          </div>
        </div>
      </div>

      {/* Priority Indicator */}
      <div className="mt-4 flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <div className={cn(
            'w-2 h-2 rounded-full',
            recommendation.staleness_risk === 'High' ? 'bg-red-500' :
            recommendation.staleness_risk === 'Medium' ? 'bg-yellow-500' : 'bg-green-500'
          )} />
          <span className="text-xs text-gray-500">
            Priority: {recommendation.staleness_risk}
          </span>
        </div>
        
        <div className="text-xs text-gray-400">
          #{index + 1}
        </div>
      </div>
    </motion.div>
  );
}
