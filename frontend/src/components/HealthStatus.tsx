'use client';

import { motion } from 'framer-motion';
import { CheckCircle, XCircle, AlertCircle, Activity } from 'lucide-react';
import { HealthResponse } from '@/types';
import { cn, getRelativeTime } from '@/lib/utils';

interface HealthStatusProps {
  healthData: HealthResponse | null;
  isLoading: boolean;
  error: string | null;
}

const getStatusIcon = (status: boolean) => {
  return status ? (
    <CheckCircle className="w-4 h-4 text-green-500" />
  ) : (
    <XCircle className="w-4 h-4 text-red-500" />
  );
};

const getStatusColor = (status: boolean) => {
  return status 
    ? 'text-green-700 bg-green-50 border-green-200' 
    : 'text-red-700 bg-red-50 border-red-200';
};

export function HealthStatus({ healthData, isLoading, error }: HealthStatusProps) {
  if (isLoading) {
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className="bg-white rounded-lg border border-gray-200 p-6 shadow-sm"
      >
        <div className="flex items-center space-x-2 mb-4">
          <Activity className="w-5 h-5 text-blue-600 animate-pulse" />
          <h3 className="text-lg font-semibold text-gray-900">System Health</h3>
        </div>
        <div className="text-gray-500">Checking system health...</div>
      </motion.div>
    );
  }

  if (error) {
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className="bg-white rounded-lg border border-red-200 p-6 shadow-sm"
      >
        <div className="flex items-center space-x-2 mb-4">
          <AlertCircle className="w-5 h-5 text-red-600" />
          <h3 className="text-lg font-semibold text-gray-900">System Health</h3>
        </div>
        <div className="text-red-600 text-sm">
          Failed to fetch health status: {error}
        </div>
      </motion.div>
    );
  }

  if (!healthData) {
    return null;
  }

  const components = healthData.components || {};
  const componentEntries = Object.entries(components);
  const healthyCount = componentEntries.filter(([, status]) => status).length;
  const totalCount = componentEntries.length;
  const overallHealthy = healthyCount === totalCount;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white rounded-lg border border-gray-200 p-6 shadow-sm"
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          <Activity className="w-5 h-5 text-blue-600" />
          <h3 className="text-lg font-semibold text-gray-900">System Health</h3>
        </div>
        
        <div className={cn(
          'px-3 py-1 rounded-full text-xs font-medium border',
          overallHealthy 
            ? 'text-green-700 bg-green-50 border-green-200'
            : 'text-red-700 bg-red-50 border-red-200'
        )}>
          {overallHealthy ? 'All Systems Operational' : 'Issues Detected'}
        </div>
      </div>

      {/* Overall Status */}
      <div className="mb-4 p-3 bg-gray-50 rounded-lg">
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium text-gray-700">
            Components Status
          </span>
          <span className="text-sm text-gray-600">
            {healthyCount}/{totalCount} healthy
          </span>
        </div>
        
        <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
          <div 
            className={cn(
              'h-2 rounded-full transition-all duration-300',
              overallHealthy ? 'bg-green-500' : 'bg-red-500'
            )}
            style={{ width: `${(healthyCount / totalCount) * 100}%` }}
          />
        </div>
      </div>

      {/* Component Details */}
      <div className="space-y-2">
        {componentEntries.map(([component, status], index) => (
          <motion.div
            key={component}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: index * 0.05 }}
            className={cn(
              'flex items-center justify-between p-3 rounded-lg border',
              getStatusColor(status)
            )}
          >
            <div className="flex items-center space-x-3">
              {getStatusIcon(status)}
              <span className="font-medium capitalize">
                {component.replace(/_/g, ' ')}
              </span>
            </div>
            
            <span className="text-xs font-medium">
              {status ? 'Healthy' : 'Unhealthy'}
            </span>
          </motion.div>
        ))}
      </div>

      {/* Last Updated */}
      <div className="mt-4 pt-4 border-t border-gray-200">
        <div className="flex items-center justify-between text-xs text-gray-500">
          <span>Last updated: {getRelativeTime(healthData.timestamp)}</span>
          <span>{healthData.service}</span>
        </div>
      </div>
    </motion.div>
  );
}
