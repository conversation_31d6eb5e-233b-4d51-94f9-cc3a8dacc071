'use client';

import { motion } from 'framer-motion';
import { Bar<PERSON>hart3, Pie<PERSON>hart, TrendingUp, Clock, Users, Database } from 'lucide-react';
import { QueryResponse, HealthResponse } from '@/types';
import { RecommendationCard } from './RecommendationCard';
import { ChartContainer } from './charts/ChartContainer';
import { TTLChart } from './charts/TTLChart';
import { RiskDistributionChart } from './charts/RiskDistributionChart';
import { HealthStatus } from './HealthStatus';
import { 
  prepareTTLChartData, 
  prepareRiskDistributionData, 
  formatProcessingTime, 
  formatConfidence,
  formatDataSources,
  sortRecommendationsByPriority
} from '@/lib/utils';

interface DashboardProps {
  queryResponse: QueryResponse | null;
  healthData: HealthResponse | null;
  healthLoading: boolean;
  healthError: string | null;
}

export function Dashboard({ queryResponse, healthData, healthLoading, healthError }: DashboardProps) {
  if (!queryResponse) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-400 mb-4">
          <BarChart3 className="w-16 h-16 mx-auto" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          No Analysis Yet
        </h3>
        <p className="text-gray-600">
          Submit a query above to see caching recommendations and analytics
        </p>
      </div>
    );
  }

  const { recommendations, intent, metadata, summary } = queryResponse;
  const sortedRecommendations = sortRecommendationsByPriority(recommendations);
  const ttlChartData = prepareTTLChartData(recommendations);
  const riskDistributionData = prepareRiskDistributionData(recommendations);

  return (
    <div className="space-y-8">
      {/* Query Summary */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 border border-blue-200"
      >
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Analysis Summary</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="bg-white rounded-lg p-4 border border-blue-100">
            <div className="flex items-center space-x-2 mb-2">
              <TrendingUp className="w-5 h-5 text-blue-600" />
              <span className="text-sm font-medium text-gray-700">Recommendations</span>
            </div>
            <div className="text-2xl font-bold text-gray-900">{summary.total_recommendations}</div>
          </div>
          
          <div className="bg-white rounded-lg p-4 border border-blue-100">
            <div className="flex items-center space-x-2 mb-2">
              <Clock className="w-5 h-5 text-green-600" />
              <span className="text-sm font-medium text-gray-700">Avg TTL</span>
            </div>
            <div className="text-2xl font-bold text-gray-900">{summary.avg_ttl_hours}h</div>
          </div>
          
          <div className="bg-white rounded-lg p-4 border border-blue-100">
            <div className="flex items-center space-x-2 mb-2">
              <Users className="w-5 h-5 text-purple-600" />
              <span className="text-sm font-medium text-gray-700">Confidence</span>
            </div>
            <div className="text-2xl font-bold text-gray-900">
              {formatConfidence(summary.confidence_score)}
            </div>
          </div>
          
          <div className="bg-white rounded-lg p-4 border border-blue-100">
            <div className="flex items-center space-x-2 mb-2">
              <Database className="w-5 h-5 text-orange-600" />
              <span className="text-sm font-medium text-gray-700">Processing</span>
            </div>
            <div className="text-2xl font-bold text-gray-900">
              {formatProcessingTime(summary.processing_time_ms)}
            </div>
          </div>
        </div>

        {/* Intent Analysis */}
        <div className="mt-4 p-4 bg-white rounded-lg border border-blue-100">
          <h3 className="font-medium text-gray-900 mb-2">Query Analysis</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
              <span className="text-gray-600">Type:</span>
              <span className="ml-2 font-medium capitalize">{intent.type}</span>
            </div>
            <div>
              <span className="text-gray-600">Locations:</span>
              <span className="ml-2 font-medium">{intent.locations.join(', ') || 'None'}</span>
            </div>
            <div>
              <span className="text-gray-600">Data Sources:</span>
              <span className="ml-2 font-medium">{formatDataSources(summary.data_sources)}</span>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <ChartContainer 
          title="TTL Distribution" 
          description="Cache time-to-live recommendations by entity"
        >
          <TTLChart data={ttlChartData} />
        </ChartContainer>
        
        <ChartContainer 
          title="Risk Assessment" 
          description="Distribution of staleness risk levels"
        >
          <RiskDistributionChart data={riskDistributionData} />
        </ChartContainer>
      </div>

      {/* Health Status and Recommendations */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Health Status */}
        <div className="lg:col-span-1">
          <HealthStatus 
            healthData={healthData}
            isLoading={healthLoading}
            error={healthError}
          />
        </div>

        {/* Risk Summary */}
        <div className="lg:col-span-2">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white rounded-lg border border-gray-200 p-6 shadow-sm"
          >
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Risk Summary</h3>
            
            <div className="grid grid-cols-3 gap-4">
              <div className="text-center p-4 bg-red-50 rounded-lg border border-red-200">
                <div className="text-2xl font-bold text-red-600">{summary.high_risk_count}</div>
                <div className="text-sm text-red-700">High Risk</div>
              </div>
              
              <div className="text-center p-4 bg-yellow-50 rounded-lg border border-yellow-200">
                <div className="text-2xl font-bold text-yellow-600">{summary.medium_risk_count}</div>
                <div className="text-sm text-yellow-700">Medium Risk</div>
              </div>
              
              <div className="text-center p-4 bg-green-50 rounded-lg border border-green-200">
                <div className="text-2xl font-bold text-green-600">{summary.low_risk_count}</div>
                <div className="text-sm text-green-700">Low Risk</div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Recommendations List */}
      <div>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-6"
        >
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Caching Recommendations
          </h2>
          <p className="text-gray-600">
            {recommendations.length} recommendations sorted by priority
          </p>
        </motion.div>

        <div className="grid gap-6">
          {sortedRecommendations.map((recommendation, index) => (
            <RecommendationCard
              key={`${recommendation.entity_type}-${recommendation.entity_id_or_name}-${index}`}
              recommendation={recommendation}
              index={index}
            />
          ))}
        </div>
      </div>
    </div>
  );
}
