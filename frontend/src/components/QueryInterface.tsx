'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { Send, Loader2, <PERSON>bulb, Sparkles } from 'lucide-react';
import { cn, validateQuery, generateSampleQueries } from '@/lib/utils';

interface QueryInterfaceProps {
  onQuerySubmit: (query: string) => void;
  isLoading: boolean;
}

export function QueryInterface({ onQuerySubmit, isLoading }: QueryInterfaceProps) {
  const [query, setQuery] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [showSuggestions, setShowSuggestions] = useState(false);

  const sampleQueries = generateSampleQueries();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const validation = validateQuery(query);
    if (!validation.isValid) {
      setError(validation.error || 'Invalid query');
      return;
    }

    setError(null);
    onQuerySubmit(query);
  };

  const handleSampleQuery = (sampleQuery: string) => {
    setQuery(sampleQuery);
    setShowSuggestions(false);
    setError(null);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setQuery(e.target.value);
    if (error) setError(null);
  };

  return (
    <div className="w-full max-w-4xl mx-auto">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center mb-8"
      >
        <div className="flex items-center justify-center space-x-2 mb-4">
          <Sparkles className="w-8 h-8 text-blue-600" />
          <h1 className="text-3xl font-bold text-gray-900">
            Travel Caching Strategist
          </h1>
        </div>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          Ask me anything about travel content caching strategies. I'll analyze your query using 
          RAG + MCP and provide intelligent recommendations.
        </p>
      </motion.div>

      {/* Query Form */}
      <motion.form
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        onSubmit={handleSubmit}
        className="space-y-4"
      >
        <div className="relative">
          <textarea
            value={query}
            onChange={handleInputChange}
            placeholder="What should I cache for flights from Mumbai to Delhi next week?"
            className={cn(
              'w-full p-4 pr-12 border rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors',
              'min-h-[120px] text-lg',
              error ? 'border-red-300 bg-red-50' : 'border-gray-300 bg-white'
            )}
            disabled={isLoading}
          />
          
          {/* Submit Button */}
          <button
            type="submit"
            disabled={isLoading || !query.trim()}
            className={cn(
              'absolute bottom-4 right-4 p-2 rounded-lg transition-colors',
              'disabled:opacity-50 disabled:cursor-not-allowed',
              query.trim() && !isLoading
                ? 'bg-blue-600 text-white hover:bg-blue-700'
                : 'bg-gray-200 text-gray-400'
            )}
          >
            {isLoading ? (
              <Loader2 className="w-5 h-5 animate-spin" />
            ) : (
              <Send className="w-5 h-5" />
            )}
          </button>
        </div>

        {/* Error Message */}
        {error && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            className="text-red-600 text-sm bg-red-50 p-3 rounded-lg border border-red-200"
          >
            {error}
          </motion.div>
        )}

        {/* Character Count */}
        <div className="flex justify-between items-center text-sm text-gray-500">
          <span>{query.length}/500 characters</span>
          <button
            type="button"
            onClick={() => setShowSuggestions(!showSuggestions)}
            className="flex items-center space-x-1 text-blue-600 hover:text-blue-700 transition-colors"
          >
            <Lightbulb className="w-4 h-4" />
            <span>Need inspiration?</span>
          </button>
        </div>
      </motion.form>

      {/* Sample Queries */}
      {showSuggestions && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          className="mt-6 space-y-3"
        >
          <h3 className="text-sm font-medium text-gray-700 flex items-center space-x-2">
            <Lightbulb className="w-4 h-4" />
            <span>Try these sample queries:</span>
          </h3>
          
          <div className="grid gap-2">
            {sampleQueries.map((sampleQuery, index) => (
              <motion.button
                key={index}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.05 }}
                onClick={() => handleSampleQuery(sampleQuery)}
                className="text-left p-3 bg-gray-50 hover:bg-gray-100 rounded-lg border border-gray-200 transition-colors text-sm"
                disabled={isLoading}
              >
                <span className="text-gray-700">{sampleQuery}</span>
              </motion.button>
            ))}
          </div>
        </motion.div>
      )}

      {/* Loading State */}
      {isLoading && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="mt-8 text-center"
        >
          <div className="inline-flex items-center space-x-3 text-blue-600">
            <Loader2 className="w-6 h-6 animate-spin" />
            <span className="text-lg font-medium">Analyzing your query...</span>
          </div>
          <p className="text-gray-500 mt-2">
            Using RAG + MCP to gather context and generate recommendations
          </p>
        </motion.div>
      )}
    </div>
  );
}
