import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';
import { CachingRecommendation, TTLChartData, RiskDistributionData } from '@/types';

/**
 * Utility function to merge Tailwind CSS classes
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Format processing time in a human-readable format
 */
export function formatProcessingTime(ms: number): string {
  if (ms < 1000) {
    return `${ms}ms`;
  }
  return `${(ms / 1000).toFixed(1)}s`;
}

/**
 * Format confidence score as percentage
 */
export function formatConfidence(score: number): string {
  return `${Math.round(score * 100)}%`;
}

/**
 * Get color for staleness risk level
 */
export function getRiskColor(risk: 'Low' | 'Medium' | 'High'): string {
  switch (risk) {
    case 'Low':
      return 'text-green-600 bg-green-50 border-green-200';
    case 'Medium':
      return 'text-yellow-600 bg-yellow-50 border-yellow-200';
    case 'High':
      return 'text-red-600 bg-red-50 border-red-200';
    default:
      return 'text-gray-600 bg-gray-50 border-gray-200';
  }
}

/**
 * Get icon for entity type
 */
export function getEntityIcon(entityType: string): string {
  switch (entityType.toLowerCase()) {
    case 'flight_route':
    case 'flight':
      return '✈️';
    case 'hotel_cluster':
    case 'hotel':
      return '🏨';
    case 'experience_group':
    case 'experience':
      return '🎯';
    default:
      return '📊';
  }
}

/**
 * Format TTL hours in a human-readable format
 */
export function formatTTL(hours: number): string {
  if (hours < 1) {
    return `${Math.round(hours * 60)}min`;
  }
  if (hours < 24) {
    return `${hours}h`;
  }
  const days = Math.floor(hours / 24);
  const remainingHours = hours % 24;
  if (remainingHours === 0) {
    return `${days}d`;
  }
  return `${days}d ${remainingHours}h`;
}

/**
 * Convert recommendations to TTL chart data
 */
export function prepareTTLChartData(recommendations: CachingRecommendation[]): TTLChartData[] {
  return recommendations.map((rec, index) => ({
    name: rec.entity_id_or_name.length > 20 
      ? rec.entity_id_or_name.substring(0, 20) + '...' 
      : rec.entity_id_or_name,
    ttl: rec.suggested_ttl_hours,
    risk: rec.staleness_risk,
    type: rec.entity_type,
  }));
}

/**
 * Calculate risk distribution data for pie chart
 */
export function prepareRiskDistributionData(recommendations: CachingRecommendation[]): RiskDistributionData[] {
  const riskCounts = recommendations.reduce((acc, rec) => {
    acc[rec.staleness_risk] = (acc[rec.staleness_risk] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  return [
    { name: 'Low Risk', value: riskCounts.Low || 0, color: '#10b981' },
    { name: 'Medium Risk', value: riskCounts.Medium || 0, color: '#f59e0b' },
    { name: 'High Risk', value: riskCounts.High || 0, color: '#ef4444' },
  ].filter(item => item.value > 0);
}

/**
 * Truncate text to specified length
 */
export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
}

/**
 * Format data sources list
 */
export function formatDataSources(sources: string[]): string {
  if (sources.length === 0) return 'No sources';
  if (sources.length === 1) return sources[0]!;
  if (sources.length === 2) return sources.join(' and ');
  return `${sources.slice(0, -1).join(', ')}, and ${sources[sources.length - 1]}`;
}

/**
 * Get relative time string
 */
export function getRelativeTime(timestamp: string): string {
  const now = new Date();
  const time = new Date(timestamp);
  const diffMs = now.getTime() - time.getTime();
  const diffSeconds = Math.floor(diffMs / 1000);
  const diffMinutes = Math.floor(diffSeconds / 60);
  const diffHours = Math.floor(diffMinutes / 60);

  if (diffSeconds < 60) {
    return 'just now';
  } else if (diffMinutes < 60) {
    return `${diffMinutes}m ago`;
  } else if (diffHours < 24) {
    return `${diffHours}h ago`;
  } else {
    return time.toLocaleDateString();
  }
}

/**
 * Validate query input
 */
export function validateQuery(query: string): { isValid: boolean; error?: string } {
  if (!query.trim()) {
    return { isValid: false, error: 'Query cannot be empty' };
  }
  
  if (query.length < 5) {
    return { isValid: false, error: 'Query is too short (minimum 5 characters)' };
  }
  
  if (query.length > 500) {
    return { isValid: false, error: 'Query is too long (maximum 500 characters)' };
  }
  
  return { isValid: true };
}

/**
 * Generate sample queries based on context
 */
export function generateSampleQueries(): string[] {
  return [
    "What should I cache for flights from Mumbai to Delhi next week?",
    "Cache recommendations for hotels in Goa during Independence Day weekend",
    "Analyze caching needs for Thailand experiences in August",
    "What travel content should I pre-cache for the upcoming festival season?",
    "Cache strategy for Paris attractions and hotels for summer vacation",
    "Pre-cache recommendations for Manali cottages with limited inventory"
  ];
}

/**
 * Sort recommendations by priority
 */
export function sortRecommendationsByPriority(recommendations: CachingRecommendation[]): CachingRecommendation[] {
  return [...recommendations].sort((a, b) => {
    // First sort by risk (High > Medium > Low)
    const riskOrder = { 'High': 3, 'Medium': 2, 'Low': 1 };
    const riskDiff = riskOrder[b.staleness_risk] - riskOrder[a.staleness_risk];
    if (riskDiff !== 0) return riskDiff;
    
    // Then sort by TTL (shorter TTL = higher priority)
    return a.suggested_ttl_hours - b.suggested_ttl_hours;
  });
}
