import { QueryResponse, HealthResponse, SampleQueriesResponse } from '@/types';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000';

class ApiClient {
  private baseUrl: string;

  constructor(baseUrl: string = API_BASE_URL) {
    this.baseUrl = baseUrl;
  }

  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`;
    
    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('An unexpected error occurred');
    }
  }

  /**
   * Submit a natural language query for caching analysis
   */
  async query(query: string): Promise<QueryResponse> {
    return this.request<QueryResponse>('/api/v1/query', {
      method: 'POST',
      body: JSON.stringify({ query }),
    });
  }

  /**
   * Get system health status
   */
  async health(): Promise<HealthResponse> {
    return this.request<HealthResponse>('/api/v1/health');
  }

  /**
   * Get sample queries for testing
   */
  async sampleQueries(): Promise<SampleQueriesResponse> {
    return this.request<SampleQueriesResponse>('/api/v1/sample-queries');
  }

  /**
   * Legacy analyze endpoint (for JSON input)
   */
  async analyze(data: any): Promise<any> {
    return this.request('/api/v1/analyze', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  /**
   * Get sample input data
   */
  async sampleInput(): Promise<any> {
    return this.request('/api/v1/sample-input');
  }
}

// Create singleton instance
export const apiClient = new ApiClient();

// Export for testing or custom instances
export { ApiClient };
