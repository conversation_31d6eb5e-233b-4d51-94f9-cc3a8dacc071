// API Response Types
export interface CachingRecommendation {
  entity_type: string;
  entity_id_or_name: string;
  reasoning: string;
  suggested_ttl_hours: number;
  staleness_risk: 'Low' | 'Medium' | 'High';
}

export interface QueryIntent {
  type: 'flight' | 'hotel' | 'experience' | 'general';
  locations: string[];
  dateRange?: {
    start: string;
    end: string;
  };
  entities: string[];
  confidence: number;
}

export interface AnalysisMetadata {
  processingTime: number;
  dataSourcesUsed: string[];
  confidenceScore: number;
  retrievedDocuments: number;
}

export interface QueryResponse {
  success: boolean;
  timestamp: string;
  query: string;
  intent: QueryIntent;
  recommendations: CachingRecommendation[];
  metadata: AnalysisMetadata;
  summary: {
    total_recommendations: number;
    high_risk_count: number;
    medium_risk_count: number;
    low_risk_count: number;
    avg_ttl_hours: number;
    processing_time_ms: number;
    data_sources: string[];
    confidence_score: number;
  };
}

export interface HealthResponse {
  status: string;
  timestamp: string;
  service: string;
  components: Record<string, boolean>;
}

export interface SampleQuery {
  query: string;
  description: string;
}

export interface SampleQueriesResponse {
  message: string;
  sample_queries: SampleQuery[];
  usage: {
    endpoint: string;
    body: { query: string };
  };
}

// UI State Types
export interface QueryState {
  isLoading: boolean;
  error: string | null;
  data: QueryResponse | null;
}

export interface HealthState {
  isLoading: boolean;
  error: string | null;
  data: HealthResponse | null;
}

// Chart Data Types
export interface TTLChartData {
  name: string;
  ttl: number;
  risk: string;
  type: string;
}

export interface RiskDistributionData {
  name: string;
  value: number;
  color: string;
}

export interface ConfidenceData {
  component: string;
  score: number;
}

// Component Props Types
export interface RecommendationCardProps {
  recommendation: CachingRecommendation;
  index: number;
}

export interface QueryInterfaceProps {
  onQuerySubmit: (query: string) => void;
  isLoading: boolean;
}

export interface DashboardProps {
  queryResponse: QueryResponse | null;
  healthData: HealthResponse | null;
}

export interface ChartContainerProps {
  title: string;
  children: React.ReactNode;
  className?: string;
}

// API Client Types
export interface ApiClient {
  query: (query: string) => Promise<QueryResponse>;
  health: () => Promise<HealthResponse>;
  sampleQueries: () => Promise<SampleQueriesResponse>;
}

// Theme Types
export type Theme = 'light' | 'dark';

export interface ThemeContextType {
  theme: Theme;
  toggleTheme: () => void;
}
