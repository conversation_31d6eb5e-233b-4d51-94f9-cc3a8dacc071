'use client';

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { apiClient } from '@/lib/api';
import { QueryResponse, HealthResponse, SampleQueriesResponse } from '@/types';

// Query Keys
export const queryKeys = {
  health: ['health'] as const,
  sampleQueries: ['sample-queries'] as const,
  query: (query: string) => ['query', query] as const,
};

/**
 * Hook for submitting natural language queries
 */
export function useSubmitQuery() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (query: string): Promise<QueryResponse> => apiClient.query(query),
    onSuccess: (data, query) => {
      // Cache the successful query result
      queryClient.setQueryData(queryKeys.query(query), data);
    },
    onError: (error) => {
      console.error('Query submission failed:', error);
    },
  });
}

/**
 * Hook for fetching system health status
 */
export function useHealthStatus() {
  return useQuery({
    queryKey: queryKeys.health,
    queryFn: (): Promise<HealthResponse> => apiClient.health(),
    refetchInterval: 30000, // Refetch every 30 seconds
    staleTime: 10000, // Consider stale after 10 seconds
  });
}

/**
 * Hook for fetching sample queries
 */
export function useSampleQueries() {
  return useQuery({
    queryKey: queryKeys.sampleQueries,
    queryFn: (): Promise<SampleQueriesResponse> => apiClient.sampleQueries(),
    staleTime: 60 * 60 * 1000, // 1 hour - sample queries don't change often
  });
}

/**
 * Hook for getting a cached query result
 */
export function useCachedQuery(query: string) {
  return useQuery({
    queryKey: queryKeys.query(query),
    queryFn: (): Promise<QueryResponse> => apiClient.query(query),
    enabled: false, // Don't auto-fetch, only use cached data
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Hook for prefetching health status
 */
export function usePrefetchHealth() {
  const queryClient = useQueryClient();

  return () => {
    queryClient.prefetchQuery({
      queryKey: queryKeys.health,
      queryFn: () => apiClient.health(),
      staleTime: 10000,
    });
  };
}

/**
 * Hook for invalidating all queries (useful for refresh)
 */
export function useRefreshAll() {
  const queryClient = useQueryClient();

  return () => {
    queryClient.invalidateQueries();
  };
}
